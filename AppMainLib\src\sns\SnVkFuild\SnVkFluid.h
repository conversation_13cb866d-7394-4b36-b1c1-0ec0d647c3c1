#pragma once

#include "../../irrmmd/SnPhyFluid.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/include/irrArray.h"
#include "../../../UaIrrlicht/include/IHardwareBuffer.h"
#include "../../../UaIrrlicht/include/ITexture.h"

namespace irr
{
namespace scene
{

// Volume rendering parameters (ShaderToy style)
struct FluidVolumeParams {
    // Volume resolution (3D texture dimensions)
    u32 volumeResX = 128;
    u32 volumeResY = 128; 
    u32 volumeResZ = 128;
    
    // Volume bounds in world space
    core::vector3df volumeMin = core::vector3df(-10.0f, -5.0f, -10.0f);
    core::vector3df volumeMax = core::vector3df(10.0f, 15.0f, 10.0f);
    
    // Raymarching parameters
    u32 maxRaymarchSteps = 128;
    f32 rayStepSize = 0.02f;
    f32 densityThreshold = 0.1f;
    
    // Volume effects
    f32 volumeNoiseScale = 2.0f;
    f32 absorptionScale = 1.0f;
    f32 scatteringScale = 0.5f;
    
    // Kernel radius for particle-to-volume conversion
    f32 kernelRadius = 0.2f;
    f32 particleInfluence = 1.0f;
};

// SPH Fluid Parameters (inherited from PhysX)
struct FluidSimParams {
    // PhysX grid dimensions
    u32 gridSizeX = 32;
    u32 gridSizeY = 32;
    u32 gridSizeZ = 32;
    
    // SPH Physics parameters
    f32 particleSpacing = 0.1f;
    f32 restDensity = 1000.0f;
    f32 viscosity = 0.001f;
    f32 surfaceTension = 0.0728f;
    f32 gravity = 9.81f;
    f32 timeStep = 0.001f;
    
    // Pressure parameters
    f32 gasConstant = 2000.0f;
    f32 pressureRadius = 0.085f;
    
    // Rendering parameters
    f32 particleRadius = 0.08f;
    f32 normalSmoothness = 0.72f;
    
    // Container bounds
    core::vector3df containerSize = core::vector3df(10.0f, 10.0f, 10.0f);
    core::vector3df containerPos = core::vector3df(0.0f, 0.0f, 0.0f);
    
    // Initial fluid setup
    u32 numParticlesX = 20;
    u32 numParticlesY = 20;
    u32 numParticlesZ = 20;
    core::vector3df initialFluidPos = core::vector3df(0.0f, 5.0f, 0.0f);
};

// Particle data structure for GPU (from PhysX)
struct FluidParticle {
    float4 positionMass;    // xyz = position, w = 1/mass
    float4 velocity;        // xyz = velocity, w = density
    float4 force;           // xyz = force, w = pressure
    float4 extra;           // For additional data (color, temperature, etc.)
};

// Covariance matrix for anisotropic rendering (from BufferD equivalent)
struct ParticleCovariance {
    float4 rotation;        // Quaternion rotation
    float4 scale;           // xyz = scale factors, w = unused
};

class SnVkFluid : public SnPhyFluid
{
public:
    // ShaderToy-style render modes
    enum FluidRenderMode {
        FRM_VOLUME = 0,         // Pure volume raymarching (ShaderToy style)
        FRM_SURFACE = 1,        // Distance field surface extraction + volume effects  
        FRM_DEBUG = 2           // 2D slice visualization of 3D textures
    };

    SnVkFluid(f32 size, ISceneNode* parent, ISceneManager* mgr, s32 id,
              const core::vector3df& position = core::vector3df(0, 0, 0),
              const core::vector3df& rotation = core::vector3df(0, 0, 0),
              const core::vector3df& scale = core::vector3df(1, 1, 1),
              const FluidSimParams& params = FluidSimParams(),
              const FluidVolumeParams& volumeParams = FluidVolumeParams());
    virtual ~SnVkFluid();

    // ISceneNode interface overrides (inherit physics from SnPhyFluid)
    virtual void OnAnimate(u32 timeMs) override;
    virtual void render() override;
    
    // Override SnPhyFluid::updateMesh() to prevent quad mesh generation for volume rendering
    virtual void updateMesh() override;

    // Volume rendering methods (ShaderToy pattern)
    void updateVolumeTextures();       // Upload PhysX data to 3D textures
    void generateDensityField3D();     // Compute 3D density field from particles
    void generateVelocityField3D();    // Compute 3D velocity field from particles
    void generateDistanceField3D();    // Compute signed distance field for surfaces
    void computeCovariance3D();        // Calculate anisotropic shapes (BufferD equivalent)
    
    // Volume configuration (ShaderToy style)
    void setVolumeResolution(u32 x, u32 y, u32 z);
    void setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds);
    void setRaymarchSteps(u32 steps);
    void setRaymarchStepSize(f32 stepSize);
    void setDensityThreshold(f32 threshold);
    void setVolumeNoiseScale(f32 scale);
    
    // Rendering configuration
    void setRenderMode(FluidRenderMode mode) { m_renderMode = mode; }
    void enableVolumetricEffects(bool enable) { m_enableVolumetric = enable; }
    void enableDiffuseRendering(bool enable) { m_enableDiffuseRendering = enable; }
    
    // Material properties for volume rendering
    void setFluidColor(const video::SColor& color);
    void setRefractionIndex(f32 index);
    void setAbsorptionColor(const core::vector3df& absorption);
    void setAbsorptionScale(f32 scale);
    void setScatteringScale(f32 scale);
    
    // Get simulation statistics (from PhysX)
    u32 getActiveParticleCount() const;
    bool hasVulkanMaterialRenderer() const { return s_fluidMaterialRenderer != nullptr; }

    // Volume texture access (for material renderer)
    video::ITexture* getDensityTexture3D() const { return m_densityTexture3D; }
    video::ITexture* getVelocityTexture3D() const { return m_velocityTexture3D; }
    video::ITexture* getDistanceTexture3D() const { return m_distanceTexture3D; }
    video::ITexture* getCovarianceTexture3D() const { return m_covarianceTexture3D; }
    video::ITexture* getNormalTexture3D() const { return m_normalTexture3D; }

protected:
    void initializeVulkanFluid();
    void initializeVolumeTextures();
    void updateVulkanBuffers();
    void extractPhysXData();
    void uploadParticleData();
    void dispatchVolumeComputeShaders();
    void createVulkanBuffers();
    void createVolumeTextures();
    
    // ShaderToy rendering method (single full-screen quad)
    void renderFullScreenQuad();

private:
    // Physics and volume parameters
    FluidSimParams m_params;
    FluidVolumeParams m_volumeParams;
    
    // GPU buffers for particle data (from PhysX)
    video::IHardwareBuffer* m_particleBuffer;         // Particle data from PhysX
    video::IHardwareBuffer* m_diffuseBuffer;          // Foam/bubble particles
    video::IHardwareBuffer* m_covarianceBuffer;       // Anisotropic shapes
    
    // 3D Volume textures (ShaderToy data)
    video::ITexture* m_densityTexture3D;      // R16F - density values
    video::ITexture* m_velocityTexture3D;     // RGB16F - velocity vectors  
    video::ITexture* m_distanceTexture3D;     // R16F - signed distance field
    video::ITexture* m_covarianceTexture3D;   // RGBA16F - covariance matrix data
    video::ITexture* m_normalTexture3D;       // RGB16F - surface normals
    video::ITexture* m_temperatureTexture3D;  // R16F - temperature/foam data
    
    // CPU staging arrays for volume data
    core::array<FluidParticle> m_particleData;
    core::array<FluidParticle> m_diffuseData;
    core::array<f32> m_densityField3D;
    core::array<core::vector3df> m_velocityField3D;
    core::array<f32> m_distanceField3D;
    
    // Rendering state
    FluidRenderMode m_renderMode;
    bool m_enableVolumetric;
    bool m_enableDiffuseRendering;
    bool m_volumeTexturesNeedUpdate;
    
    // Material properties
    video::SColor m_fluidColor;
    f32 m_refractionIndex;
    core::vector3df m_absorptionColor;
    u32 maxParticles;
    
    // CUDA staging buffers for PhysX data (following SnPhyFluid pattern)
    float4* ptcBuf;           // Particle position staging buffer
    float4* velBuf;           // Particle velocity staging buffer
    
    // Material renderer reference (static - shared across all fluid instances)
    static video::MrVkFluid* s_fluidMaterialRenderer;
    static video::E_MATERIAL_TYPE s_fluidMaterialType;
    static bool s_fluidInitialized;
    
    // Friend class for material renderer
    friend class video::MrVkFluid;
};

} // namespace scene

namespace video
{

// Forward declaration
class MrVkFluid;

} // namespace video
} // namespace irr 