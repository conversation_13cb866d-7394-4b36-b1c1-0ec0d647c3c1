#pragma once

#include "../../irrmmd/SnPhyFluid.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/include/irrArray.h"
#include "../../../UaIrrlicht/include/IHardwareBuffer.h"
#include "../../../UaIrrlicht/include/ITexture.h"

namespace irr
{
namespace scene
{



// Forward declaration - we'll use VkFluidVolumeUniforms from MrVkFluid.h

// Note: FluidVolumeParams removed - using video::VkFluidVolumeUniforms instead

// Particle data structure for GPU (from PhysX)
struct FluidParticle {
    float4 positionMass;    // xyz = position, w = 1/mass
    //float4 velocity;        // xyz = velocity, w = density
    //float4 force;           // xyz = force, w = pressure
    //float4 extra;           // For additional data (color, temperature, etc.)
};

// Covariance matrix for anisotropic rendering (from BufferD equivalent)
struct ParticleCovariance {
    float4 rotation;        // Quaternion rotation
    float4 scale;           // xyz = scale factors, w = unused
};

// Grid-based particle storage (following BufferB pattern)
struct GridParticleCell {
    float4 packedData;      // Packed data for 2 particles (like BufferB vec4)
    // Unpacked: particle0 mass+pos, particle1 mass+pos, particle0 velocity, particle1 velocity
};

class SnVkFluid : public SnPhyFluid
{
public:
    // ShaderToy-style render modes
    enum FluidRenderMode {
        FRM_VOLUME = 0,         // Pure volume raymarching (ShaderToy style)
        FRM_SURFACE = 1,        // Distance field surface extraction + volume effects  
        FRM_DEBUG = 2           // 2D slice visualization of 3D textures
    };

    SnVkFluid(f32 size, ISceneNode* parent, ISceneManager* mgr, s32 id,
              const core::vector3df& position = core::vector3df(0, 0, 0),
              const core::vector3df& rotation = core::vector3df(0, 0, 0),
              const core::vector3df& scale = core::vector3df(1, 1, 1),
              const video::VkFluidVolumeUniforms& volumeParams = video::VkFluidVolumeUniforms());
    virtual ~SnVkFluid();

    // ISceneNode interface overrides (inherit physics from SnPhyFluid)
    virtual void OnAnimate(u32 timeMs) override;
    virtual void render() override;
    
    // Override SnPhyFluid::updateMesh() to prevent quad mesh generation for volume rendering
    virtual void updateMesh() override {};
    virtual void RefreshMr() override;
    // Volume rendering methods (BufferD pattern)
    void dispatchParticleToGrid();     // Convert raw particles to grid structure (ParticleToGrid compute)
 
    void computeCovariance3D();        // Calculate anisotropic shapes (BufferD equivalent)
    
    // Volume configuration (ShaderToy style)
    void setVolumeResolution(u32 x, u32 y, u32 z);
    void setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds);

    virtual void setDepthTexture(irr::video::ITexture* texture) override;;

    // Rendering configuration
    void setRenderMode(FluidRenderMode mode) { m_renderMode = mode; }
    void enableVolumetricEffects(bool enable) { m_enableVolumetric = enable; }
    void enableDiffuseRendering(bool enable) { m_enableDiffuseRendering = enable; }
    
    // Material properties for volume rendering 
    void setRefractionIndex(f32 index);
    void setAbsorptionColor(const core::vector3df& absorption);
    
    // Get simulation statistics (from PhysX)
    u32 getActiveParticleCount() const;
    bool hasVulkanMaterialRenderer() const { return s_fluidMaterialRenderer != nullptr; }

 
    bool drawDebugScene = 0;
    virtual void changeMaterialPreset(int presetType) override;
    video::VkFluidVolumeUniforms Pm;
protected:
    void initializeVulkanFluid();
    void initializeVolumeTextures();
    void updateVulkanBuffers();
    void extractPhysXData();
    void uploadParticleData();
    
 
    void createVulkanBuffers();
    void createVolumeTextures();
    
    // ShaderToy rendering method (single full-screen quad)
    void renderFullScreenQuad();

    // Grid-based particle methods (BufferB pattern)
    void initializeParticleGrid();
 
 
private:

    
    // GPU buffers for particle data (from PhysX)
    video::IHardwareBuffer* m_particleBuffer;         // Particle data from PhysX

    // 3D Volume textures (BufferD pattern - essential textures only)
    video::ITexture* m_densityTexture3D;      // R32F - density values (BufferB equivalent)
#if COVARIANCE_COMPUTATION
    video::ITexture* m_covarianceTexture3D;   // RGBA16F - covariance matrix data (BufferD output)
#endif
    // Note: Velocity, distance, normal, and temperature textures removed 
    // as they're not needed for BufferD covariance computation pattern
    
    // CPU staging arrays for volume data (BufferD pattern)
    core::array<float4> m_particleData;  // Main particle data from PhysX
    core::array<FluidParticle> m_diffuseData;   // Foam/bubble particles
    
    // Rendering state
    FluidRenderMode m_renderMode;
    bool m_enableVolumetric;
    bool m_enableDiffuseRendering;
    bool m_volumeTexturesNeedUpdate;
    
    // Material properties
    video::SColor m_fluidColor;
    f32 m_refractionIndex;
    core::vector3df m_absorptionColor;
    u32 maxParticles;
    
    // CUDA staging buffers for PhysX data (following SnPhyFluid pattern)
    float4* ptcBuf, *ptcBufT;           // Particle position staging buffer
    float4* velBuf, *velBufT;           // Particle velocity staging buffer
    uint32_t* phaseBuf;                 // Particle phase/material staging buffer
    
    // Material renderer reference (static - shared across all fluid instances)
    static video::MrVkFluid* s_fluidMaterialRenderer;
    static video::E_MATERIAL_TYPE s_fluidMaterialType;
    static bool s_fluidInitialized;
    
    // Friend class for material renderer
    friend class video::MrVkFluid;
    
    // Grid-based particle storage (BufferB pattern)
    core::vector3d<u32> m_gridResolution;         // 3D grid dimensions
    video::ITexture* m_gridTexture3D, *m_gridTexture3D2;             // 3D texture storing grid data
    
    // Grid parameters
    f32 m_gridCellSize;                           // Size of each grid cell in world units
    core::vector3df m_gridWorldMin;               // Grid world space minimum bounds
    core::vector3df m_gridWorldMax;               // Grid world space maximum bounds
    core::vector3df m_localVolumeMin;
    core::vector3df m_localVolumeMax;

    core::dimension2d<u32> screenSize;
    int resDiv = 1;
    irr::video::ITexture* rtTex{};
};

} // namespace scene

namespace video
{

// Forward declaration
class MrVkFluid;

} // namespace video
} // namespace irr 