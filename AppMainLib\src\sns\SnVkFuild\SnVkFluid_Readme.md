Original request:

Implement real water fluild scene node here. Including shaders, MaterialRenderer class, SceneNode class, based on SnTemplate, see D:\AProj\AppMainLib\src\sns\SnVkPipelineTemplate_Readme.md

(1) learn fluid physics from "D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp"
(2) learn water rendering effect from shaderToy example "D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\shader\shadertoy\wt". only need 3d texture generation and rendering part, use physics particle data from (1) 

-----------------------

Updated Plan (ShaderToy Pattern):

# Vulkan Fluid Scene Node Implementation - ShaderToy Style

Real-time water fluid simulation using PhysX particle data with Vulkan volume raymarching rendering pipeline, following ShaderToy full-screen shader patterns.

## 🌊 Overview

This implementation creates a hybrid CPU-GPU fluid system that combines:
1. **PhysX SPH Physics** - CPU-based simulation from `SnPhyFluid.cpp` 
2. **GPU Volume Raymarching** - ShaderToy-style full-screen fragment shader rendering
3. **3D Density Fields** - GPU-generated volumetric data from particle positions
4. **Advanced Water Effects** - Pure ShaderToy-inspired volume rendering techniques

## 🏗️ ShaderToy Architecture

### **CPU Physics + GPU Volume Rendering Pipeline** 
*Based on ShaderToy "3D Water Box" Multi-Pass Architecture*

```
┌──────────────────┐
│   PhysX (CPU)    │ ← SnPhyFluid.cpp (replaces BufferA particle advection)
│  SPH Simulation  │   • Particle positions
│    + Diffuse     │   • Velocities  
│   Particles      │   • Foam/Bubbles
└─────────┬────────┘
          │ CPU→GPU Transfer
┌─────────▼────────┐
│ GPU Buffers      │ ← Upload particle data
│ Particle Data    │   • Position/velocity buffer
│ Diffuse Data     │   • Density/temperature data
└─────────┬────────┘
          │
┌─────────▼────────┐
│ Volume Generation│ ← VkFluidDensity.comp (inspired by BufferB)
│ 3D Density Field │   VkFluidDistance.comp 
│ 3D Distance Field│   VkFluidCovariance.comp (inspired by BufferD)
│ Covariance Matrix│   
└─────────┬────────┘
          │
┌─────────▼────────┐
│ Full-Screen Quad │ ← VkFluidRaymarching.vert/frag (inspired by Image.glsl)
│ Volume Raymarching│   • Anisotropic ellipsoid rendering
│ ShaderToy Style  │   • PBR water shading with refraction/reflection
│ Water Rendering  │   • All logic in fragment shader
└──────────────────┘
```

### Core Components

#### 1. **SnVkFluid** (Scene Node)
- **Inherits from SnPhyFluid**: Gets PhysX particle data
- **3D Texture Management**: Creates and updates volumetric textures
- **Full-Screen Rendering**: Renders single full-screen quad
- **No Geometry Instancing**: Pure volume-based rendering

#### 2. **MrVkFluid** (Material Renderer)  
- **Single Graphics Pipeline**: Full-screen triangle with raymarching
- **Volume Compute Pipelines**: 3D density/velocity field generation
- **ShaderToy Pattern**: Minimal vertex shader, complex fragment shader

#### 3. **ShaderToy Shader Pipeline**
*Mapping from ShaderToy "3D Water Box" buffers to VkFluid shaders:*

**Graphics Shaders (Image.glsl equivalent):**
- **VkFluidRaymarching.vert** - Minimal full-screen triangle vertex shader
- **VkFluidRaymarching.frag** - Complete volume raymarching fragment shader (←Image.glsl)

**Compute Shaders (Buffer equivalent):**
- **VkFluidDensity.comp** - Generate 3D density field from particles (←BufferB.glsl)
- **VkFluidCovariance.comp** - Calculate anisotropic ellipsoid shapes (←BufferD.glsl)  


**Replaced by PhysX:**
- ~~BufferA.glsl~~ - Particle advection & clustering (now handled by SnPhyFluid.cpp)
- ~~BufferC.glsl~~ - Force computation & integration (now handled by PhysX SPH)

### ShaderToy Data Flow
*Mapping ShaderToy multi-pass architecture to VkFluid pipeline:*

```
SnPhyFluid (PhysX)           SnVkFluid (Volume Generation)        MrVkFluid (Raymarching)
──────────────────          ─────────────────────────────       ───────────────────────
│ updateMesh()    │   ────→  │ uploadParticleData()      │ ────→ │ Full-Screen Quad     │
│ - Extract PhysX │          │ - BufferB: density field  │       │ - Image.glsl style   │
│ - Copy to CPU   │          │ - BufferD: covariance     │       │ - Raymarch volume    │
│ - Get particles │          │ - Generate distance field │       │ - Sample 3D textures │
│                 │          │ - Update 3D textures      │       │ - Volume lighting    │
│ Replaces:       │          └───────────────────────────┘       │ - Surface extraction │
│ • BufferA.glsl  │                                              └─────────────────────┘
│ • BufferC.glsl  │          
└─────────────────┘          

ShaderToy Original Multi-Pass:
BufferA → BufferB → BufferC → BufferD → Image
  ↓         ↓         ↓         ↓         ↓
PhysX     Density   PhysX    Covariance  Raymarch
(CPU)     Compute   (CPU)    Compute     Fragment
```

## 🚀 ShaderToy Features

### From PhysX (SnPhyFluid)
- **Complete SPH Physics**: Density, pressure, viscosity, surface tension
- **Container Boundaries**: Collision detection and response  
- **Diffuse Particles**: Automatic foam and bubble generation
- **Particle Management**: Dynamic addition/removal, lifetime tracking
- **CUDA Integration**: GPU-accelerated PhysX computation

### GPU Volume Rendering Enhancements
- **3D Density Fields**: Smooth volumetric density from particles
- **3D Velocity Fields**: Fluid motion vectors for animation effects
- **Signed Distance Fields**: Precise surface reconstruction
- **Volume Raymarching**: True ShaderToy-style rendering
- **Procedural Effects**: Noise, turbulence, foam generation
- **Volume Lighting**: Scattering, absorption, caustics

## 📋 Updated Implementation Status

### ✅ Completed
- [x] Architecture redesign (PhysX + ShaderToy Volume Rendering)
- [x] Header files with volume rendering structure
- [x] Descriptor sets for 3D textures and volume data
- [x] Material renderer interface

### 🚧 In Progress  
- [ ] SnVkFluid implementation (inheriting from SnPhyFluid)
- [ ] MrVkFluid ShaderToy-style material renderer
- [ ] PhysX data extraction and 3D texture upload
- [ ] Volume generation compute shaders
- [ ] Full-screen raymarching shaders

### 📅 TODO
- [ ] 3D texture management and streaming
- [ ] Volume lighting and caustics
- [ ] Foam and bubble volume effects
- [ ] Performance optimization for high-resolution volumes
- [ ] Multi-scale rendering (LOD volumes)

## 💻 Usage

### Basic Setup
```cpp
#include "SnVkFluid.h"

// Create hybrid fluid system (inherits PhysX simulation)
SnVkFluid* fluid = new SnVkFluid(
    1.0f,                           // particle size
    sceneManager->getRootSceneNode(), // parent
    sceneManager,                   // scene manager  
    -1,                            // id
    core::vector3df(0, 10, 0)      // position
);

// PhysX configuration (inherited)
fluid->numPointsX = 30;
fluid->numPointsY = 40; 
fluid->numPointsZ = 20;
fluid->particleSpacing = 0.15f;

// Volume rendering configuration (ShaderToy style)
fluid->setVolumeResolution(128, 128, 128);
fluid->setRaymarchSteps(128);
fluid->setVolumeExtents(vector3df(-10, -5, -10), vector3df(10, 15, 10));
```

### Advanced Configuration
```cpp
// PhysX physics parameters
fluid->fluidDensity = 1000.0f;
fluid->showDiffuseParticles = true;
fluid->maxDiffuseParticles = 50000;

// ShaderToy volume rendering parameters  
fluid->setFluidColor(SColor(255, 100, 150, 255));
fluid->setRefractionIndex(1.333f);
fluid->setAbsorptionColor(vector3df(0.584f, 0.843f, 0.953f));
fluid->setRaymarchStepSize(0.02f);
fluid->setDensityThreshold(0.1f);
fluid->setVolumeNoiseScale(2.0f);
```

## 🔧 Technical Details

### PhysX to Volume Data
```cpp
// From SnPhyFluid::updateMesh()
PxVec4* positions = userBuffer->getPositionInvMasses();
PxVec4* velocities = userBuffer->getVelocities();
const PxU32 numParticles = userBuffer->getNbActiveParticles();

// Generate 3D volume textures from particle data
updateDensityField3D(positions, numParticles);
updateVelocityField3D(positions, velocities, numParticles);
updateDistanceField3D(positions, numParticles);
```

### Volume Data Structures
```cpp
// 3D Textures for volume rendering
VkImage densityTexture3D;     // R16F - density values
VkImage velocityTexture3D;    // RGB16F - velocity vectors
VkImage distanceTexture3D;    // R16F - signed distance field
VkImage normalTexture3D;      // RGB16F - surface normals
VkImage temperatureTexture3D; // R16F - temperature/foam data

// Volume rendering uniforms
struct VkFluidVolumeUniforms {
    matrix4 mViewProjectionInverse;
    vector3 cameraPosition;
    vector3 volumeMin, volumeMax;
    vector3 volumeSize;
    float rayStepSize;
    float densityThreshold;
    int maxRaymarchSteps;
    float noiseScale;
    float time;
};
```

### ShaderToy Vertex Shader
```glsl
#version 460
// VkFluidRaymarching.vert - Minimal full-screen triangle
void main() {
    // Generate full-screen triangle (ShaderToy pattern)
    vec2 positions[3] = vec2[](
        vec2(-1, -1), vec2(3, -1), vec2(-1, 3)
    );
    gl_Position = vec4(positions[gl_VertexIndex], 0, 1);
}
```

### ShaderToy Fragment Shader Structure
*Based on Image.glsl anisotropic ellipsoid rendering:*

```glsl
#version 460
// VkFluidRaymarching.frag - Complete volume raymarching (←Image.glsl)

// 3D textures (vs ShaderToy 2D texture mapping)
layout(binding = 3) uniform sampler3D texDensity;      // From BufferB equivalent
layout(binding = 4) uniform sampler3D texVelocity;     // Particle velocity field
layout(binding = 5) uniform sampler3D texDistance;     // Signed distance field
layout(binding = 6) uniform sampler3D texCovariance;   // From BufferD equivalent

void main() {
    vec2 uv = gl_FragCoord.xy / resolution.xy;
    
    // Generate camera ray (same as Image.glsl)
    vec3 rayOrigin = cameraPosition;
    vec3 rayDirection = generateRayDirection(uv);
    
    // Anisotropic ellipsoid intersection (from Image.glsl iEllipsoid function)
    Ray ray = Ray(rayOrigin, rayDirection, MAX_DIST, vec3(0), vec3(0));
    
    // Grid traversal and ellipsoid testing (ShaderToy pattern)
    vec3 gridPos = rayOrigin;
    for (int i = 0; i < maxSteps; i++) {
        // Sample covariance matrix from BufferD equivalent
        vec4 covarianceData = texture(texCovariance, gridPos);
        vec3 ellipsoidScale = covarianceData.xyz;
        vec4 ellipsoidRotation = unpackQuaternion(covarianceData.w);
        
        // Test ray-ellipsoid intersection (Image.glsl technique)
        iEllipsoid(ray, gridPos, ellipsoidScale, ellipsoidRotation);
        
        if (ray.td < MAX_DIST) {
            // Surface found - PBR shading (from Image.glsl)
            vec3 color = computePBRShading(ray.ro + ray.rd * ray.td, ray.normal);
            break;
        }
        
        gridPos += rayDirection * stepSize;
    }
    
    outColor = vec4(ray.color, 1.0);
}
```

## 🎨 Rendering Modes

### 1. **Pure Volume Mode** (FRM_VOLUME)
- Full ShaderToy-style volume raymarching
- Highest quality, most expensive
- True volumetric effects and lighting

### 2. **Hybrid Surface Mode** (FRM_SURFACE)
- Distance field surface extraction + volume effects
- Good balance of quality and performance
- Sharp surfaces with volume details

### 3. **Debug Slice Mode** (FRM_DEBUG)
- 2D slice visualization of 3D textures
- Development and debugging tool
- Fast volume data inspection

## 🔄 Next Steps

1. **Volume Generation Compute Shaders (BufferB/BufferD equivalents)**
   - **VkFluidDensity.comp**: Implement particle-to-volume conversion (BufferB.glsl)
   - **VkFluidCovariance.comp**: SVD decomposition for anisotropic ellipsoids (BufferD.glsl)
   - Optimize 3D texture updates and temporal coherence

2. **ShaderToy Fragment Shader Development (Image.glsl equivalent)** 
   - **VkFluidRaymarching.frag**: Complete volume raymarching implementation
   - Port anisotropic ellipsoid intersection from Image.glsl
   - Add advanced lighting, caustics, and foam effects from ShaderToy

3. **Performance Optimization**
   - Multi-scale volume rendering (LOD)
   - Adaptive raymarching step sizes
   - GPU-driven volume updates

## 📊 Performance Expectations (ShaderToy Style)

| Volume Resolution | Particles | GPU Load | Total FPS | Quality |
|------------------|-----------|----------|-----------|---------|
| 64³ (262k voxels) | 10k      | High     | 60 FPS    | Medium  |
| 128³ (2M voxels)  | 50k      | Very High| 30 FPS    | High    |  
| 256³ (16M voxels) | 100k     | Extreme  | 15 FPS    | Ultra   |

*Note: ShaderToy pattern is GPU-intensive, requires high-end graphics cards*

## 🔗 ShaderToy Buffer Mapping

### **Original ShaderToy "3D Water Box" Pipeline:**
```
BufferA.glsl → BufferB.glsl → BufferC.glsl → BufferD.glsl → Image.glsl
Particle     │ Density     │ Force       │ Covariance  │ Final
Advection    │ Computation │ Integration │ Matrix      │ Rendering
```

### **VkFluid Equivalent Implementation:**
| ShaderToy Buffer | VkFluid Implementation | Purpose |
|------------------|----------------------|---------|
| **BufferA.glsl** | `SnPhyFluid.cpp` (PhysX) | Particle advection & clustering |
| **BufferB.glsl** | `VkFluidDensity.comp` | 3D density field generation |
| **BufferC.glsl** | `SnPhyFluid.cpp` (PhysX) | Force computation & integration |
| **BufferD.glsl** | `VkFluidCovariance.comp` | SVD decomposition for ellipsoids |
| **Image.glsl** | `VkFluidRaymarching.frag` | Anisotropic ellipsoid rendering |

### **Key ShaderToy Features Preserved:**
- **Anisotropic Ellipsoids**: BufferD SVD decomposition → VkFluidCovariance.comp
- **3D Grid Mapping**: 2D texture → True 3D textures (improved)
- **Volume Raymarching**: Image.glsl technique → VkFluidRaymarching.frag
- **PBR Water Shading**: Physical lighting model with refraction/reflection

### **Hybrid Architecture Benefits:**
- **Physics**: PhysX (CPU) replaces expensive GPU SPH simulation
- **Rendering**: Pure ShaderToy raymarching pattern preserved
- **Quality**: Same visual output as original ShaderToy
- **Performance**: Optimized for production use

## 🛠️ Development Notes

- **Pure Volume Rendering**: No geometry, all effects in fragment shader
- **ShaderToy Compatibility**: Patterns directly transferable from ShaderToy
- **3D Texture Focus**: All fluid data stored in 3D textures (vs 2D mapping)
- **High GPU Requirements**: Volume raymarching is computationally expensive
- **Artistic Control**: Easy to add procedural effects and modifications
- **BufferD Complexity**: SVD decomposition for anisotropic ellipsoids requires advanced math

- **Log**: Instead of `printf("message", args)`,  use `DP(("message", args))`