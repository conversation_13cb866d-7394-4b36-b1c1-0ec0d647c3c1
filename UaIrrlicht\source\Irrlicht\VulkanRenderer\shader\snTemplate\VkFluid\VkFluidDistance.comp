#version 460

/**
 * =================================================================
 * VkFluidDistance.comp - 3D Distance Field Generation Compute Shader
 * =================================================================
 * 
 * Generates 3D signed distance field from SPH particle data
 * Creates smooth surface representation for volume rendering
 * 
 * Algorithm:
 * 1. For each voxel in 3D volume texture
 * 2. Find distance to nearest particle surface
 * 3. Use SPH kernel functions for smooth surface reconstruction
 * 4. Store signed distance in 3D texture for volume rendering
 * =================================================================
 */

layout(local_size_x = 8, local_size_y = 8, local_size_z = 8) in;

// Uniform buffer
layout(set = 0, binding = 0) uniform FluidUniforms {
    mat4 mViewProjectionInverse;
    vec3 volumeMin;
    float time;
    vec3 volumeMax; 
    float rayStepSize;
    vec3 volumeSize;
    float densityThreshold;
    vec3 cameraPosition;
    int maxRaymarchSteps;
    float noiseScale;
    float refractionIndex;
    float absorptionScale;
    float scatteringScale;
    uint numParticles;
    float padding0;
    float padding1;
    float padding2;
} uniforms;

// Particle data buffers
layout(set = 0, binding = 1, std430) readonly buffer ParticleBuffer {
    vec4 positionMass[100000];    // xyz = position, w = 1/mass
    vec4 velocity[100000];        // xyz = velocity, w = density
    vec4 force[100000];           // xyz = force, w = pressure
    vec4 extra[100000];           // Additional data
} particles;

// Output 3D distance texture
layout(set = 0, binding = 5, r16f) uniform image3D distanceTexture;

// Constants
#define PI 3.1415926535
#define KERNEL_RADIUS 1.0
#define PARTICLE_RADIUS 0.5
#define ISO_SURFACE_THRESHOLD 0.5

// SPH Kernel Functions
float kernelPoly6(float r, float h) {
    if (r > h) return 0.0;
    float ratio = h * h - r * r;
    return (315.0 / (64.0 * PI)) * ratio * ratio * ratio / (h * h * h * h * h * h * h * h * h);
}

// Gaussian kernel for smooth distance field
float gaussianKernel(float r, float h) {
    float norm = 1.0 / (h * h * h * sqrt(PI * PI * PI * 8.0));
    return norm * exp(-0.5 * (r * r) / (h * h));
}

// Compute density at a point for surface reconstruction
float computeDensityAt(vec3 pos) {
    float totalDensity = 0.0;
    uint numParticles = uniforms.numParticles;
    
    for (uint i = 0; i < numParticles; i++) {
        vec4 particlePosMass = particles.positionMass[i];
        if (particlePosMass.w == 0.0) continue; // Skip invalid particles
        
        vec3 particlePos = particlePosMass.xyz;
        float mass = 1.0 / max(particlePosMass.w, 1e-6);
        float distance = length(pos - particlePos);
        
        totalDensity += mass * gaussianKernel(distance, KERNEL_RADIUS);
    }
    
    return totalDensity;
}

// Compute signed distance to fluid surface
float computeSignedDistance(vec3 voxelPos) {
    // Sample density at current position
    float density = computeDensityAt(voxelPos);
    
    // Convert density to signed distance
    // Positive inside fluid, negative outside
    float signedDistance = ISO_SURFACE_THRESHOLD - density;
    
    // Scale to reasonable distance units
    signedDistance *= PARTICLE_RADIUS;
    
    return signedDistance;
}

void main() {
    ivec3 voxelCoord = ivec3(gl_GlobalInvocationID.xyz);
    ivec3 volumeSize = imageSize(distanceTexture);
    
    // Check bounds
    if (any(greaterThanEqual(voxelCoord, volumeSize))) {
        return;
    }
    
    // Convert voxel coordinate to world position
    vec3 voxelPos = uniforms.volumeMin + 
                   (vec3(voxelCoord) + 0.5) * uniforms.volumeSize / vec3(volumeSize);
    
    // Compute signed distance to fluid surface
    float signedDistance = computeSignedDistance(voxelPos);
    
    // Store distance in 3D texture
    imageStore(distanceTexture, voxelCoord, vec4(signedDistance, 0.0, 0.0, 0.0));
}
