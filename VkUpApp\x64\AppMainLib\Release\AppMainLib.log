﻿  MrCsParticle.cpp
  VideoFrameProcessor.cpp
  MrSnTemplate.cpp
  MrVkFluid.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/sns/MrSnTemplate.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/sns/SnVkFuild/MrVkFluid.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/MrCsParticle.cpp')
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(70,64): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(104,11): warning C4101: 'res': unreferenced local variable
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\sns\SnVkFuild\MrVkFluid.cpp(263,46): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/sns/SnVkFuild/MrVkFluid.cpp')
  
  AppMainLib.vcxproj -> D:\AProj\VkUpApp\x64\Release\AppMainLib.lib
