import bpy
import bmesh
import math
import time
import traceback
from mathutils.bvhtree import BVHTree
from mathutils import Vector
from mathutils.geometry import barycentric_transform
import numpy as np
from pathlib import Path
import logging


# First we need to ensure pyvox is available
try:
    from pyvox.models import Vox, Color, get_default_palette
    from pyvox.writer import VoxWriter
except ImportError:
    import sys
    import subprocess
    # Install pyvox if not present
    subprocess.check_call([sys.executable, "-m", "pip", "install", "py-vox-io"])
    from pyvox.models import Vox, Color, get_default_palette
    from pyvox.writer import VoxWriter

image_tuples = {}

def TriangulateMesh( obj ):
    bm = bmesh.new()
    bm.from_mesh( obj.data )
    bmesh.ops.triangulate( bm, faces=bm.faces[:] )
    bm.to_mesh( obj.data )
    bm.free()
    
 
def get_material_image(material):
    try:
        if not material or not material.use_nodes:
            # print(f"DEBUG: Material {material.name if material else 'None'} not using nodes")
            return None

        # Check all nodes for Image Texture nodes
        for node in material.node_tree.nodes:
            if node.type == 'TEX_IMAGE' and node.image:
                # print(f"DEBUG: Found image texture node: {node.name} with image: {node.image.name}")
                return node.image

        # print(f"DEBUG: No image texture found in material {material.name}")
        return None

    except Exception as e:
        # print(f"DEBUG: Error in get_material_image: {str(e)}")
        print(traceback.format_exc())
        return None

def get_color_from_geometry(obj, ray_origin, ray_direction, orig_scene=None, location=None, polygon_index=-1):
    global image_tuples
    
    if not location or polygon_index == -1:
        if not orig_scene:
            dg = bpy.context.evaluated_depsgraph_get()
            orig_scene = bpy.context.scene.evaluated_get(dg)
        success, location, normal, polygon_index, object, matrix = orig_scene.ray_cast(bpy.context.view_layer, ray_origin, ray_direction, distance=0.002)
        if not success:
            # print("DEBUG: Raycast failed")
            return None
    
    try:
        # Get material and UV data
        polygon = obj.data.polygons[polygon_index]
        material_index = polygon.material_index
        # print(f"DEBUG: Processing polygon {polygon_index} with material index {material_index}")

        if material_index >= len(obj.material_slots):
            # print(f"DEBUG: Invalid material index {material_index}")
            return [0.8, 0.8, 0.8]

        material = obj.material_slots[material_index].material
        if not material:
            # print("DEBUG: No material assigned")
            return [0.8, 0.8, 0.8]

        # Try to get texture first
        image = get_material_image(material)
        if image:
            # print(f"DEBUG: Processing image {image.name}")
            
            # Check if object has UV coordinates
            if not obj.data.uv_layers:
                # print("DEBUG: No UV layers found")
                return get_material_color(material)

            # Get UV coordinates
            uv_layer = obj.data.uv_layers.active
            if not uv_layer:
                # print("DEBUG: No active UV layer")
                return get_material_color(material)

            loop_indices = polygon.loop_indices
            uvs = [uv_layer.data[i].uv for i in loop_indices]
            
            # Get barycentric coordinates
            vertices = [obj.data.vertices[i].co for i in polygon.vertices]
            barycentric = barycentric_transform(location, vertices[0], vertices[1], vertices[2],
                                             Vector((uvs[0][0], uvs[0][1], 0)),
                                             Vector((uvs[1][0], uvs[1][1], 0)),
                                             Vector((uvs[2][0], uvs[2][1], 0)))

            # Sample image
            width, height = image.size
            x = int((barycentric.x % 1.0) * (width - 1))
            y = int((barycentric.y % 1.0) * (height - 1))
            
            if image.name not in image_tuples:
                # print(f"DEBUG: Caching image {image.name}")
                image_tuples[image.name] = tuple(image.pixels)
            
            pixel_index = (y * width + x) * 4
            color = image_tuples[image.name][pixel_index:pixel_index + 4]
            # print(f"DEBUG: Sampled color from texture: {color}")
            return color

        # Fallback to material color
        # print("DEBUG: Falling back to material color")
        return get_material_color(material)

    except Exception as e:
        # print(f"DEBUG: Error in get_color_from_geometry: {str(e)}")
        print(traceback.format_exc())
        return [0.8, 0.8, 0.8]

def try_add_color_to_palette(new_color, palette, color_threshold=24):
    if len(palette) >= 254:
        return palette, nearest_color_index(new_color, palette)
    for color in palette:
        if color_distance(new_color, color) <= color_threshold:
            return palette, nearest_color_index(new_color, palette)
    palette.append(new_color)
    return palette, (len(palette)-1)

def get_material_color(material):
    # print(f"DEBUG: Getting color for material {material.name}")
    
    if not material:
        # print("DEBUG: No material found")
        return (0.8, 0.8, 0.8, 1.0)
        
    if material.use_nodes:
        # Try to get color from nodes
        for node in material.node_tree.nodes:
            # Check Principled BSDF
            if node.type == 'BSDF_PRINCIPLED':
                color = node.inputs[0].default_value
                # print(f"DEBUG: Found Principled BSDF color: {color}")
                return color
                
            # Check Diffuse BSDF
            elif node.type == 'BSDF_DIFFUSE':
                color = node.inputs[0].default_value
                # print(f"DEBUG: Found Diffuse BSDF color: {color}")
                return color
                
            # Check emission
            elif node.type == 'EMISSION':
                color = node.inputs[0].default_value
                # print(f"DEBUG: Found Emission color: {color}")
                return color
    
    # Try to get viewport display color
    if hasattr(material, 'diffuse_color'):
        # print(f"DEBUG: Using diffuse color: {material.diffuse_color}")
        return material.diffuse_color
        
    # Try to get base color
    if hasattr(material, 'use_nodes') and not material.use_nodes:
        # print(f"DEBUG: Using base color: {material.node_tree.nodes['Base Color'].outputs[0].default_value}")
        return material.node_tree.nodes['Base Color'].outputs[0].default_value

    # print("DEBUG: Returning default grey color")
    return (0.8, 0.8, 0.8, 1.0)

def get_closest_point(p, obj, max_dist=1.84467e+19):
    # max_dist = 1.84467e+19
    result, location, normal, face = obj.closest_point_on_mesh(p, distance=max_dist)
    return result, location, normal, face

def distance(c1, c2):
    (r1,g1,b1) = c1
    (r2,g2,b2) = c2
    return math.sqrt((r1 - r2)**2 + (g1 - g2) ** 2 + (b1 - b2) **2)

def color_distance(c1, c2):
    (r1,g1,b1) = c1.r, c1.g, c1.b
    (r2,g2,b2) = c2.r, c2.g, c2.b
    return math.sqrt((r1 - r2)**2 + (g1 - g2) ** 2 + (b1 - b2) **2)

def find_center(o):
    vcos = [ o.matrix_world @ v.co for v in o.data.vertices ]
    findCenter = lambda l: ( max(l) + min(l) ) / 2

    x,y,z  = [ [ v[i] for v in vcos ] for i in range(3) ]
    center = [ findCenter(axis) for axis in [x,y,z] ]

    return tuple(center)

def find_bounds(o):
    vcos = [ o.matrix_world @ v.co for v in o.data.vertices ]
    findCenter = lambda l: ( max(l) + min(l) ) / 2

    x,y,z  = [ [ v[i] for v in vcos ] for i in range(3) ]
    bbox_min = [ min(axis) for axis in [x,y,z] ]
    bbox_max = [ max(axis) for axis in [x,y,z] ]

    return tuple(bbox_min), tuple(bbox_max)

def nearest_color(color, palette):
    colors_dict = {}
    for i in range(len(palette)):
        colors_dict[i] = palette[i]
    closest_colors = sorted(colors_dict, key=lambda point: color_distance(color, colors_dict[point]))
    return colors_dict[closest_colors[0]]
    
def nearest_color_index(color, palette):
    color = nearest_color(color, palette)
    return palette.index(color)

def voxelize(obj, file_path, vox_detail=32, use_default_palette=False):
    global image_tuples
    image_tuples = {}
    last_time = time.time()

    # print("DEBUG: Starting voxelization process')
    source = obj
    source_name = obj.name
    
    bpy.ops.object.select_all(action='DESELECT')
    source.select_set(True)
    
    bpy.ops.object.duplicate_move(OBJECT_OT_duplicate={"linked":False, "mode":"TRANSLATION"})
    bpy.context.object.name = source_name+'_voxelized'
    bpy.ops.object.transform_apply(location=True, rotation=True, scale=True)
    bpy.ops.object.convert(target='MESH')
    
    target_name = bpy.context.object.name
    target = bpy.data.objects[target_name]
    TriangulateMesh(target)
    
    # voxelize
    
    vox_size = max(target.dimensions) / vox_detail
    half_size = vox_size * 0.5
    bbox_min, bbox_max = find_bounds(target)
    
    a = np.zeros((vox_detail, vox_detail, vox_detail), dtype=int)
    
    dg = bpy.context.evaluated_depsgraph_get()
    orig_scene = bpy.context.scene.evaluated_get(dg)
    
    if not use_default_palette:
        palette = []
        # print("DEBUG: Using empty palette")
    else:
        palette = get_default_palette()[1:256]
        # print("DEBUG: Using default palette, length:', len(palette))
    
    for x1 in range(0,vox_detail):
        print(str(int(x1 / vox_detail * 100))+'%...')
        x = bbox_min[0] + x1 * vox_size + half_size
        if x > bbox_max[0] + vox_size:
            break
        for y1 in range(0,vox_detail):
            y = bbox_min[1] + y1 * vox_size + half_size
            if y > bbox_max[1] + vox_size:
                break
            for z1 in range(0,vox_detail):
                z = bbox_min[2] + z1 * vox_size + half_size
                if z > bbox_max[2] + vox_size:
                    break
                inside, inside_location, inside_normal, inside_face = get_closest_point(Vector((x,y,z)), target, max_dist=half_size*1.5)
                if inside:
                    inside = (inside_location[0], inside_location[1], inside_location[2])
                    vox_min = (x-half_size,y-half_size,z-half_size)
                    vox_max = (x+half_size,y+half_size,z+half_size)
                    if inside > vox_min and inside < vox_max:
                        location = (inside_location[0] + inside_normal[0] * 0.001,
                            inside_location[1] + inside_normal[1] * 0.001,
                            inside_location[2] + inside_normal[2] * 0.001)
                        normal = (-inside_normal[0], -inside_normal[1], -inside_normal[2])
                        color = get_color_from_geometry(target, location, normal, orig_scene=orig_scene, location=inside_location, polygon_index=inside_face)
                        if color:
                            if len(color) == 4 and color[3] < 0.1:
                                continue
                            color = Color(int(color[0]*255), int(color[1]*255), int(color[2]*255), 255)
                            threshold = max(7, min(12, len(palette) * 0.65))
                            palette, color_index = try_add_color_to_palette(color, palette, color_threshold=threshold)
                            #color_index = nearest_color_index(color, palette[1:])
                            a[y1,(vox_detail-1)-z1,x1] = color_index+1

    vox = Vox.from_dense(a)
    print('Palette length', len(palette))
    vox.palette = palette
    VoxWriter(file_path, vox).write()
    print('100%... Exported to', file_path)
    
    # delete temporary target
    bpy.ops.object.select_all(action='DESELECT')
    target.select_set(True)
    bpy.ops.object.delete()
    bpy.ops.object.select_all(action='DESELECT')
    source.select_set(True)
    bpy.context.view_layer.objects.active = source
    print('Took', int(time.time() - last_time), 'seconds')



def main():
    # Get the active object
    print('SSSSSSSSSSSSSSSSSSSSSSSSSS')

    obj = bpy.context.active_object
    
    if not obj:
        raise Exception("No object selected")
        
    if obj.type != 'MESH':
        raise Exception("Selected object must be a mesh")
    
    # Get the output path
    blend_file_path = bpy.data.filepath
    directory = Path(blend_file_path).parent if blend_file_path else Path.home()
    output_path =  "R:/output1.vox"
    
    # Voxelize with default settings
    voxelize(obj, str(output_path), vox_detail=64, use_default_palette=False)

if __name__ == "__main__":
    main()