/**
 * =================================================================
 * BUFFER A - 粒子平流与聚类阶段 (Particle Advection & Clustering Stage)
 * =================================================================
 * 
 * 这是SPH流体模拟的第一个阶段，负责：
 * This is the first stage of SPH fluid simulation, responsible for:
 * 
 * 主要功能 (Main Functions):
 * 1. 粒子平流 (Particle Advection) - 基于速度更新粒子位置
 * 2. 粒子聚类 (Particle Clustering) - 将移动的粒子重新分配到网格
 * 3. 粒子分割 (Particle Splitting) - 当需要时分割粒子以保持平衡
 * 
 * 算法流程 (Algorithm Flow):
 * 1. 遍历当前单元及其26个邻居 (Iterate current cell and its 26 neighbors)
 * 2. 对每个邻居单元的粒子进行平流 (Advect particles from each neighbor cell)
 * 3. 检查平流后的粒子是否落入当前单元 (Check if advected particles fall into current cell)
 * 4. 如果是，则进行聚类分配 (If yes, perform clustering assignment)
 * 5. 平衡粒子分布，必要时进行分割 (Balance particle distribution, split if necessary)
 * 
 * 输入数据 (Input Data):
 * - ch0: 前一帧的粒子状态 (Previous frame particle states)
 * 
 * 输出数据 (Output Data):
 * - fragColor: 更新后的粒子状态 (Updated particle states)
 * =================================================================
 */
#include "wt_common.glsl"
// filepath: d:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\shader\shadertoy\wt\bufferA.glsl
void mainImage( out vec4 fragColor, in vec2 fragCoord )
{
    // 初始化3D网格映射系统 (Initialize 3D grid mapping system)
    InitGrid(iResolution.xy);
    
    // 将片段坐标转换为整数网格坐标 (Convert fragment coordinates to integer grid coordinates)
    fragCoord = floor(fragCoord);
    vec3 pos = dim3from2(fragCoord);
    
    // 初始化当前单元的粒子 (Initialize particles for current cell)
    // 每个网格单元最多存储2个粒子 (Each grid cell stores up to 2 particles)
    Particle p0, p1;

    // =================================================================
    // 粒子平流与聚类循环 (Particle Advection & Clustering Loop)
    // =================================================================
    // 遍历3x3x3邻域（包括中心单元）(Iterate through 3x3x3 neighborhood including center)
    range(i, -1, 1) range(j, -1, 1) range(k, -1, 1)
    {
        // 计算邻居单元坐标 (Calculate neighbor cell coordinates)
        vec3 pos1 = pos + vec3(i, j, k);
        
        // 边界检查：确保邻居单元在有效范围内 (Boundary check: ensure neighbor is within valid range)
        if(!all(lessThanEqual(pos1, size3d)) || !all(greaterThanEqual(pos1, vec3(0.0))))
        {
            continue;
        }
        
        // 从邻居单元加载粒子数据 (Load particle data from neighbor cell)
        Particle p0_, p1_;
        unpackParticles(LOAD3D(ch0, pos1), pos1, p0_, p1_);
        
        // 处理第一个粒子 (Process first particle)
        if(p0_.mass > 0u)
        {
            // 粒子平流：根据速度和时间步长更新位置 (Particle advection: update position based on velocity and time step)
            p0_.pos += p0_.vel*dt;
            
            // 粒子聚类：如果平流后粒子落入当前单元，则分配到合适的聚类
            // (Particle clustering: if advected particle falls into current cell, assign to appropriate cluster)
            Clusterize(p0, p1, p0_, pos);
        }
   
        // 处理第二个粒子 (Process second particle)
        if(p1_.mass > 0u)
        {
            // 同样的平流和聚类过程 (Same advection and clustering process)
            p1_.pos += p1_.vel*dt;
            Clusterize(p0, p1, p1_, pos);
        }
    }
    
    // =================================================================
    // 粒子平衡与分割 (Particle Balancing & Splitting)
    // =================================================================
    // 如果只有一个粒子有质量，尝试分割以平衡分布
    // (If only one particle has mass, try to split for balanced distribution)
    
    // 情况1：p0有质量，p1无质量 (Case 1: p0 has mass, p1 has no mass)
    if(p1.mass == 0u && p0.mass > 0u)
    {
        SplitParticle(p0, p1);
    }

    // 情况2：p1有质量，p0无质量 (Case 2: p1 has mass, p0 has no mass)
    if(p0.mass == 0u && p1.mass > 0u)
    {
        SplitParticle(p1, p0);
    }
    
    // =================================================================
    // 数据输出 (Data Output)
    // =================================================================
    // 将处理后的粒子数据打包并输出 (Pack processed particle data and output)
    vec4 packed = packParticles(p0, p1, pos);
    fragColor = packed;
}