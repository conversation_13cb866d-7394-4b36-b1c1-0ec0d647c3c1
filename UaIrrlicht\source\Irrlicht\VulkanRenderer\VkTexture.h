#pragma once
#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "ITexture.h"
#include "IImage.h"
#if defined(__BORLANDC__) || defined (__BCPLUSPLUS__)
#include "irrMath.h"    // needed by borland for sqrtf define
#endif

#include "VkHeader.h"
#define VKTEXTURE_CAST(drv) ((VkTexture*)drv)

namespace irr
{
	namespace scene {
		class  IMeshBuffer;
	}
	namespace video
	{

			
		class VkDriver;
		class VulkanPass;
		// forward declaration for RTT depth buffer handling
		struct SDepthSurfaceVk;

		class VkTexture : public ITexture
		{
			friend class VkDriverBase;
		public:

			//! constructor 
			VkTexture(IImage* image, VkDriver* driver,
				u32 flags, const io::path& name, u32 arraySlices = 1, void* mipmapData = 0);
			VkTexture(const std::vector<IImage*>& images, VkDriver* driver,
				u32 flags, const io::path& name);

			//! rendertarget constructor
			VkTexture(VkDriver* driver, const core::dimension2d<u32>& size, const io::path& name,
				const ECOLOR_FORMAT format = ECF_UNKNOWN, u32 arraySlices = 1,
				u32 sampleCount = 1, u32 sampleQuality = 0);

			//! 3D texture constructor
			VkTexture(VkDriver* driver, const core::vector3d<u32>& size3d, const io::path& name,
				const ECOLOR_FORMAT format = ECF_UNKNOWN, u32 sampleCount = 1, u32 sampleQuality = 0);

			//! 3D texture constructor (non-render target)
			VkTexture(VkDriver* driver, const core::vector3d<u32>& size3d, const io::path& name,
				const ECOLOR_FORMAT format, bool isRenderTarget, u32 sampleCount = 1, u32 sampleQuality = 0);
#if 0
			VkTexture(VkDriver* driver, const io::path& name, vks::Texture *pTex, bool needRTV = true);
#endif

			void InitVars(VkDriver* driver);

			//! destructor
			virtual ~VkTexture();

			virtual ITexture* Clone(const io::path& name) override;
			virtual bool copyTo(ITexture *pTex, bool convertFormatIfNeed = true) override;
			virtual bool copyRectTo(ITexture* pTex, core::position2di posTgt, int idx=0, core::recti* rcSrc = nullptr);
			void ReleaseTextureObjects();
			
			//! lock function
			virtual void* lock(E_TEXTURE_LOCK_MODE mode = ETLM_WRITE_ONLY, u32 mipmapLevel = 0);

			//! unlock function
			virtual void unlock();


			void runVkCopyBufferCmd(bool buf2img);

			void runTestCmd(bool buf2img);

			//! Returns original size of the texture.
			virtual const core::dimension2d<u32>& getOriginalSize() const;

			//! Returns (=size) of the texture.
			virtual const core::dimension2d<u32>& getSize() const
			{
				return TextureSize;
			}

			//! Returns 3D size of the texture (for 3D textures)
			virtual const core::vector3d<u32>& getSize3D() const
			{
				return TextureSize3D;
			}

			//! returns driver type of texture (=the driver, who created the texture)
			virtual E_DRIVER_TYPE getDriverType() const
			{
				return EDT_VK;
			}

			//! returns color format of texture
			virtual ECOLOR_FORMAT getColorFormat() const
			{
				return ColorFormat;
			}

			virtual u32 getPitch() const
			{
				return Pitch;
			}

			//! returns if texture has mipmap levels
			bool hasMipMaps() const
			{
				return _HasMipMaps;
			}

			virtual u32 getNumberOfArraySlices() const
			{
				return NumberOfArraySlices;
			}

			//! Regenerates the mip map levels of the texture. Useful after locking and
			//! modifying the texture
			virtual void regenerateMipMapLevels(void* mipmapData = 0);

			//! returns if it is a render target
			virtual bool isRenderTarget() const;
			virtual void ClearBackBuffer(SColor c = SColor(0));

			const VkCommandBuffer rtCmdBuf();

			//! returns if this is a 3D texture
			virtual bool is3DTexture() const
			{
				return _Is3DTexture;
			}

		public:

			//! return texture resource
			VkImage getTextureResource() const
			{
				return Texture;
			}
			operator VkImage() { return Texture; };

			//! return render target view
//			VkImageView getRenderTargetView() const
//			{
//				return RTView;
//			}

			//! return shader resource view
			const VkImageView &getShaderResourceView() const
			{
				// Emulate "auto" mipmap generation
				//if(_HasMipMaps  && _IsRenderTarget && SRView )	ImmediateContext->GenerateMips( SRView ); //Ч�ʴ���
				return SRView;
			}

			VkFormat getVkFormat() { return mFormat; }
#if HAS_ARCORE_SHARETEX
			AHardwareBuffer* getSysHardwareBuffer();
#endif
			VkImageLayout setVkImageLayout(VkImageLayout layout);
			void onImageLayoutChanged(VkImageLayout layout) { ImageLayout = layout; }
			VulkanPass* getVRP();
			void setHasMipMap(bool b) { _HasMipMaps = b; }

			//! Transition image layout for storage image usage (for 3D textures in compute shaders)
			void transitionLayoutForStorageAccess();

			//! Transition image layout back to shader read optimal
			void transitionLayoutForShaderRead();

		private:
#if HAS_ARCORE_SHARETEX
            AHardwareBuffer* aHardBuf{};
#endif
			void LoadTexture();
			E_TEXTURE_LOCK_MODE lastLockMode;
			friend class VkDriver;
			
			VkDevice Device;
			VkImage Texture = VK_NULL_HANDLE;
			VkDeviceMemory TexMemory = VK_NULL_HANDLE;
			//VkImageView RTView = VK_NULL_HANDLE;
			VkImageView SRView = VK_NULL_HANDLE;
			VkImageType TextureDimension = VK_IMAGE_TYPE_2D;
			VkImageUsageFlags ImageUsageFlags = VK_IMAGE_USAGE_SAMPLED_BIT;
			VkImageLayout ImageLayout = VK_IMAGE_LAYOUT_UNDEFINED;
			//E_MAP_TYPE  LastMapDirection ;
			VulkanPass* vrp = nullptr;

			VkDriver* Driver;
			SDepthSurfaceVk* DepthSurface;
			core::dimension2d<u32> TextureSize;
			core::dimension2d<u32> ImageSize;
			s32 Pitch;
			u32 NumberOfMipLevels = 1;
			u32 NumberOfArraySlices;
			ECOLOR_FORMAT ColorFormat;
			u32 SampleCount;
			u32 SampleQuality;

			VkBuffer stagingBuffer = VK_NULL_HANDLE;		// staging texture used for lock/unlock
			VkDeviceMemory stagingMemory = VK_NULL_HANDLE;

			s32 SrcPitch=0;
			u32 MipLevelLocked;
			u32 ArraySliceLocked;



			bool _HasMipMaps=false;
			bool _HardwareMipMaps=false;
			bool _IsRenderTarget = false;
			bool _isTextureArray=false;
			bool _IsSystemShared = false;
			bool useExternalFormat=false;
			//bool _IsShared;

			//rt only
			bool _UsageStoreImage = false;
			bool _ForceRGBA = false, _ForceR8 = false;
			bool _ForceTransferSrc = false;

			//! creates hardware render target
			//void createRenderTarget(const ECOLOR_FORMAT format = ECF_UNKNOWN);

			//! creates the hardware texture  --CK: gen mipmap also need RenderTarget
			bool createTexture(u32 flags, IImage * image);

			void SetVkFormat(u32 flags, IImage* image);

			//! copies the image to the texture
			bool copyFromImage(IImage* image, u32 layerId);

			//! set Pitch based on the d3d format
			void setPitch(VkFormat d3dformat);

			//! create texture buffer needed for lock/unlock
			//bool createTextureBuffer();

			//! create views to bound texture to pipeline
			bool createViews(bool bCreateRTV = true);

			//ckadd
			VkFormat mFormat= VK_FORMAT_UNDEFINED;
			bool bSRGB = true;

			uint32_t mBufferSize=0;
			io::path texName;
			bool isCube = false;

			bool _Is3DTexture = false;
			core::vector3d<u32> TextureSize3D;

		};

		void NewFunction(const uint32_t& h, char*& pb, const uint32_t& w);

		inline VkTexture* VKTEX(irr::video::ITexture* tex) { return static_cast<VkTexture*>(tex); }
}
}
		
#endif // _IRR_COMPILE_WITH_

