
#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "VkHeader.h"
#include "VkDriverBase.h"

#define SHADER_MAX_TEXTURE_NUM 128
#define SHADER_FLAG_VS			1
#define SHADER_FLAG_GS			2
#define SHADER_FLAG_PS			4

#ifdef _IRR_WINDOWS_
#define WIN32_LEAN_AND_MEAN

#define _USE_MATH_DEFINES




#include <tchar.h>
#include <strsafe.h>


#define USE_UNWORK 0 //not work



#ifdef _WIN64
#define TEMPMATRIX 
#else
#define TEMPMATRIX matrix
#endif

//
//#include <d3dx10math.h>


// #include "IrrD3D11Material.h"
// #include "IrrD3D11MaterialNormalMap.h"


#else	//metro

#endif

#include <memory>
#include <map>

namespace irr
{


	namespace io
	{
		class IWriteFile;
	} 
	namespace gui { class CGUIFontDW;}
namespace video
{
	struct CbMr2D;
	class VkMr2D;
	class VkVertexDeclaration;

	struct SDepthSurfaceVk : public IReferenceCounted
	{
		SDepthSurfaceVk(VkDevice device) : Surface(0), Device(device)
		{
			#ifdef _DEBUG
			setDebugName("SDepthSurface");
			#endif
		}
		virtual ~SDepthSurfaceVk()
		{
			vkDestroyImageView(Device, Surface, nullptr);
		}

		VkImageView Surface;
		VkDevice Device;
		core::dimension2du Size;
	};

	struct TMatrixData
	{
		float4x4 xmV,xmVt,xmP,xmPt;//,m_xPit;,xmVit
	};

	struct ShaderMaterialParam
	{
		c8 *vsStr = nullptr, *gsStr = nullptr, *psStr = nullptr;
		c8 *vsEntry = nullptr, *gsEntry = nullptr, *psEntry = nullptr;
		c8 *vsTypeVer = nullptr, *gsTypeVer = nullptr, *psTypeVer = nullptr;
		unsigned char	*vsCso = nullptr, *gsCso = nullptr, *psCso = nullptr;
		unsigned int	vsLen = 0, gsLen = 0, psLen = 0;
		scene::E_PRIMITIVE_TYPE inType = scene::EPT_TRIANGLES;
		scene::E_PRIMITIVE_TYPE outType = scene::EPT_TRIANGLE_STRIP;
		u32 verticesOut = 0;
		IShaderConstantSetCallBack* callback = 0;
		E_MATERIAL_TYPE baseMaterial = video::EMT_SOLID;
		s32 userData = 0;


			//int streamOut;
			//E_VERTEX_TYPE vertexTypeOut = EVT_STANDARD;
	};



	class VkDriver : public VkDriverBase
	{
	public:

		friend class VkTexture;
		friend class VkMaterialRenderer;
		friend class VkFixedFunctionMaterialRenderer;
		friend class VkMr2D;
		friend class gui::CGUIFontDW;

		//! constructor
		VkDriver(const irr::SIrrlichtCreationParameters& params,
			io::IFileSystem* io);

		//! destructor
		virtual ~VkDriver();


		//! applications must call this method before performing any rendering. returns false if failed.
		virtual bool beginScene(bool backBuffer=true, bool zBuffer=true,
				SColor color=SColor(255,0,0,0),
				const SExposedVideoData& videoData=SExposedVideoData(),
				core::rect<s32>* sourceRect=0);

		void copyLastFrame(irr::video::ITexture *texTgt=nullptr);

		virtual void resetFrameCache() override;

		void beginRenderPass(VkClearColorValue *clearColor = nullptr);
		virtual void beginPass(const PassParam& ppm) override;
		void endRenderPass();
		virtual int present() override;
		//! applications must call this method after performing any rendering. returns false if failed.
		virtual bool endScene();
		virtual void waitDriverIdle(u32 what = 0);
		void UploadSharedBuffers();



		//! queries the features of the driver, returns true if feature is available
		virtual bool queryFeature(E_VIDEO_DRIVER_FEATURE feature) const;

		//! sets transformation
		virtual void setTransform(E_TRANSFORMATION_STATE state, const core::matrix4& mat);

		//! sets a material
		virtual void setMaterial(const SMaterial& material);
		virtual void setMaterialPara(int paraId, const SMaterial& material) override {
			if (paraId < 0) setMaterial(material);
			else {
				int tid = paraId % threadPool.getThreadCount();
				CurrentMaterialPara[tid] = material;
			}
		}
		virtual const SMaterial& getMaterialPara(int paraId) override {
			if (paraId < 0) return Material;
			int tid = paraId % threadPool.getThreadCount();
			return CurrentMaterialPara[tid];
		}

		//! sets a render target
		virtual bool setRenderTarget(video::ITexture* texture,
			bool clearBackBuffer=true, bool clearZBuffer=true,
			SColor color=video::SColor(0,0,0,0), bool multiThreadRender=false) override;

		//! Sets multiple render targets
		virtual bool setRenderTarget(const core::array<video::IRenderTarget>& texture,
			bool clearBackBuffer=true, bool clearZBuffer=true,
			SColor color=video::SColor(0,0,0,0));

		//! sets a viewport
		virtual void setViewPort(const core::rect<s32>& area);

		//! gets the area of the current viewport
		virtual const core::rect<s32>& getViewPort() const;

		struct SHWBufferLink_VK : public SHWBufferLink
		{
			SHWBufferLink_VK(const scene::IMeshBuffer *_MeshBuffer):
				SHWBufferLink(_MeshBuffer),
					vertexBuffer(0), indexBuffer(0), //vertexBuffer2(0),
					vertexBufferSize(0), indexBufferSize(0)//  vertexBufferSize2(0)
			{}

			VkHardwareBuffer* vertexBuffer;
			//VkHardwareBuffer* vertexBuffer2;
			VkHardwareBuffer* indexBuffer;

			u32 vertexBufferSize;
			u32 vertexBufferSize2;
            u32 indexBufferSize;
 		};

		bool updateVertexHardwareBuffer(SHWBufferLink_VK *HWBuffer);
		bool updateIndexHardwareBuffer(SHWBufferLink_VK *HWBuffer);

		//! updates hardware buffer if needed
		virtual bool updateHardwareBuffer(SHWBufferLink *HWBuffer);

		//! Create hardware buffer from mesh
		virtual SHWBufferLink *createHardwareBuffer(const scene::IMeshBuffer* mb);

		//! Delete hardware buffer (only some drivers can)
		virtual void deleteHardwareBuffer(SHWBufferLink *HWBuffer);
		
		//! is vbo recommended on this mesh? for DirectX 11 ALWAYS YES!!!!!!!!!!!
		// DirectX 11 doesn't use methods like drawPrimitiveUp (DX9) or glVertex (OpenGL)
		//virtual bool isHardwareBufferRecommend(const scene::IMeshBuffer* mb) { return true; }

		//! Draw hardware buffer
		virtual void drawHardwareBuffer(SHWBufferLink *HWBuffer);

		//! draw
		virtual void drawHardwareBuffer(IHardwareBuffer* vertices,
			IHardwareBuffer* indices, E_VERTEX_TYPE vType = EVT_STANDARD,
			scene::E_PRIMITIVE_TYPE pType = scene::EPT_TRIANGLES,
			E_INDEX_TYPE iType = EIT_16BIT, u32 numInstances = 0, u32 numDraw = 0, u32 firstIdx = 0) override;

		void drawHardwareBufferIndirect(
			IHardwareBuffer* vertices,
			IHardwareBuffer* indices, 
			IHardwareBuffer* paramBuf, u32 paramBufOffset, u32 paramBufStride,
			E_VERTEX_TYPE vType,
			scene::E_PRIMITIVE_TYPE pType, E_INDEX_TYPE iType);

		virtual void drawHardwareBufferParallel(int paraId, IHardwareBuffer* vertices,
			IHardwareBuffer* indices, E_VERTEX_TYPE vType = EVT_STANDARD,
			scene::E_PRIMITIVE_TYPE pType = scene::EPT_TRIANGLES,
			E_INDEX_TYPE iType = EIT_16BIT, u32 numInstances = 0, u32 numDraw = 0, u32 firstIdx = 0) override;

		virtual void drawInstancedHardwareBuffer(IHardwareBuffer* vertices,
			IHardwareBuffer* indices,
			IHardwareBuffer* instanceBuffer,
			E_VERTEX_TYPE vertexType,
			scene::E_PRIMITIVE_TYPE primType,
			E_INDEX_TYPE indexType,
			u32 instanceCount,
			u32 indexCount,
			u32 startIndex = 0) override;

		//! Draw automatically using stream output buffers
		virtual void drawAuto(IHardwareBuffer* vertices, E_VERTEX_TYPE vType = EVT_STANDARD, 
				scene::E_PRIMITIVE_TYPE pType = scene::EPT_TRIANGLES, u32 ofs = 0);

		//! draws a vertex primitive list
		virtual void drawVertexPrimitiveList(const void* vertices, u32 vertexCount,
				const void* indexList, u32 primitiveCount,
				E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
				E_INDEX_TYPE iType);

#if 0//VK_USE_2D_FX_RENDERER
		virtual void drawHardwareBuffer2D(IHardwareBuffer* vertices,
			IHardwareBuffer* indices, E_VERTEX_TYPE vType = EVT_STANDARD,
			scene::E_PRIMITIVE_TYPE pType = scene::EPT_TRIANGLES,
			E_INDEX_TYPE iType = EIT_16BIT, u32 numInstances = 0, u32 ofs = 0);
#endif
		//! draws a vertex primitive list in 2d
		void draw2DVertexPrimitiveListFast(const void* vertices, u32 vertexCount,
				const void* indexList, u32 primitiveCount,
				E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
				E_INDEX_TYPE iType, bool isSimpleImage=false);

		//! draws an 2d image, using a color (if color is other then Color(255,255,255,255)) and the alpha channel of the texture if wanted.
		virtual void draw2DImage(const video::ITexture* texture, const core::position2d<s32>& destPos,
			const core::rect<s32>& sourceRect, const core::rect<s32>* clipRect = 0,
			SColor color=SColor(255,255,255,255), bool useAlphaChannelOfTexture= false, int rotateDegree = 0) override;

		//! Draws a part of the texture into the rectangle.
		virtual void draw2DImageRect(const video::ITexture* texture, const core::rect<s32>& destRect,
			const core::rect<s32>& sourceRect, const core::rect<s32>* clipRect = 0,
			const video::SColor* const colors=0, bool useAlphaChannelOfTexture= false, int rotateDegree = 0) override ;


		//! Draws a set of 2d images, using a color and the alpha channel of the texture.
		virtual void draw2DImageBatch(const video::ITexture* texture,
				const core::array<core::position2d<s32> >& positions,
				const core::array<core::rect<s32> >& sourceRects,
				const core::rect<s32>* clipRect=0,
				SColor color=SColor(255,255,255,255),
				bool useAlphaChannelOfTexture=false);

		//!Draws an 2d rectangle with a gradient.
		virtual void draw2DRectangle(const core::rect<s32>& pos,
			SColor colorLeftUp, SColor colorRightUp, SColor colorLeftDown, SColor colorRightDown,
			const core::rect<s32>* clip);

		//! Draws a 2d line.
		virtual void draw2DLine(const core::position2d<s32>& start,
					const core::position2d<s32>& end,
					SColor color=SColor(255,255,255,255));

		//! Draws a pixel.
		virtual void drawPixel(u32 x, u32 y, const SColor & color);

		//! Draws a 3d line.
		virtual void draw3DLine(const core::vector3df& start,
			const core::vector3df& end, SColor color = SColor(255,255,255,255));

		//! initialises the Direct3D API
		bool initDriver(const core::dimension2d<u32>& screenSize, HWND hwnd,
				u32 bits, bool fullScreen, bool pureSoftware,
				bool highPrecisionFPU, bool vsync, u8 antiAlias);


		void UpdateExternalDeviceInfo();

		//! \return Returns the name of the video driver. Example: In case of the DIRECT3D8
		//! driver, it would return "Direct3D8.1".
		virtual const wchar_t* getName() const;

		//! deletes all dynamic lights there are
		virtual void deleteAllDynamicLights();

		//! adds a dynamic light, returning an index to the light
		//! \param light: the light data to use to create the light
		//! \return An index to the light, or -1 if an error occurs
		virtual s32 addDynamicLight(const SLight& light);

		//! Turns a dynamic light on or off
		//! \param lightIndex: the index returned by addDynamicLight
		//! \param turnOn: true to turn the light on, false to turn it off
		virtual void turnLightOn(s32 lightIndex, bool turnOn);

		//! returns the maximal amount of dynamic lights the device can handle
		virtual u32 getMaximalDynamicLightAmount() const;

		//! Sets the dynamic ambient light color. The default color is
		//! (0,0,0,0) which means it is dark.
		//! \param color: New color of the ambient light.
		virtual void setAmbientLight(const SColorf& color);

		//! Draws a shadow volume into the stencil buffer.
		virtual void drawStencilShadowVolume(const core::vector3df* triangles, s32 count, bool zfail);

		//! Fills the stencil shadow with color.
		virtual void drawStencilShadow(bool clearStencilBuffer=false,
			video::SColor leftUpEdge = video::SColor(0,0,0,0),
			video::SColor rightUpEdge = video::SColor(0,0,0,0),
			video::SColor leftDownEdge = video::SColor(0,0,0,0),
			video::SColor rightDownEdge = video::SColor(0,0,0,0));

		//! Returns the maximum amount of primitives (mostly vertices) which
		//! the device is able to render with one drawIndexedTriangleList
		//! call.
		virtual u32 getMaximalPrimitiveCount() const;

		//! Enables or disables a texture creation flag.
		virtual bool setTextureCreationFlag(E_TEXTURE_CREATION_FLAG flag, bool enabled);

		//! Sets the fog mode.
		virtual void setFog(SColor color, E_FOG_TYPE fogType, f32 start,
			f32 end, f32 density, bool pixelFog, bool rangeFog);

		//! Only used by the internal engine. Used to notify the driver that
		//! the window was resized.
		virtual void OnResize(const core::dimension2d<u32>& size);
		virtual void OnResetWindow(void* hwnd);
		//! Can be called by an IMaterialRenderer to make its work easier.
		virtual void setBasicRenderStates(const SMaterial& material, const SMaterial& lastMaterial,
			bool resetAllRenderstates);

		//! Returns type of video driver
		virtual E_DRIVER_TYPE getDriverType() const;

		//! Returns the transformation set by setTransform
		virtual const core::matrix4& getTransform(E_TRANSFORMATION_STATE state) const;

		//! Sets a vertex shader constant.
		virtual void setVertexShaderConstant(const f32* data, s32 startRegister, s32 constantAmount=1);

		//! Sets a pixel shader constant.
		virtual void setPixelShaderConstant(const f32* data, s32 startRegister, s32 constantAmount=1);

		//! Sets a constant for the vertex shader based on a name.
		virtual bool setVertexShaderConstant(const c8* name, const f32* floats, int count);

		//! Sets a constant for the pixel shader based on a name.
		virtual bool setPixelShaderConstant(const c8* name, const f32* floats, int count);


	//! Int interface for the above.
		virtual bool setVertexShaderConstant(const c8* name, const s32* ints, int count) {return true;};


	//! Int interface for the above.
		virtual bool setPixelShaderConstant(const c8* name, const s32* ints, int count) {return true;};
			//! Bool interface for the above.
	virtual bool setVertexShaderConstant(const c8* name, const bool* bools, int count) {return true;};

	//! Bool interface for the above.
	virtual bool setPixelShaderConstant(const c8* name, const bool* bools, int count){return true;};


		//! Set hardware buffer for stream output stage
		virtual bool setStreamOutputBuffer(IHardwareBuffer* buffer,u32 ofs=0);

		//! Returns a pointer to the IVideoDriver interface. (Implementation for
		//! IMaterialRendererServices)
		virtual IVideoDriver* getVideoDriver();

		//! Creates a render target texture.
		virtual ITexture* addRenderTargetTexture(const core::dimension2d<u32>& size,
				const io::path& name, const ECOLOR_FORMAT format = ECF_UNKNOWN,
				u32 sampleCount = 1, u32 sampleQuality = 0, u32 arraySlices = 1);

		//! Creates a 3D render target texture for volume rendering
		virtual ITexture* addRenderTargetTexture3D(const core::vector3d<u32>& size3d,
				const io::path& name = "rt3d", const ECOLOR_FORMAT format = ECF_UNKNOWN,
				u32 sampleCount = 1, u32 sampleQuality = 0);

		//! Creates a 3D texture for volume data
		virtual ITexture* addTexture3D(const core::vector3d<u32>& size3d,
				const io::path& name, const ECOLOR_FORMAT format = ECF_UNKNOWN);

		//! Clears the ZBuffer.
		virtual void clearZBuffer();

		//! Returns an image created from the last rendered frame.
		virtual IImage* createScreenShot(video::ECOLOR_FORMAT format, video::E_RENDER_TARGET target);

		ITexture* createFrameBufCopy( );
		void frameBufCopyFrom(ITexture* tex);
		//! Set/unset a clipping plane.
		virtual bool setClipPlane(u32 index, const core::plane3df& plane, bool enable=false);

		//! Enable/disable a clipping plane.
		virtual void enableClipPlane(u32 index, bool enable);

		//! Used by VkMaterialRenderer to get clip plane and status
		virtual void getClipPlane(u32 index, core::plane3df& plane, bool& enable);

		//! Returns the graphics card vendor name.
		virtual core::stringc getVendorInfo() {return VendorName;}

		//! Enable the 2d override material
		virtual void enableMaterial2D(bool enable=true);

		//! Get the current color format of the color buffer
		/** \return Color format of the color buffer. */
		virtual ECOLOR_FORMAT getColorFormat() const;

		//! Get the current color format of the color buffer
		/** \return Color format of the color buffer as D3D color value. */
		VkFormat getVkScColorFormat() const;

		//! Returns the maximum texture size supported.
		virtual core::dimension2du getMaxTextureSize() const;

		//! Get D3D color format from Irrlicht color format.
		VkFormat getDeviceFormatFromColorFormat(ECOLOR_FORMAT format) const;

		ECOLOR_FORMAT getColorFormatFromDeviceFormat(VkFormat format) const;


		//! Get index format from type
		VkFormat getIndexType(E_INDEX_TYPE iType) const;

		//! query support for color format  USE  Diligent::IRenderDevice::GetTextureFormatInfoExt(format)
		//bool querySupportForColorFormat(VkFormat format, D3D11_FORMAT_SUPPORT support);

		//! return feature level
		//! this is needed for material renderers to select correct shader profiles
		const VkPhysicalDeviceFeatures& getDeviceCaps() { return deviceFeatures; }

		//! Get primitive topology
		VkPrimitiveTopology getTopology(scene::E_PRIMITIVE_TYPE primType) const;


		//! Get number of indices
		u32 getIndexAmount(scene::E_PRIMITIVE_TYPE primType, u32 primitiveCount) const;



	//	bool isHardware() const { return DriverType == D3D_DRIVER_TYPE_HARDWARE; }

		VkVertexDeclaration* getVertexDeclaration(E_VERTEX_TYPE vType) const;

		//! Create a implementation dependant hardware buffer
		virtual IHardwareBuffer* createHardwareBuffer(E_HARDWARE_BUFFER_TYPE type, E_HARDWARE_BUFFER_ACCESS accessType, 
							u32 size, u32 flags = 0, const void* initialData = 0);

		//! Register a new vertex type
		virtual E_VERTEX_TYPE registerVertexType(core::array<SVertexElement>& elements);

		//! Check multisample quality levels
		virtual u32 queryMultisampleLevels(ECOLOR_FORMAT format, u32 numSamples) const;


		bool setActiveTexture(u32 stage, const video::ITexture* texture);

		virtual E_MATERIAL_TYPE get2DNativeMaterialType(Fx2DIdEnum fid) _IRR_OVERRIDE_;
#if 0
		//ckadd
		void ShaderSetTextures(u32 ShaderFlag,ITexture** ppTex,u32 start, u32 size);


#endif


		io::IFileSystem *getFileSystem(){return FileSystem;};
		void draw2DImage2Tex(const video::ITexture* texture, const video::ITexture* texture2, E_MATERIAL_TYPE mt, 
			const core::rect<s32>& destRect, const core::rect<s32>* sourceRect=0, const core::rect<s32>* clipRect=0, const video::SColor* const colors=0, bool useAlphaChannelOfTexture= false);

		SHWBufferLink_VK *getBufferLinkVk(const scene::IMeshBuffer* mb){return static_cast<SHWBufferLink_VK *>(getBufferLink(mb));}
		TMatrixData *md;
		virtual ITexture* getRenderTarget();
		virtual void UpdateDataPerView();;		//Constant Buffer Per Frame
#if VK_USE_2D_FX_RENDERER		
		//image processing 
		void DrawMrTexToTexture(ITexture *tgtTex,const SMaterial &mt,bool clearBuffer=false,bool clearZBuffer=false);
		virtual void draw2DImageMr(const SMaterial &mr, const core::rect<s32>& destRect, const core::rect<s32>& sourceRect, const core::rect<s32>* clipRect, const video::SColor* const colors, bool invY );
		void draw2DSubPassMr(const SMaterial& mr);
		void draw2DImageBatchMr(const SMaterial &mr, const core::array<core::position2d<s32> >& positions, const core::rect<s32> & sourceRects, const core::rect<s32>* clipRect, SColor color, bool useAlphaChannelOfTexture);
#endif


		//void SetBackBufferCopyTexture(ITexture *tex){mStaticBgTex=tex;}
		irr::video::VkHardwareBuffer *testHWBUFIdx{}, *testHWBUFVtx{};

public:// original private
	const ITexture* CurrentTexture[8];	
	E_MATERIAL_TYPE mt2DAlpha = EMT_FORCE_32BIT, mt2DVertexAlpha = EMT_FORCE_32BIT, mt2DVertexColor = EMT_FORCE_32BIT, mt2DVertexColor2a = EMT_FORCE_32BIT,
		mt2DNormalMap = EMT_FORCE_32BIT, mt2DTemplate = EMT_FORCE_32BIT , mt2DToneMapping = EMT_FORCE_32BIT;// , mt2DModHSL, mt2DRGB2HSL, mt2DHSL2RGB;
	//Diligent::ISampler         *m_samplerBiLinear=nullptr,	*m_samplerTriLinear = nullptr, *m_samplerPoint = nullptr;

	irr::video::VkTexture* texDummyRT{};// , * texDummyF16RT{};

	bool prepared = false;

	//Water reflection
	PITex  texDuDv[2]{}, texMrRT[2]{}, texCube[6]{};
	void makesureDuDv(int viewId,ECOLOR_FORMAT cf , irr::core::dimension2du rtSize  );;
	bool makesureCubeSphere();
	//VkImageView DefaultBackBuffer = VK_NULL_HANDLE;
	//VkImageView DefaultDepthBuffer = VK_NULL_HANDLE;

	//CKADD
#if VK_USE_2D_FX_RENDERER
	virtual E_MATERIAL_TYPE add2DMaterialType(Fx2DIdEnum fid, const c8* name) ;
	void draw2DPointLists(const void* vertices,int n);
	void draw2DImageKaleido(const video::ITexture* texture, const core::position2d<s32>& pos,float rad=0.f,float scale=1.0f );
	void SetNoForceSampleCount();

#endif
	virtual void flush() override;
	//misc
	VK_EXTERNAL_DEVICE_INFO* getExtDevInfo();
	virtual void frameViewReset() override;
	virtual bool frameViewNext() override;

#ifdef _IRR_COMPILE_WITH_GUI_
	ITexture* _guiTexture,
#endif


 
	//void drawGuiLayer(const video::ITexture* texture);
	bool _bUpdatePerFrame;
	VkTexture *mPickingTex;

	//Screen Shot
	VkImage ssDstImg= VK_NULL_HANDLE;
	VkDeviceMemory ssDstImgMem= VK_NULL_HANDLE;
	u32 ssWidth =0;
	u32 ssHeight =0;


#if IRR_HAS_OIT

	VkPipeline mPlOitR = VK_NULL_HANDLE;
	IHardwareBuffer* mCbOitCount{}, * mCbOitData{};
	irr::video::VkTexture* pTexOitCount{};
	int ccTexOitCount = 0;
	VkDescriptorImageInfo mTexDescOitCountImg{};
#endif

	SMaterial Material, LastMaterial;
	// Storing vertex declarations
	typedef core::map<E_VERTEX_TYPE, VkVertexDeclaration*> DeclarationMap;
	typedef core::map<E_VERTEX_TYPE, VkVertexDeclaration*>::Iterator DeclarationIterator;
	typedef core::map<E_VERTEX_TYPE, VkVertexDeclaration*>::Node* DeclarationNode;
	const DeclarationMap& getDeclarationMap() { return declarationMap; }

	const bool& getVSync() { return VSync; }
	int toggleMultiPass();
	void createTexCopy();
	irr::video::VkTexture* texFrameCopy{};

	void addWaitFence(VkFence fence)
	{
		mFencesToWait.push_back(fence);
	}
	void waitAddedFences() {
		if (mFencesToWait.size() > 0)
		{
			vkWaitForFences(Device, (uint32_t)mFencesToWait.size(), mFencesToWait.data(), VK_TRUE, DEFAULT_FENCE_TIMEOUT);
			mFencesToWait.clear();
		}
	}
	DeclarationMap declarationMap;
private:
		// enumeration for rendering modes such as 2d and 3d for minizing the switching of renderStates.
		enum E_RENDER_MODE
		{
			ERM_2D = 0,		// 2d drawing rendermode
			ERM_3D,			// 3d rendering mode
		};
		E_RENDER_MODE CurrentRenderMode;


		bool ResetRenderStates,bSettedMaterial; // bool to make all renderstates be reseted if set.

		bool StencilBuffer;
		bool bLastIsSimpleImage; //speed up 2d image rendering
		u8 AntiAliasing;
		bool Transformation3DChanged;
		
							// 1x1 texture replacement for NULL textures in materials
		core::matrix4 Matrices[ETS_COUNT];			// matrizes of the 3d mode we need to restore when we switch back from the 2d mode.



		uu::CVtxIdxCache mVICache;
		// Buffers for dynamic data
		VkHardwareBuffer *DynVertexBuffer{};// , _vb2D = VK_NULL_HANDLE;
		VkHardwareBuffer *DynIndexBuffer{};// , _ib2D = VK_NULL_HANDLE;

		bool UseOIT=false;

		std::vector<VkFence> mFencesToWait;

		
		
		// Input layout
		//Diligent::InputLayoutDesc* CurrentInputLayout;		// store current to prevent set multiple times the same layout
		
		/*
		D3D11_INPUT_ELEMENT_DESC StandardInputElements[4];
		D3D11_INPUT_ELEMENT_DESC TwoCoordsInputElements[5];
		D3D11_INPUT_ELEMENT_DESC TangentsInputElements[6];
		struct LayoutKey
		{
			D3D11_INPUT_ELEMENT_DESC* elementDesc;
			void* pInputSignature;

			// shall define these operators for this structure
			inline bool operator==(const LayoutKey& other) const
			{
				return ( ( elementDesc == other.elementDesc ) && 
						 ( pInputSignature == other.pInputSignature ) );
			}

			inline bool operator!=(const LayoutKey& other) const
			{
				return ( ( elementDesc != other.elementDesc ) || 
						 ( pInputSignature != other.pInputSignature ) );
			}

			inline bool operator<(const LayoutKey& other) const
			{
				return ( ( elementDesc < other.elementDesc ) || 
						 ( ( elementDesc == other.elementDesc ) && ( pInputSignature < other.pInputSignature ) ) );
			}
		};
		typedef core::map<LayoutKey, ID3D11InputLayout*> InputLayoutStateMap;
		InputLayoutStateMap InputLayoutMap;
		*/

		// States map: due to need of comparison operators, will use wrappers for comparison

		core::dimension2d<u32> CurrentRendertargetSize;
		//core::dimension2d<u32> CurrentDepthBufferSize;
#if USE_UNWORK
		// Just one clip plane for now
		core::array<core::plane3df> ClipPlanes;
		bool ClipPlaneEnabled[3];
#endif

		core::rect<s32>* SceneSourceRect;

		E_VERTEX_TYPE LastVertexType;

		//! All the lights that have been requested; a hardware limited
		//! number of them will be used at once.
		struct RequestedLight
		{
			RequestedLight(SLight const & lightData)
				: LightData(lightData), HardwareLightIndex(-1), DesireToBeOn(true) { }

			SLight	LightData;
			s32	HardwareLightIndex; // GL_LIGHT0 - GL_LIGHT7
			bool	DesireToBeOn;
		};
		core::array<RequestedLight> RequestedLights;
		SColorf AmbientLight;
		u32 MaxActiveLights;

		core::stringc VendorName;
		u16 VendorID;

		core::array<SDepthSurfaceVk*> DepthBuffers;
		SMaterial CurrentMaterialPara[32];

		u32 MaxTextureUnits;
		u32 MaxUserClipPlanes;
		f32 MaxLightDistance;
		s32 LastSetLight;
		
		ECOLOR_FORMAT ColorFormat;
		VkFormat D3DColorFormat = IRR_SC_BUFFER_DXGI_FORMAT_SRGB;
		VkFormat DepthStencilFormat;		// Best format for depth stencil

		bool Fullscreen;
		bool AlphaToCoverageSupport;
		bool VSync;
		int lastFrameBufferId = -1;

		int frameCC = 0;

		VkDriver* baseDriver{};
		;
		//! Adds a new material renderer to the VideoDriver, based on a high level shading
		//! language.
		virtual s32 addHighLevelShaderMaterial(
			const c8* vertexShaderProgram,
			const c8* vertexShaderEntryPointName,
			E_VERTEX_SHADER_TYPE vsCompileTarget,
			const c8* pixelShaderProgram,
			const c8* pixelShaderEntryPointName,
			E_PIXEL_SHADER_TYPE psCompileTarget,
			const c8* geometryShaderProgram,
			const c8* geometryShaderEntryPointName = "main",
			E_GEOMETRY_SHADER_TYPE gsCompileTarget = EGST_GS_4_0,
			scene::E_PRIMITIVE_TYPE inType = scene::EPT_TRIANGLES,
			scene::E_PRIMITIVE_TYPE outType = scene::EPT_TRIANGLE_STRIP,
			u32 verticesOut = 0,
			IShaderConstantSetCallBack* callback = 0,
			E_MATERIAL_TYPE baseMaterial = video::EMT_SOLID,
			s32 userData=0, E_GPU_SHADING_LANGUAGE shadingLang = EGSL_DEFAULT) _IRR_OVERRIDE_{
			throw;//IRRLOG(("\"setVertexShaderConstant\" with offset is not supported", ELL_ERROR));
			return 0;}
		s32 addShaderMaterial(ShaderMaterialParam pm);

		void createMaterialRenderers();

		void prepareFrame();

		void submitFrame();
		

		void draw2D3DVertexPrimitiveList(const void* vertices,
				u32 vertexCount, const void* indexList, u32 primitiveCount,
				E_VERTEX_TYPE vType, scene::E_PRIMITIVE_TYPE pType,
				E_INDEX_TYPE iType, bool is3D);

		//Diligent::TEXTURE_ADDRESS_MODE getTextureWrapMode(const u8 clamp);

		//! sets the needed renderstates
		bool setRenderStates3DMode(int paraId,E_VERTEX_TYPE vType);

		//! sets the needed renderstates
		//bool setRenderStates2DMode(bool alpha, bool texture, bool alphaChannel);

	

		//! returns a device dependent texture from a software surface (IImage)
		//! THIS METHOD HAS TO BE OVERRIDDEN BY DERIVED DRIVERS WITH OWN TEXTURES
		virtual video::ITexture* createDeviceDependentTexture(IImage* surface, const io::path& name, void* mipmapData=0);

		//  
		virtual video::ITexture* createTextureArray(const std::vector<IImage*>& images, const io::path& name);

		//! Check if a proper depth buffer for the RTT is available, otherwise create it.
		void checkDepthBuffer(ITexture* tex);

		// removes the depth struct from the DepthSurface array
		void removeDepthSurface(SDepthSurfaceVk* depth);

		// creates a depth buffer view  //DG get from SC
		//ID3D11DepthStencilView* createDepthStencilView(core::dimension2d<u32> size);

		//! returns the current size of the screen or rendertarget
		virtual const core::dimension2d<u32>& getCurrentRenderTargetSize() const;

		//! set input vertex layout
		//virtual bool setInputLayout( video::E_VERTEX_TYPE vertexType, IMaterialRenderer* renderer );


		//! reallocate dynamic buffers
		virtual bool reallocateDynamicBuffers( u32 vertexBufferSize, u32 indexBufferSize );

		//! upload dynamic vertex and index data to GPU
		virtual bool uploadVertexData(const void* vertices, u32 vertexCount,
									  const void* indexList, u32 indexCount,
									  E_VERTEX_TYPE vType, E_INDEX_TYPE iType);

		//! handle screen resize
		void resetSwapchain();

		void recreateOitScrBuf();

		virtual void beginOIT();
		virtual void endOIT();

		// Mr2Ds
		public:
			irr::video::E_MATERIAL_TYPE getMT(irr::video::Fx2DIdEnum fx);

			irr::video::CbMr2D* getCB(irr::video::Fx2DIdEnum fx);

			irr::video::VkMr2D* getRenderer(irr::video::Fx2DIdEnum fx);
		private:
			std::map<irr::video::Fx2DIdEnum, irr::video::E_MATERIAL_TYPE> mMtMap;


		void drawHbInternal(const IHardwareBuffer *vertices, const IHardwareBuffer *indices,
							E_VERTEX_TYPE &vType, E_INDEX_TYPE &iType, u32 numInstances,
							u32 numDraw,
							u32 firstIdx, int paraId);

		//Pick
		SMaterial MrPickPoint, MrPickPointDrawPt;
	};
	
}
extern video::VkDriver* g_irrDrv11;
}
#define DRV_GET_MT(drv,fxId) ((VkDriver*)drv)->getMT(fxId)
#define VKDRV_CAST(drv) ((VkDriver*)drv)
#endif // _IRR_COMPILE_WITH_
