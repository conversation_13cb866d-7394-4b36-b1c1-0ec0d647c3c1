﻿#include "AppGlobal.h"
#include "irrSaba.h"
//AMP  single thread render  31fps 2ms 10 xl.zip  paused=770fps

#include <thread>
#include <atomic>
#include <memory>
#include <vector>
#include <string>
#include <iostream>
#include <filesystem>
namespace fs = std::filesystem;
#include <map>

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/matrix_decompose.hpp>
#include <glm/gtx/euler_angles.hpp>
#include <glm/gtx/vector_angle.hpp>

#include <stlUtils.h>
#include <Saba/Model/MMD/MMDNode.h>
#include <Saba/Model/MMD/MMDModel.h>
#include <Saba/Model/MMD/MMDPhysics.h>
#include <Saba/Base/File.h>
#include "cppIncDefine.h"
#include <UaUtils.h>
#include <helpers/UpUtils.h>
#include <irrfw/eqv/EQV.h>
#include "LeapMan.h"
//#include <DirectXMath/Inc/DirectXCollision.inl>
#include "IrrFw/SnGuQin.h"
#include "IrrMMD.h"
#include "vulkanrenderer/VkMr2D.h"
#include <saba/src/Saba/Model/MMD/VPDFile.h>
#include "IrrFw/SvgMan.h"
#include "VulkanRenderer/VkMrFF_MMD.h"
#include "../../AppMainLib/app/ArMmPLayer/SnArItem.h"
#include "VideoHelpers.h"
#include <regex>
#include "PhyObjMan.h"
#include "PhysicsHelper.h"
#include "MmdPhysicsHelper.h"
#include "MmdPhyAnimator.h"
#define ALLOW_GPU	1//(!MMD_TREE_FW && 1)// NO VTX FW

#define CENTER_FORCE_INIT		0

#define LOOKAT_CAM_TORQUE		10
#define LOOKAT_CAM_TORQUE_EYE	20
#define LOOKAT_CAM_TORQUE_ALL	0
#define LOOKAT_SB_TORQUE			10
#define FW_SOUND		"hitFw"//"hitFwSound"//
#define INIT_POS   0,0,0// 0,3.75,0

#define HAS_INFLATE				1


#define MMD_USE_LEAP			USE_LEAP
#define DISABLE_LEAP_ONSTART	0
#define LEAP_HAND_MOVE_RB		(!MMD_PMX_HAND && 0 )


#define MMD_FW 1
#define JET_FW 1
#define NODE_SPD_FW		0		//手脚烟花

#define MOUTH_FW 0
#define MMD_IRRMTR   IRR_MTR_SSAO
#define SD_OPEN_POSE MMD_CONTROL_SD
#define MMD_CYCLE_START		   -1
#define MMD_LOCK_ROOT_FRAME_ID  -1// 60//
#define MMD_FORCE_2SIDE   0//IRR_DRAW_MIRROR

//表情
#define MORPH_EYE_TIMELY		1	//MMD_PLAY_GUQIN
#define MORPTH_XIAO_CONTROL		MMD_VIRTUAL_SINGER //xiaoRat
#define MOUTH_FX_ANIM			1
#define SID_TIMER_S 0.1f
#define DMY_WIDTH 2.f
#define MOD_TEXFU 0
#define EQV_TEXFU 0
#define SHOW_INNER 0

#define SVG_DRAW_NORMCAM  (!USE_AR_DATA  )  //fpv

#define MMD_FOV_MUL 1
#define CAM_ON_HAND  0//MMD_HAND_CAM
#define CAM_ON_HEAD 0
#define MOUTH_MORPH_MUL100  1//100//120 //0=off
#define MMD_MOUTH_A_ON_RATIO 0

#define lookDis 100000//35
#define kissDis 2//15
#define MMD_POINT_HAND_APPEND_ANIM	0
#define APPROCHING_ACTION 0
#define TURN_TO_CAMERA 0  // turn upper2 to face camera

#define OIT_ON			 1 //CTRL_ALT_O useOIT
#define OIT_ALPHA_LIMIT  1.99 // >1:all.  <1: part TODO:OIT在非OIT写入DEPTH之后, 2nd pass , and FF oit

#define DBG_STROKE	0
#define NO_IK		0

#ifdef _DEBUG

#define PHYSICS_FPS			300//120// 120
#define PHYSICS_FPS_RENDER	300//180// 120
#else

#ifdef __ANDROID__
#define PHYSICS_FPS  120
#else
#define PHYSICS_FPS  300
#endif
#define PHYSICS_FPS_RENDER	300
#endif

#define DBG_CUBE  0

#define PUPPET_2	1

#define LEAP_SWAP_LR  0
//SAME TO SHADER
#define MMDSD_NO_COLOR			0x00000001
#define MMDSD_IS_CAMSB			0x00000002
#define MMDSD_CTR_TRANSPARENT	0x00000010
#if IS_WIN_DBG
#define MMDFWD(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime
#else
#define MMDFWD( )
#endif
#define MMDFW(T,N,P, V, C) 		do{ static int fid = Eqv->getFwIdxByFwIdStr(T,N); mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime
#define MMDFWID(fid,P, V, C) 		do{  mmdFw(P, fid, V , C);}while(0) //FID will not change at runtime

static std::string m_resourceDir, m_mmdDir;
static bool resInited = false;
using namespace glm;
using namespace uu;
using namespace irr;
using namespace irr::scene;
using namespace irr::video;
using namespace irr::core;
using namespace saba;
using namespace ualib;
using namespace EQVisual;

static irr::scene::IrrSaba* Sb0{};
static int createCC = 0;
IrrSaba::IrrSaba(ISceneNode* parent, ISceneManager* mgr, s32 id, irrSabaParam pm)
	:CEmptySceneNode(parent, mgr, id)
{

	Pm = pm;	mmd = pm.mmd;
	Eqv = pm.eqv;
	Ctx = pm.ctx;
	modelCollisionId = pm.fcp.collisionId >= 0 ? pm.fcp.collisionId : createCC++;  //ownerid
	Driver = getSceneManager()->getVideoDriver();
	setAutomaticCulling(scene::EAC_FRUSTUM_BOX);


	CenterForce = CENTER_FORCE_INIT;
	animCycleStart = MMD_CYCLE_START;
	leapOn = MMD_USE_LEAP && !DISABLE_LEAP_ONSTART;
	//matToParentNode.setTranslation(Pm.relToParent);
	setPickData(pm.pickData);
	saba::Fs = Ctx->getFileSystem();


	if (pm.handWrite)
		nodeHandlers.push_back(std::make_shared<MmdNodeHandler_Writer>(this));
#if SD_OPEN_POSE
	nodeHandlers.push_back(ophdl = std::make_shared<MmdNodeHandler_OpenPose>(this));
#endif

	if (pm.clsType == ESabaClass::SabaBase //&& createCC==1
		)
	{
#if MMD_JOYSTICK_GAMECAST
		nodeHandlers.push_back(hdlJgc = std::make_shared<MmdNodeHandler_JoyGameCast>(this));
#elif MMD_JOYSTICK_BODY
		nodeHandlers.push_back(hdlJbd = std::make_shared<MmdNodeHandler_JoyDance>(this));
#endif
		Pm.mmd->mdplr.joySb = this;
	}


	if (!resInited)
	{
		resInited = true;
		m_resourceDir = saba::PathUtil::GetExecutablePath();
		m_resourceDir = saba::PathUtil::GetDirectoryName(m_resourceDir);
		m_resourceDir = saba::PathUtil::Combine(m_resourceDir, "resource");
		m_mmdDir =
#ifdef _WIN32
			Ctx->getFileSystem()->getWorkingDirectory() + L"/data/mmd";
#else
			Ctx->getFileSystem()->getWorkingDirectory() + "/data/mmd";
		saba::File::dataPath = Ctx->getDataFilePath("");
#endif
	}

#if MMD_PLAY_GUQIN
	showGuqin(true);

#endif

#if MMD_MTG_HANDSEAL
	adAnmId = adAnmIdLast = 1;
#endif
	this->setPassType(IrrPassType_Models, true);
	this->setPassType(IrrPassType_ShadowMap, true);
	//this->setPassType(IrrPassType_DuDv, true);
	if (IRR_MTR_SSAO) {
		assert(!ALLOW_GPU);
		this->setPassType(IrrPassType_GBuffer, true);
		this->setPassType(IrrPassType_Normal, false);
	}
	setScale(Pm.scale);
	//setRotation({ 0,180,0 });
	if (!loadModel(pm.pmxFile))
		throw "loadPmxFailed";

	bool br = loadAnimation(pm.vmdFile, false);
	assert(br);
	//ndRoot->SetAnimationRotate(vec3(90, 0, 90));	ndRoot->SetAnimationTranslate(vec3(0, 60, 0));




#ifdef _DEBUG
	setDebugName(Pm.pmxFile.c_strA());
	mdViewNode = 1;
#endif
	//Pmx->GetMMDPhysics()->senPhyDbgVisual(phyDbgView);
	//Pmx->GetMMDPhysics()->cbOnPhysicsStep = std::bind(&IrrSaba::phyOnStep, this, std::placeholders::_1, std::placeholders::_2);
	//  if (Pm.idx ==0) setScale(MMD_SABA_SCALE*vector3df(1, 0.9, 1));
	  //rootMat.setScale(100);
	//  setPosition(1200, -100, 0);

	if (pm.camVmdFile.size() > 0) {
		loadCamera(pm.camVmdFile, pm.drawCam);
	}
#if MMD_POINT_HAND_APPEND_ANIM
	loadAppendAnimation("data/mmd/fingerVmd/point5.vmd");
#endif
#if MMD_AR_BALL
	setAutomaticCulling(scene::EAC_OFF);
	snBall = SceneManager->addSphereSceneNode(1, 32, this); //snBall->setScale(50, 20, 50);
	snBall->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL);
	snBall->setMaterialDiffuse(0xFFFFFFFF);
	snBall->getMaterial(0).EmissiveColor = 0x00202020;
	snBall->setMaterialFlag(EMF_BACK_FACE_CULLING, false);
	//snBall->getMaterial(0).AmbientColor = 0xFFFFFFFF;
	snBall->getMaterial(0).SpecularColor = 0xFFFFFFFF;
	snBall->setMaterialTexture(0, Driver->getTexture("res/w2trSphere.png"));
	snBall->setVisible(false);
	snBox = SceneManager->addCubeSceneNode(0.5, this, -1, { 0,0,0 });
	snBox->setMaterialDiffuse(0xFFFF8000);

#endif
	smEdge.MaterialType = EMT_MMD_EDGE;
	smEdge.pCbCp = &smEdgeCP;

}
irr::scene::IrrSaba::~IrrSaba()
{

	//Pom->onSabaRelease(this);
	Pmx = nullptr;
	sbd = nullptr;
	freeRes();
	if (sbMpa) delete sbMpa;
	if (fwLch) delete fwLch;
	if (mcs) delete mcs;
#if MMD_ADD_WING
	delete mdRWing;	delete mdLWing;
#endif

}

void irr::scene::IrrSaba::setItemIdx(int idx)
{
	itemIdx = idx;
	Pm.mmd->addSaba(this);
	if (!Sb0) Sb0 = this;
	if (idx >= PLATE_PLAYER_SKIP)
		Pm.mmd->players.push_back(this);
	//**********************************************************************************
	//**********************************************************************************
	//**********************************************************************************

	//ndHead->SetScale(vec3(2, 2, 2));
	//if (itemIdx > 0) 		ndHead->SetScale(vec3{ 1.27 }, { 0,0.05,0 });
	if (itemIdx >= 0 && itemIdx < 16)// Pmx->GetMMDPhysics()->ownPhysicsWorld())
	{
		if (itemIdx < 8) setFxMpCallback(itemIdx);


		Eqv->cbIncVtxMat[itemIdx] = [=](EQV::TextRbGridIncVtxInfo& ivi) {
			int ret = 0; auto ppt = Eqv->GetPPT(); auto& vms = ppt->vtxMats;
			auto vfg = ivi.vfg;
			int count = vfg.grcount;
			static int cc2 = 0, bcc = 0; cc2++;
			PhyObj* o{};
			int i = int(vfg.yc * 0.43f) * vfg.xc + vfg.xc / 2, startI = i;  // Center grid first
			int cy = -1, cx = -1;
			for (int ii = 0; ii < count; ii++) {
				i = (ii + startI) % count;
				int y = i / vfg.xc, x = i % vfg.xc;
				if (vms.usedSize() < FW_VTX_MAT_MAX - 1) {
					ret = vms.alloc({});

					float r = vfg.r / MMD_SABA_SCALE * 0.9f;  static float ang[] = { 18,9,9 };//r参考值 0.9
					PhyObjParam pm;
					pm.pmxrb.m_friction = 0.5f; pm.pmxrb.m_repulsion = 0.6f;
					pm.pmxrb.m_rotateDimmer = .999f; pm.pmxrb.m_translateDimmer = .2f;
					pm.pmxrb.materialId = PHYSX_MATERIALID_TEXTFWCUBE;
					if (vfg.pmxrb) pm.pmxrb = *vfg.pmxrb;
					pm.poType = 0;
					pm.mass = ivi.vfg.baseMass * r * r * r;
					pm.size = vec3(r, r, r);
					pm.vfg = ivi.vfg;
					//pm.pmxrb.m_translateDimmer = .5f;
					pm.visible = 0;
					pm.autoKill = true; pm.timer = pm.vfg.poTimer;
					pm.camShoot = 0; pm.camFixPos = true; pm.camAngle = ang[itemIdx];
					pm.pos = vec3(ivi.ofs[i]); //ofs and vel has transformed to pos in mmd space
					if (vfg.useCamMat == 3)
						pm.vel = ivi.dir[i];
					else pm.vel = vec3(ivi.dir[i]) * (ndYao ? std::max(20.f, 2.f * glm::length(vec3(mmdLookAt) - ndYao->getGlobalPos())) : 60.f);

					//if (ivi.dir[i].getLength()>0.001f)
					//ivi.rtt = pm.rtt = ivi.dir[i].normalize().getHorizontalAngle()*DEGTORAD;
					//else ivi.rtt = pm.rtt = vec3(0);
					pm.rtt = ivi.rtt;
					static float a = 0; a += core::PI / 6;
					float ofsR = MMD_SABA_SCALE * 0.3f;

					pm.camStartOfs =  ivi.ofs[i]; vec3{ cc2 % 2 * ofsR - ofsR / 2,ofsR, 3 }; //

					//vec3{ cos(a)*ofsR, (1.0+ sin(a)) * ofsR, 300}; ;
					pm.toMMD = 0;//  pm.traceNode = std::vector({ndYao, ndOpaiL,ndOpaiR,ndHead,ndHandL,ndHandR,ndFootL,ndFootR, })[cc2%6];// true;

					pm.hasFw = 0;
					pm.fwIdx = Eqv->getFwIdxByFwIdStr(2, "poFlower");
					pm.gravityMul = 0.3f;
					pm.spdLenMul = 1.f;
					pm.tgtSb = this;
					pm.vtxMatId = ret;		pm.txt = ivi.vfg.txt; pm.key = ivi.vfg.key;
					ppt->vtxMats.dataPtr()[ret].spec = vfg.fwSpecular;
					pm.bandId = bcc++;
					u32 handFlag = (1 << ENdId::eHd);// (1 << ENdId::ehL) | (1 << ENdId::ehR);
					//pm.atkFlag = MMD_ATK_GRAB||1 ? (1 << ENdId::efL) | (1 << ENdId::efR) | (1 << ENdId::eHd) : -1; //handFlag;// (1 << ENdId::eoR) | (1 << ENdId::eoL);// 0xFFFFFFFF & (~handFlag);
					//pm.atkForceMul = 0.5f; pm.atkForceMul2 = 0.1f;
					//pm.pmxrb.m_collideMask32 = 0;


					if (pm.vfg.srcVelMode == 1) {
						if (i == startI) {
							cy = y; cx = x;
							pm.mass *= 100;
							pmt = pm; pm.hitSbTest = true;
							//pm.poType = 1;
							actAnimPm(pm.tgtSb, false, 0, pmt);
						}
						else pm.vel = pmt.vel;
						//pm.size = vec3(0.2f);
						//pm.pmxrb.m_collideMask32 = 0;
						pm.p2pInTime = (i == startI ? pmt.p2pInTime : false);
						pm.p2pTime = pmt.p2pTime;
						pm.p2pTarget = pmt.p2pTarget + pm.pos - pmt.pos;;
						pm.p2pSetTarget = pmt.p2pInTime = true;
					}
					if (vfg.sbAtkMode) {
						if (vfg.sbAtkMode == 1) {//单手抓
							if (i == startI)	pm.atkFlag = handFlag;// (1 << ENdId::efL) | (1 << ENdId::efR) | (1 << ENdId::elL) | (1 << ENdId::elR);// (1 << ENdId::ehL) | (1 << ENdId::ehR); //
						}
						else if (vfg.sbAtkMode == 2 && y == cy)		//双手抓
							if (x == cx - 1) pm.maxSbLockNodes = 2, pm.atkFlag = (1 << ENdId::ehR);
							else if (x == cx + 1) pm.maxSbLockNodes = 2, pm.atkFlag = (1 << ENdId::ehL);

					}

					auto obj = ivi.po[i] = Pom->addObj(pm);// ->setGravityMul(-0.1);
					DP(("PO %d %p RB %p", i, obj, obj->rb));
					obj->oTgtSb = vfg.oTgtSb;
					if (i == startI) {
						ivi.gridCenterIdx = i;
						if (pm.vfg.srcVelMode == 1)
							pmt.vel = pm.vel;
						obj->pm.visible = true; //for flyingToSb
						obj->isflyingToSb = true, obj->flyingToFlag=1,obj->flyingToSbNode = pmt.ndActAnim;
					}

				}

				ivi.ids[i] = ret;

			}

			};
		//**********************************************************************************
		//**********************************************************************************
		//**********************************************************************************

#if MMD_ATTACK_OBJ
		if (isAiCharacter())
		{
			CharacterAttackerParam cap{ this,
				{ ndHandL,ndHandR,ndFootL,ndFootR,ndLegL,	ndLegR,	ndHead,	ndYao,	ndOpaiL,ndOpaiR},
			};

			charAtk.init(cap);
			if (itemIdx >= 0) {
				//charAtk.lockObjMinHit = 1; charAtk.lockObjMaxHit = 2;
				charAtk.jump2Obj = 0;
			}
		}
		auto sb = this;
		//if (MMD_ATTACK_OBJ && sb->ndFootIKL) {
		//	sb->ndFootIKL->SetAnimationTranslate(sb->ndFootIKL->GetAnimationTranslate() + float3(Pmx->yaoPos.y/4, 0, 0));
		//	sb->ndFootIKR->SetAnimationTranslate(sb->ndFootIKR->GetAnimationTranslate() + float3(-Pmx->yaoPos.y / 4, 0, 0));
		//	if (sb->ndIKArmL)sb->ndIKArmL->SetAnimationTranslate(sb->ndIKArmL->GetAnimationTranslate() + float3(Pmx->yaoPos.y/4, 3, 0));
		//	if (sb->ndIKArmR)sb->ndIKArmR->SetAnimationTranslate(sb->ndIKArmR->GetAnimationTranslate() + float3(-Pmx->yaoPos.y / 4, 3, 0));
		//}
#endif
#if MMD_COMBINE_CATCHER
		{
			CharacterCatcherParam catpm{ this };
			cat.init(catpm);

		}
#endif

		//if (itemIdx == 0)
		{
			ndYao->rb0->cbOnTriggerEnter = [this](MMDRigidBody* trb) {
				FRAMEWAITER_CALL_BI(a, 1) {
					auto& tgp = trb->trigerParam;

					if (tgp.flag & TriggerParam::ePlayMPA) {
						auto file = std::string("data/mpa/") + ualib::WcharToAnsi(tgp.fileName);

						mmd->sb0->startSbMPA({ file.c_str() });
					}
				});


				};

			ndYao->rb0->cbOnTriggerExit = [this](MMDRigidBody* trb) {
				FRAMEWAITER_CALL_BI(a, 1) {
					auto& tgp = trb->trigerParam;
					auto rb = ndRbRoot->rb0;
					vec3 yp = rb->pos;
					if (tgp.flag & TriggerParam::eSetVelP2P) {
						auto tn = trb->node->model->GetNodeManager()->FindNode(tgp.tgtNodeName);

						if (tn) {
							vec3 vel{};
							vec3 setVelP2PTgt = tn->getGlobalPos();
							vec3 ovel = rb->getLinearVel();
							//ovel = glh::vecLimitFast(ovel,170,270.f);
							glh::calcSpeedDirP2PinTimeGuess_Param pm = {
							.startPosition = yp,
							.targetPosition = setVelP2PTgt,
							.velLen = glm::length(ovel),
							.initVel = ovel ,
							.ignoreVelLenLimit = 10.f,
							.gravity = gGravity,
							.time = 0,
							.linearDamping = ndYao->rb0->Pm.m_translateDimmer,
							.stepsPerSecond = 60 * SABA_PHYSICS_FRAMESTEP,
							.maxErrorMag = 100.f,
							.maxGuess = 20,
							};
							vel = glh::calcP2PAdjustVelocityGuess(pm.startPosition, pm.targetPosition,
								pm.initVel, pm.gravity, pm.linearDamping, 2.f, pm.stepsPerSecond, pm.maxGuess, 0.01f);
							DP(("tgt pos %f %f %f", setVelP2PTgt.x, setVelP2PTgt.y, setVelP2PTgt.z));
							DP(("orig vel  %f %f %f  Len=%f", pm.initVel.x, pm.initVel.y, pm.initVel.z, glm::length(pm.initVel)));
							DP(("delta vel %f %f %f  len=%f", vel.x - pm.initVel.x, vel.y - pm.initVel.y, vel.z - pm.initVel.z, glm::length(vel)));

							//ndYao->rb0->setLinearVel(vel); ndUpper->rb0->setLinearVel(vel); ndUpper2->rb0->setLinearVel(vel);
							Pmx->setBodyVel(vel, 0); //Pmx->scaleBodyVel(0, 2);
							lastTimeSetVelP2P = gPhyTime;
							sbTracker = mmd->sbTracker;
							// clear = true;
						}
					}
				});


				};

		}
	}

	springDefMode = (itemIdx + 3) % SPRING_MODE_COUNT;

	if (!isAiCharacter())
		return;

	if (ndLower) ndLower->forEachSubNodes([](MMDNode* nd) { nd->lowerNd = true;
	//DPWCS((L"Lower node %10s	 ", nd->GetNameU().c_str()));
		}, true);
	if (MMD_VIRTUAL_SINGER && !Vmd.get() && isAiCharacter()) {
		loadAnimation("d:/mmd/vmd/facialBase.vmd"); //setPlaying(true);
	}

	if (MMD_JOYSTICK_GAMECAST && itemIdx == 1) {
		core::matrix4 m; m.setTranslation({ 0, 0, -10 }); m.setRotationDegrees({ 0,180,0 });
		ndCenter->setAnimationMatrix(m);
		//centerForceMul = { 1,1,1 };
	}



}

void irr::scene::IrrSaba::loadCamera(irr::io::path p, bool drawOnly)
{
	if (p.size() < 1) {
		if (m_vmdCameraAnim) m_vmdCameraAnim = nullptr;
		if (irrCam) irrCam->setVisible(false);
		return;
	}
	loadAnimation(p, true);
	drawCam = drawOnly;
}

bool irr::scene::IrrSaba::loadModel(irr::io::path file)
{
	if (file.size() < 1) file = modelFile;
	std::string dupStr;
	if (Pm.fcp.duplicateAndRotatePass>0) dupStr = ualib::strFmt("dup%d,%d,T%.3f,R%.1f,S%.3f,%d", Pm.fcp.duplicateAndRotatePass, Pm.fcp.copyCount,
		Pm.fcp.copyTrsInc.y,Pm.fcp.copyRttInc.y, Pm.fcp.copyScaleInc,(int32_t)Pm.fcp.copyRttArray.size());
	strModelID = ualib::strFmt("%s[sc%d=%.3f,%.3f,%.3f][%s]", Pm.pmxFile.std_string().c_str(), Pm.fcp.modelScale, Pm.fcp.modelScaleVec3.x, Pm.fcp.modelScaleVec3.y, Pm.fcp.modelScaleVec3.z		, dupStr.c_str()	);

	auto ext = file.pathGetExt();
	//if (ext == ".pmd")	{	auto pmdModel = std::make_shared<saba::PMDModel>();	if (!pmdModel->Load( file , m_mmdDir))	return false;					mModel = std::move(pmdModel);	Pmx = nullptr;	}	else
	if (ext == ".pmx")
	{
		auto pmxModel = std::make_shared<saba::PMXModel>();
		pmxModel->createIdx = modelCollisionId;
		pmxModel->allowAllPhysicsNode = Pm.allowAllNodePhysics;
		pmxModel->setTexInvV(true);
		pmxModel->modelIdStr = strModelID;
		pmxModel->fcp = Pm.fcp;
		pmxModel->saba = this;
		updateAbsolutePosition();

		//if (Pm.rootInitScale != 1) {
		//	//pmxModel->initScale =
		//		pmxModel->rootSc =
		//	{ Pm.rootInitScale,Pm.rootInitScale,Pm.rootInitScale };
		//	pmxModel->scaled = true;
		//}
		//pmxModel->bAddWingBones = true;
		pmxModel->fcp.ikHandToArm1 = MMD_ARM1_IK;
		CPU_COUNT_B(loadpmx);
		if (!pmxModel->Load(file, m_mmdDir, useGPU, false))
		{
			std::cout << "Failed to load pmx file.\n";
			return false;
		}

		setName(file.pathGetFileName());
		CPU_COUNT_E(loadpmx);
		//auto nd = pmxModel->GetNodeManager()->getRootNode();
		//nd->SetInitialScale({ Pm.rootInitScale,Pm.rootInitScale,Pm.rootInitScale });
		mModel = nullptr;
		mModel = std::move(pmxModel);
		Pmx = (PMXModel*)mModel.get();
		Pmx->rt1Tr = { INIT_POS };
		if (!gPoMan[Pm.fcp.phType]) {
			PhyObjManagerParam pm{ SceneManager, Pmx->GetMMDPhysics(),Eqv,Pm.mmd };
			gPoMan[Pm.fcp.phType] = new PhyObjManager(pm);
		}

		Pom = gPoMan[Pm.fcp.phType];
		Pom->initSetSb0(this);
#if MMD_ADD_WINGX
		mdLWing = new saba::PMXModel();
		mdRWing = new saba::PMXModel();
		if (!mdRWing->Load(ualib::WcstoUtf8(file.c_str())//ualib::WcstoUtf8(L"d:/mmd/pmx/wing.pmx")
			, m_mmdDir)) return false;
#endif
	}
	else
	{
		throw;
		return false;
	}
	hasModel = mModel->GetIndexCount() > 2;
	modelFile = file;
#if MMD_GPU_TRANSFORM
	if (!ALLOW_GPU) useGPU = false;
	else useGPU = useGPU && Pm.allowGPU;
	if (Pmx->hasInflate) useGPU = false;

#endif

	mNodeMan = mModel->GetNodeManager();
#ifdef _DEBUG
	//mModel->needPhysics = false;
#endif
	mmdPhysics = mModel->GetPhysicsManager()->GetMMDPhysics();
	mmdPhysics->SetFPS(PHYSICS_FPS);
	//mmdPhysics->phyTimeMul = MMDPhysics::phyTimeMul;
	//mmdPhysics->SetMaxSubStepCount(32);
	MMDNode* nd;


	//mModel->needMorph = false;
	//mModel->needPhysics = false;


	ndRoot = Pmx->GetNodeManager()->GetMMDNode(0);
	ndAllParent =   setNodeFlag(L"全ての親", EId::root, 1);
	if (!ndAllParent) ndAllParent = Pmx->GetNodeManager()->GetMMDNode(1);
	ndHead = setNodeFlag(L"頭", EId::head, 1);

	if (!hasModel)
		return true;
	if (Pmx->getRb(0)) ndRbRoot = Pmx->getRb(0)->node;
	if (Pmx->isCharacter) {
		ndYao = nd = setNodeFlag(L"腰", EId::yao, 1); //ndYao->rb0->Pm.dbgVisual = true;
		ndRoot = ndYao;	while (auto p = ndRoot->GetParent()) ndRoot = p;

#define MMD_HDIK_STR  L"headIK"
#define MMD_HDTGT_STR 	L"headTarget"
		ndHeadIK = setNodeFlag(MMD_HDIK_STR, EId::ikHead, MNFlag::headIK | MNFlag::mnfIKCache | 1);
		ndHeadTgt = nd = setNodeFlag(MMD_HDTGT_STR, EId::headTgt, 1); if (nd) nd->exd.realSpdVec = new ExponentialMovingAverageXYZ(3);
		ndNeck = ndHead->GetParent();
		ndCenter = nd = setNodeFlag(L"センター", EId::center, 1);
		if (!nd) setNodeFlag(2, EId::center, 1);
		else {
			//nd->exd.fwCount = -1;
			nd->exd.flag |= MNFlag::mnfCenter;
			ndYao = nd = setNodeFlag(L"腰", EId::yao, 1);
			if (!ndYao) ndYao = nd = setNodeFlag(L"センター", EId::yao, 1);
		}
		nd = setNodeFlag(L"eyesIK", EId::ikEyes, mnfEyesIK | 1);

#ifdef _WIN32

#if MMD_SAVE_VMD_MOTION

		//setNodeFlag(L"頭", 0x8000);
		//setNodeFlag(L"左ひじ", 0x8000); setNodeFlag(L"左腕", 0x8000); setNodeFlag(L"左肩C", 0x8000); setNodeFlag(L"左肩", 0x8000); setNodeFlag(L"左肩P", 0x8000); setNodeFlag(L"右ひじ", 0x8000); setNodeFlag(L"右腕", 0x8000); setNodeFlag(L"右肩C", 0x8000); setNodeFlag(L"右肩", 0x8000); setNodeFlag(L"右肩P", 0x8000);
		//setNodeFlag(L"上半身2", 0x8000); setNodeFlag(L"上半身3", 0x8000); setNodeFlag(L"下半身", 0x8000);
		setNodeFlag(L"左足D", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"左ひざD", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"左足首D", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"左足先EX", EId::none, mnfSaveVMD | mnfLegD | 1);
		setNodeFlag(L"右足D", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"右ひざD", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"右足首D", EId::none, mnfSaveVMD | mnfLegD | 1); setNodeFlag(L"右足先EX", EId::none, mnfSaveVMD | mnfLegD | 1);
		auto savesn = setNodeFlag(L"下半身", EId::none, mnfSaveVMD | 1);
		while (savesn = savesn->GetParent()) savesn->exd.flag |= (mnfSaveVMD | 1);

		for (auto& zi : mModel->mapDIsp[L"腕"]) {
			auto node = mModel->GetNodeManager()->GetMMDNode(zi.m_index);
			node->exd.flag |= 1;
			node->exd.motionRecRttDis = 0.01f;
		}
		for (auto& zi : mModel->mapDIsp[L"指"]) {
			auto node = mModel->GetNodeManager()->GetMMDNode(zi.m_index);
			node->exd.flag |= 1;
			node->exd.motionRecRttDis = 0.1f;
		}
#endif
		ndToeL = Pmx->ndToeL; ndToeR = Pmx->ndToeR;
		ndHandL = nd = setNodeFlag(L"左手首", EId::handL, 1); //nd->SetScale(vec3(2)); 	//nd->exd.fwCount =  -1;
		ndHandR = nd = setNodeFlag(L"右手首", EId::handR, 1); //nd->SetScale(vec3(2)); 	//nd->exd.fwCount =  -1;

		ndFootIKL = nd = setNodeFlag(L"左足ＩＫ", EId::ikFootL, 1); //nd->exd.fwId = 3;	nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
		ndFootIKR = nd = setNodeFlag(L"右足ＩＫ", EId::ikFootR, 1); //nd->exd.fwId = 3; nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
		//nd = setNodeFlag(L"左つま先ＩＫ", EId::ikFootL, 1); nd->exd.fwId = 3;	nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
		//nd = setNodeFlag(L"右つま先ＩＫ", EId::ikFootR, 1); nd->exd.fwId = 3; nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;

		ndFootL = nd = setNodeFlag(L"左足首D", EId::footL, 1);
		if (nd && nd->rb0 || !findNode(L"左足首")) {
			//nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
			ndFootR = nd = setNodeFlag(L"右足首D", EId::footR, 1); //	 nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
		}
		else { ndFootL = nd = setNodeFlag(L"左足首", EId::footL, 1); ndFootR = nd = setNodeFlag(L"右足首", EId::footR, 1); }

		ndLegL = nd = setNodeFlag(L"左足D", EId::legL, 1);
		if (nd && nd->rb0 || !findNode(L"左足")) {
			//nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
			ndLegR = nd = setNodeFlag(L"右足D", EId::legR, 1); //	 nd->exd.fwCount = -1; nd->exd.fwIntvl = 0.0f;
		}
		else { ndLegL = nd = setNodeFlag(L"左足", EId::legL, 1); ndLegR = nd = setNodeFlag(L"右足", EId::legR, 1); }

		//if (setNodeFlag(L"裙_6_15",EId::none,0))	{	nd = setNodeFlag(L"裙_5_0", 1); nd->exd.fwId = 5;	nd->exd.fwCount = -1; nd->exd.eId = 111;	}
		ndLeg1L = nd = setNodeFlag(L"左ひざD", EId::kneeD, 1);
		if (nd) ndLeg1R = nd = setNodeFlag(L"右ひざD", EId::kneeD, mnfRight | 1);
		else { ndLeg1L = nd = setNodeFlag(L"左ひざ", EId::kneeD, 1); ndLeg1R = nd = setNodeFlag(L"右ひざ", EId::kneeD, mnfRight | 1); }

		ndGroup = nd = setNodeFlag(L"グルーブ", EId::group, 1); // if (nd) nd->exd.fwCount = -1;

		nd = setNodeFlag(L"左乳先", EId::chikubi, 1); if (nd) nd->exd.fwCount = -1;
		nd = setNodeFlag(L"右乳先", EId::chikubi, 1); if (nd) nd->exd.fwCount = -1;
		ndUpper = nd = setNodeFlag(L"上半身", EId::upper, 1);
		ndUpper2 = nd = setNodeFlag(L"上半身2", EId::upper2, 1);
		ndUpper3 = nd = setNodeFlag(L"上半身3", EId::upper3, 1);
		if (ndUpper3 && ndUpper3->GetIndex() > ndUpper2->GetIndex() && ndUpper3->rb0) {
			std::swap(ndUpper2, ndUpper3); std::swap(ndUpper2->exd.eId, ndUpper3->exd.eId);
		}
		ndLower = nd = setNodeFlag(L"下半身", EId::lower, 1);

		//ndManco = nd = setNodeFlag(L"manco", EId::pussy, 1); if (nd) nd->exd.fwCount = -1;	if (!ndManco) ndManco = ndLower;

#if MMD_JOYSTICK_BODY || MMD_USE_LEAP || MMD_ATTACK_OBJ
		ndArmL = nd = setNodeFlag(L"左腕", EId::armD, 1);
		ndArm1L = nd = setNodeFlag(L"左ひじ", EId::armD, mnfLv1 | 1);
		ndArmR = nd = setNodeFlag(L"右腕", EId::armD, mnfRight | 1);
		ndArm1R = nd = setNodeFlag(L"右ひじ", EId::armD, mnfRight | mnfLv1 | 1);

		//nd = setNodeFlag(L"左足D", EId::kneeD, 1); 	nd = setNodeFlag(L"右足D", EId::kneeD, mnfRight | 1);
		ndOpaiL = setNodeFlag(L"左胸上2", EId::oppai, 0);
		if (ndOpaiL) ndOpaiR = setNodeFlag(L"右胸上2", EId::oppai, 0);
		else {
			ndOpaiL = setNodeFlag(L"左胸", EId::oppai, 0);
			ndOpaiR = setNodeFlag(L"右胸", EId::oppai, 0);
		}


		ndEyes = setNodeFlag(L"両目", EId::eyes, 0);
		//ndTail = nd = setNodeFlag(L"ndTail", EId::tail, 1); if (nd) { nd->exd.fwCount = -1; nd->exd.fwt1Idx = Eqv->findPtrFwIdx(1, "tailFw"); }
		ndCatEarL = nd = setNodeFlag(L"左耳１", EId::catEarL, 1);
		ndCatEarR = nd = setNodeFlag(L"右耳１", EId::catEarR, 1);
		ndJoy2Rtt = findNode(L"ndTailRoot");
		//ndEyeL = setNodeFlag(L"左目", EId::eye, 0);
		//ndEyeR = setNodeFlag(L"右目", EId::eye, 0);
		//nd = setNodeFlag(L"胸前饰", EId::music1, 1); if (nd) { nd->exd.direction = float3(30, 0, 0); }
		// nd = setNodeFlag(L"左腕甲", EId::music1, 1); if (nd) { nd->exd.direction = float3(0, 0, 60); }
#endif
	//nd = setNodeFlag(L"腰", EId::music0, 1);if (nd) { nd->exd.direction = float3(10, 0, 0); }
	//ndUpper = setNodeFlag(L"上半身", EId::upper, 1);
	//nd = setNodeFlag(L"左足", EId::legL, 1); nd = setNodeFlag(L"右足", EId::legR, 1);
	//nd = setNodeFlag(L"左腕", EId::armL, 1); nd = setNodeFlag(L"右腕", EId::armR, 1);
		ndIKArmL = nd = setNodeFlag(L"IKLeftArm", EId::ikArmL, 0x1 | MNFlag::handIK | (MMD_USE_LEAP & 0 ? 0 : MNFlag::mnfIKCache));
		//if (nd) { nd->EnableIK(MMD_PLAY_GUQIN || leapOn); }
		ndIKArmR = nd = setNodeFlag(L"IKRightArm", EId::ikArmR, 0x1 | MNFlag::handIK | (MMD_USE_LEAP & 0 ? 0 : MNFlag::mnfIKCache));
		//if (nd) { nd->EnableIK(MMD_PLAY_GUQIN || leapOn); }

	}
	else
	{
		//setPassType(IrrPassType_Mirror, false);

	}


	ndCamPos = nd = setNodeFlag(L"camPos", EId::camPos, 1);
	//if (nd) { nd->exd.flag |= MNFlag::headIK;  }


	//if (!ndFootR) {		ndFootR = ndHandR; ndFootL = ndHandL;	}
	if (!ndFootR) ndFootL = ndFootR = ndYao;


	//nd = setNodeFlag(L"アホ毛_0_1", EId::ahoge,  1);	nd = setNodeFlag(L"アホ毛_4_1", EId::ahogeT, 1); if (nd) { nd->exd.fwId = 2;	nd->exd.fwCount = -1; }

	//ndOppaiC = nd = setNodeFlag(L"おっぱい調整", EId::music0, 1); if (nd) { nd->exd.direction = float3(10, 0, 0); }
	//nd = setNodeFlag(L"左TD_0_1", EId::music0, 1); if (nd) { nd->exd.direction=float3(0,0,180); }
	//nd = setNodeFlag(L"右TD_0_1", EId::music0, 1); if (nd) { nd->exd.direction=float3(0,0,-180); }
	//nd = setNodeFlag(L"LWingRoot", EId::LWing, 1); if (nd) { 	 /*nd->exd.realSpdVec = new ExponentialMovingAverageXYZ(8);*/}
	//nd = setNodeFlag(L"RWingRoot", EId::RWing, 1); if (nd) { 	 /*nd->exd.realSpdVec = new ExponentialMovingAverageXYZ(8);*/}
#if MMD_HAND_PTCTRL   || MMD_FINGERS

	if (isAiCharacter() && Pmx->pmx.fingerNdCount == 30)
	{
		for (int i = 0; i < 2; i++) for (int j = 0; j < 5; j++) for (int k = 1; k <= 3; k++)
		{
			ndFgrs[i][j][k] = Pmx->ndFingers[i][j][k];

		}
		//ndHand[0][0][1] = setNodeFlag(L"右手首", EId::handR, 0);
#if MMD_LEAP_FINGER==1

		assert(ndFgrs[0][0][1] == setNodeFlag(L"左親指０", EId::handPts, 0));
#else
		ndFgrs[0][0][1] = setNodeFlag(L"足_親指1.L", EId::handPts, 0);
		ndFgrs[0][0][2] = setNodeFlag(L"足_親指2.L", EId::handPts, 0);
		ndFgrs[0][1][1] = setNodeFlag(L"足_人差指1.L", EId::handPts, 0);
		ndFgrs[0][1][2] = setNodeFlag(L"足_人差指2.L", EId::handPts, 0);
		ndFgrs[0][2][1] = setNodeFlag(L"足_中指1.L", EId::handPts, 0);
		ndFgrs[0][2][2] = setNodeFlag(L"足_中指2.L", EId::handPts, 0);
		ndFgrs[0][3][1] = setNodeFlag(L"足_薬指1.L", EId::handPts, 0);
		ndFgrs[0][3][2] = setNodeFlag(L"足_薬指2.L", EId::handPts, 0);
		ndFgrs[0][4][1] = setNodeFlag(L"足_小指1.L", EId::handPts, 0);
		ndFgrs[0][4][2] = setNodeFlag(L"足_小指2.L", EId::handPts, 0);
		ndFgrs[1][0][1] = setNodeFlag(L"足_親指1.R", EId::handPts, 0);
		ndFgrs[1][0][2] = setNodeFlag(L"足_親指2.R", EId::handPts, 0);
		ndFgrs[1][1][1] = setNodeFlag(L"足_人差指1.R", EId::handPts, 0);
		ndFgrs[1][1][2] = setNodeFlag(L"足_人差指2.R", EId::handPts, 0);
		ndFgrs[1][2][1] = setNodeFlag(L"足_中指1.R", EId::handPts, 0);
		ndFgrs[1][2][2] = setNodeFlag(L"足_中指2.R", EId::handPts, 0);
		ndFgrs[1][3][1] = setNodeFlag(L"足_薬指1.R", EId::handPts, 0);
		ndFgrs[1][3][2] = setNodeFlag(L"足_薬指2.R", EId::handPts, 0);
		ndFgrs[1][4][1] = setNodeFlag(L"足_小指1.R", EId::handPts, 0);
		ndFgrs[1][4][2] = setNodeFlag(L"足_小指2.R", EId::handPts, 0);
#endif
	}
#endif


	//nd = setNodeFlag(L"发_8_1", 1); nd->exd.fwId = 5;	nd->exd.fwCount = -1; nd->exd.eId = 250; nd->exd.flag |= MNFlag::center;

	nd = setNodeFlag(L"左人指先", EId::handLf2h, 1);	if (!nd) nd = setNodeFlag(L"右人差指先", EId::handLf2h, 1);
	if (nd) {
		nd->exd.realSpdVec = new ExponentialMovingAverageXYZ(3); ndLFinger2 = nd;
		//nd->exd.fwCount = -1;  nd->exd.fwIntvl = 0.0f;
		nd = setNodeFlag(L"右人指先", EId::handRf2h, 1);	if (!nd) nd = setNodeFlag(L"右人差指先", EId::handRf2h, 1);//
		nd->exd.realSpdVec = new ExponentialMovingAverageXYZ(3); ndRFinger2 = nd;
		//nd->exd.fwCount = -1;  nd->exd.fwIntvl = 0.0f;
	}

	if (SVG_MMD_WRITE) ndSubRbBase = Pmx->GetNodeManager()->FindNode(L"subRbBase");

	//findNode(L"首")->SetScale(vec3(1, 10, 1));

#endif //not android
	MMDMorph* mph{};//onMorph


	mpBlink = setMorphFlag(L"まばたき", 1, mpfCb);//眨眼  L"笑い", 1, mpfCb); //

	//mph = setMorphFlag(L"ω", 100, mpfCb);
	//mph = setMorphFlag(L"恥ずかしい", 100, mpfCb);

	//mph = setMorphFlag(L"ねこみみ", 201, mpfCb);
	//mph = setMorphFlag(L"仮面消", 202, mpfCb);
	if (isAiCharacter()) {
#if  MMD_MOUTH_FROM_LYRIC || MOUTH_MORPH_MUL100
		mpA = setMorphFlag(L"あ", 11, mpfCb);
		mpI = setMorphFlag(L"い", 12, mpfCb);
		mpU = setMorphFlag(L"う", 13, mpfCb);
		mpE = setMorphFlag(L"え", 14, mpfCb);
		mpO = setMorphFlag(L"お", 15, mpfCb);
		//mpWa = setMorphFlag(L"あ２", 16, mpfCb);	if (!mpWa)
		mpWa = setMorphFlag(L"ワ", 16, mpfCb);
		mpN = setMorphFlag(L"ん", 17, mpfCb);

#if SHOW_INNER
		mph = setMorphFlag(L"pantySize", 111, mpfCb); if (mph) mph->SetWeight(0.5);
		mph = setMorphFlag(L"innerSize", 112, mpfCb); if (mph) mph->SetWeight(1);
#endif
		mpFw1 = setMorphFlag(L"fw1", 900, mpfCb);
		setMorphFlag(L"★バニー化", 999, mpfCb); setMorphFlag(L"★逆バニー化", 999, mpfCb);
		//setMorphFlag(L"ON_猫しっぽ", 999, mpfCb); setMorphFlag(L"ON_猫耳", 999, mpfCb);
		//mpPsy = setMorphFlag(L"OwO Morph", 116, mpfCb);
		mpSpr = setMorphFlag(L"びっくり", 116, mpfCb);


		mpXiao = setMorphFlag(L"笑い", 21, mpfCb); if (mph) mph->SetWeight(1);
		//mpXiao = setMorphFlag(L"恥ずかしい", 21, mpfCb); if (mph) mph->SetWeight(1);

#elif SVG_MMD_WRITE_WITH_MOUTH
		mpA = setMorphFlag(L"あ", 11, mpfCb);
#else

		mpA = setMorphFlag(L"あ", 11, mpfCb);
#endif

		mpStarEye = setMorphFlag(L"星目", 30, mpfCb);



	}

	mModel->cbMorph = std::bind(&IrrSaba::onMorph, this, std::placeholders::_1, std::placeholders::_2);



	for (auto& hdl : nodeHandlers)
		hdl->onModelLoaded();
	int origNodes = mNodeMan->GetNodeCount() - 1;
	for (int i = 0; i < origNodes; i++) {
		auto nd = mNodeMan->GetMMDNode(i);
		if (nd->IsIK())
			ikNodes.push_back(nd);
#if NO_IK
		nd->EnableIK(false);
#endif

	}

	//mModel->InitializeAnimation();

	mModel->cbStage = [this](NodeCbStage reason)
		{
			switch (reason)
			{
			case NodeCbStage::NS9:
			{
				if (mdApproachingMorph && Pm.snArItem) {
					size_t mid = Pm.snArItem->Cei->ctrlMorphId;
					auto mm = Pmx->GetMorphManager();
					DP(("ANG %f", headCamAngle));
					if (mid < mm->GetMorphCount()) {
						const float minAng = 15.f, maxAng = 30.f;
						if (nearKiss && headDis < 20) {
							mm->addWeightIdx = mid;
							mm->addWeight = (core::clamp((20 - headDis) / 2.f, 0.f, 1.f)
								* (1 - core::clamp((headCamAngle - minAng) / (maxAng - minAng), 0.f, 1.f)));
						}
						else
							mm->addWeightIdx = MMDMorphManager::NPos;

					}
				}
			}break;
			}
		};

	//setMorphValue(L"仮面消", 1.f);//setMorphValue(L"ねこみみ", 1.f);
	mModel->cbNode = [this](NodeCbStage reason, saba::MMDNode& node) {

		for (auto& hdl : nodeHandlers)
			//if (Drawing == (hdl->ht == IMmdNodeHandler::nh_writer))
			if (hdl->onNodeStage(reason, node)) return;

		//auto ndGlbGlmM = node.GetGlobalTransform();

		//memcpy(&ms, &gm, sizeof(ms));
		core::matrix4& ndGlmIrrM = *(core::matrix4*)node.GlobalTransformPtr();// *(core::matrix4*)&ndGlbGlmM;

		core::matrix4 matAbs = mmdBaseMat * ndGlmIrrM;

		auto pos = matAbs.getTranslation(), dir = matAbs.getRotationDegrees().rotationToDirection();
		const auto& ws = node.GetNameU();
		auto& ne = node.exd;

		if (reason == NodeCbStage::NS1) {

			if (ne.eId == EId::center)
			{
#ifdef _WIN32
#if SMALL_CHARACTER_SCALE
				node.SetScale(glm::vec3(1.25));
#endif
#endif
			}

		}

		//-----------------------------------------------------------   9   <USER>
		<GROUP> (reason == NodeCbStage::NS9)//after anim state set , before local & global transform
		{



			if (false) {}
#if  MMD_LOCK_ROOT_FRAME_ID>=0
			else if (ne.eId == EId::root) {
				if (!Eqv->pvp->working) return;

				if (mpStarEye && mpStarEye->GetWeight() > 0.99f || to_rootLockMat)
				{
					if (onSabaEvent)
					{
						onSabaEvent(this, 1);
					}
					else
					{
						auto cam = Ctx->gd.CamNormal;
						glm::mat4 mc = this->matAbsInv * cam->getAbsoluteTransformation();
						auto mRoot = node.GetGlobalTransform();

						if (!rootLocked)
						{
							rootLocked = true; Pmx->resetPhyCD = 1;
							rootLockMat = glm::inverse(mc) * mRoot;
							pinOnRoot(Pmx->mmdRootMatInv * mRoot);
							Pmx->GetPhysicsManager()->GetMMDPhysics()->enablePlane(0);
							Driver->dsd.landY = -100000.f;
							//ndFootL->exd.fwCount = ndFootR->exd.fwCount = -1;
						}
						auto m = Pmx->mmdRootMatInv * mc * rootLockMat;
						node.setAnimationMatrix(m);
						//DP(("STar %f, x=%f", mpStarEye->GetWeight(), m[3][0]));
						for (auto& rb : (*Pmx->GetPhysicsManager()->GetRigidBodys()))
						{
							//if (rb->dynRbType==1)
							//rb->Reset(nullptr);
						}
					}
				}
				else if (rootLocked) {
					rootLocked = false;
					Pmx->clearVtxPickNodes();
					Pmx->ResetPhysics();//Pmx->GetPhysicsManager()->setDynRbActive(true);
					Pmx->GetPhysicsManager()->GetMMDPhysics()->enablePlane(1);
					Driver->dsd.landY = 0.f;
					//ndFootL->exd.fwCount = ndFootR->exd.fwCount = 0;
				}
			}
#endif

			//else if (ne.eId == EId::ahoge)	{
			//	float bend = aniRateR * ( 0.5f );glm::quat gq = node.GetAnimationRotate();	glm::vec3 tr=glm::eulerAngles(gq);
			//	tr.x = bend * -90*core::DEGTORAD;tr.y = curSTP.pos.Z * 60 * core::DEGTORAD;	tr.z = sin(animeTime * core::PI) * bend*  10 * core::DEGTORAD;
			//	node.SetAnimationRotate(glm::quat(tr));
			//}
			else if (ne.eId == EId::music0)
			{
				float bend = aniRateR * (10.5f);
				glm::quat gq = node.GetAnimationRotate();	glm::vec3 tr = glm::eulerAngles(gq);
				tr = node.exd.direction * core::DEGTORAD * bend;
				node.SetAnimationRotate(glm::quat(tr));
			}
			//else if (ne.eId == EId::yao && Drawing) //test
			//{
			//	auto tr = node.GetAnimationTranslate(); tr.y -= drawDyC.Y * 0.1f; tr.z += (drawDyC.Y) * 1.0f+2-drawCtrPosC.Y*1;
			//	node.SetAnimationTranslate(tr);
			//	auto rt = node.GetAnimationRotate(); rt.x -= (drawDyC.Y+1) * core::PI/16;
			//	node.SetAnimationRotate(rt);
			//}
#if TURN_TO_CAMERA
			else if (ne.eId == EId::upper2) {
				SceneManager->getActiveCamera()->bindTargetAndRotation(true);
				glm::mat4 rttMat = matAbsInv * SceneManager->getActiveCamera()->getAbsoluteTransformation();
				glm::vec3 dirCtr, dirCam;
				dirCtr = glm::mat3(node.GetGlobalTransform()) * glm::vec3(0, 0, 1);
				dirCam = glm::mat3(rttMat) * glm::vec3(0, 0, 1);

				auto axis = glm::normalize(glm::cross(dirCtr, dirCam));
				float angle = acos(glm::dot(dirCtr, dirCam) / (glm::length(dirCtr) * glm::length(dirCam)));
				auto qt = glm::rotate(glm::mat4(1), angle, axis);
				if (angle != 0 && !isnan(axis.x)) {
					glm::vec3 rtt = glm::eulerAngles(glm::quat(qt));
					rtt.y = core::clamp(rtt.y, -core::PI / 2, core::PI / 2);
					node.SetAnimationRotate(glm::vec3(0, rtt.y, 0));
				}
			}
#endif
#if MMD_HAND_OXR
			else if (ne.eId == EId::ikArmR && !Drawing) {
				glm::vec3 v =// glm::angleAxis(glm::radians(180.0f), glm::vec3(0.0f, 1.0f, 0.0f)) *
					oud.h[1].t;
				node.SetAnimationTranslate(v * 20.f);
			}
#endif

		}

		//-----------------------------------------------------------   10   <USER>
		<GROUP> if (reason == NodeCbStage::NS10) //just before skining
		{
			bool saveMotion = false;
			bool isR;
			if (!Pm.handWrite && (isR = (ne.eId == EId::ikArmR || ne.eId == EId::ikFingerR))
#if MMD_PLAY_GUQIN
				|| (ne.eId == EId::ikArmL)
#endif
				) {
				saveMotion = true;
				auto tr = ndGlmIrrM.getTranslation();

				int ik = isR ? 0 : 1;
				if (evtMoving[ik])
				{	//tr.set(-1.5, 8, 2);
#if MMD_PLAY_GUQIN
				//	tr += (isR ? float3(3.f, 1.6, -4.4) : float3(-3.f, 1.6, -4.4))						;// +(jyMovArm ? float3(jyMovArm == 2 ? (isR ? joy2.x : -joy2.x) : joy2.x, std::max(0.f, joy2.z), 0) * 3.97f : float3(0, 0, 0));//预定手位置偏移
					posIKArmR = tr;
					if (isR) {
						tr += curSTP.pos + float3(-0.75, 0.0, 0.95);// -aniRateR * motionMul;
						node.exd.ms.updateFrame(tr, Ctx->gd.deltaTime, 30.5f, /*jyMovArm ?  600.f:*/30.f);						//node.exd.ms.pos = tr;
					}
					else {
						tr += curSTP.poL + float3(0.72, 0.0, 0.95);// -aniRateR * motionMul;
						node.exd.ms.updateFrame(tr, Ctx->gd.deltaTime, 30.5f, /*jyMovArm ? 600.f :*/ 15.f);
					}
					ndGlmIrrM.setTranslation(node.exd.ms.pos);//node.SetGlobalTransform(ndGlbGlmM);
					lookTgtMmdPos = node.exd.ms.pos; lookTgtMmdPos.Y += 0.7;
#else
					tr = curSTP[ik].pos;
					ndGlmIrrM.setTranslation(tr); //node.SetGlobalTransform(ndGlbGlmM);
					node.exd.ms.pos = tr;
#endif
				}
				else {
					float spd = 100.f;
					if (HandPointCam && fist == 1) {
						tr = curSTP[ik].pos; spd = HandPointCam ? 200.f : 100.f;
					}
					else 	curSTP[ik].pos = tr;
#if 1//HAND_CACHE_R
					node.exd.ms.updateFrame(tr, Ctx->gd.deltaTime, 20.f, spd);
					ndGlmIrrM.setTranslation(node.exd.ms.pos); //node.SetGlobalTransform(ndGlbGlmM);
#endif
					//lookTgtMmdPos = node.exd.ms.pos;
				}
			}

			else if (!Pm.handWrite && HandPointCam && (ne.eId == EId::ikFingerR)) {
				ndGlmIrrM.setTranslation(curSTP[0].pos); //node.SetGlobalTransform(ndGlbGlmM);
			}
			else if (!Pm.handWrite && (ne.eId == EId::ikArmL)) {
				saveMotion = true;
				auto tr = ndGlmIrrM.getTranslation();
#if MMD_PLAY_GUQIN
				//tr.set(1.5, 15, 5);
				tr.X += 3 * drawPosAdd.X - 1.5f;
				tr.Y += 3 * drawPosAdd.Y;
				tr.Z += -3;

				node.exd.ms.updateFrame(tr, Ctx->gd.deltaTime, 60.f, 70.f);
				ndGlmIrrM.setTranslation(node.exd.ms.pos); //node.SetGlobalTransform(ndGlbGlmM);
#elif MMD_POINT_HAND_APPEND_ANIM
				float spd = 100.f;
				if (HandPointCam && fist == 0) {
					tr = curSTP.poL; spd = HandPointCam ? 200.f : 100.f;
				}
				else 	curSTP.poL = tr;
				node.exd.ms.updateFrame(tr, Ctx->gd.deltaTime, 20.f, spd);
				ndGlmIrrM.setTranslation(node.exd.ms.pos); //node.SetGlobalTransform(ndGlbGlmM);
#else
				//if (midiNodeFlag != 2) {
				//	tr = curSTP[1].pos;
				//	ndGlmIrrM.setTranslation(tr); //node.SetGlobalTransform(ndGlbGlmM);
				//	node.exd.ms.pos = tr;
				//}
#endif
			}
			else if (ne.eId == EId::ikHead) //head IK
			{
				auto tr = ndGlmIrrM.getTranslation();
				if (allowCalcLookAtCam && !ndHead->rb0->GetActivation())
				{
					auto cam = SceneManager->getActiveCamera();
					auto camRealPos = cam->getAbsolutePosition();
#if MMD_LOOKAT_CAMERA
					if (Pm.mmd->LookOverride)
					{
						lookTgtMmdPos = Pm.mmd->lookAtPos;
						mmdBaseInv.transformVect(lookTgtMmdPos);
					}
					else if (!Drawing && (!MMD_JOYSTICK_GAMECAST || cam != Ctx->gd.CamNormal)
						&& midiNodeFlag == 0
						) {

						lookTgtMmdPos = camRealPos;
						mmdBaseInv.transformVect(lookTgtMmdPos);//cam pos to model space

					}
					ndGlmIrrM.setTranslation(lookTgtMmdPos + lookTgtMmdPosAdd);
#else
					//ms.setTranslation(MMD_JOYSTICK_HEAD ? headIkTgtPos : drawPosGlobal + vector3df(2, 0, 0));
#endif
					//node.SetGlobalTransform(ndGlbGlmM);
					auto campos = lookTgtMmdPos;
					{
						vector3df sbpos;// = ndHead->exd.lastPos;
						sbpos = ((matrix4*)&ndHead->GetGlobalTransform())->getTranslation();
						//matAbsInv.transformVect(pos);
						headDis = campos.getDistanceFrom(sbpos);//in model spacee

						mmdBaseMat.transformVect(sbpos);
						headCamAngle = ((sbpos - camRealPos).normalize().angleBetweenInDegree((cam->getTarget() - camRealPos).normalize()));
						//DP(("DIS %f", headDis));
						lookAtCam = canLookAtCam && headDis < lookDis;

					}
					if (Pm.idx == 2) {
						DP(("LOOK %d = %f %f %f   %f", lookAtCam, lookTgtMmdPos.X, lookTgtMmdPos.Y, lookTgtMmdPos.Z, headCamAngle));
					}
#if MMD_PLAY_GUQIN
					ndHead->EnableIK(true);
					//if (lookAtCam) 	hdIkTgtPos = drawPosGlobal;							else
					hdIkTgtPos = fwtSrc + vector3df(0, 1, 0);
					if (lastLookAtCam != lookAtCam) {
						hdIkPosRatio = 0.f;
						lastLookAtCam = lookAtCam;
						hdIkSrcPos = hdIkPos;
					}
					if (hdIkPosRatio < 1.f) {
						hdIkPosRatio += 0.066f;
						DP(("LOOK %f", hdIkPosRatio));
					}
					else hdIkPosRatio = 1.f;
					hdIkPos = hdIkTgtPos.getInterpolated(hdIkSrcPos, (1 - cos(hdIkPosRatio * core::PI)) / 2);

					node.exd.ms.updateFrame(hdIkPos, Ctx->gd.deltaTime, lookAtCam ? (/*jyMovArm ? 0.85f:*/0.9f) : 0.8f, lookAtCam ? (/*jyMovArm ? 60.f : */30.f) : 60.f);
					ndGlmIrrM.setTranslation(node.exd.ms.pos);//					node.SetGlobalTransform(ndGlbGlmM);

					{
						core::vector3df pos = node.exd.ms.pos; pos.Y = 0; pos.normalize();
						float deg = pos.angleBetweenInDegree({ 0,0,1 });
						mModel->camDirRate = std::max(0.f, cos(deg * core::DEGTORAD));
					}
#else

					ndHead->EnableIK_withAnime(lookAtCam, true, animeTime);// Drawing);
					//ndHead->EnableIK(lookAtCam);
#endif
				}
			}
#if MMD_LOOKAT_CAMERA
			else if (ne.eId == EId::ikEyes) {
				node.EnableIK(lookAtCam || canEyeLookAtCam);
				vector3df lookpos;
				if (lookAtCam || canEyeLookAtCam) {

					//if (dsLast.drawing)
					lookpos = lookTgtMmdPos;
					//else {
					//	lookpos = SceneManager->getActiveCamera()->getAbsolutePosition();
					//	matAbsInv.transformVect(lookpos);//cam pos to model space
					//}
				}
				else {
					lookpos = fwtSrc + vector3df(0, 0, 0);
				}
				//ndGlmIrrM.setTranslation(lookpos);//				node.SetGlobalTransform(ndGlbGlmM);
				node.setCmPosGlobal(lookpos, 0.33);
			}
#endif
#if MMD_HAND_OXR
			else if (ne.eId == EId::handR && !Drawing && !MMD_HAND_CAM) {
				node.SetAnimationRotate(oud.h[1].r);
				//node.SetAnimationTranslate(oud.h[1].t*10.f);
			}
#endif
		}

		//-----------------------------------------------------------   200   <USER>
		<GROUP> if (reason == NodeCbStage::NS200)
		{
			core::vector3df curPos = pos;
			bool isR;
			core::vector3df  dtPos = curPos - ne.lastPos;
			float durTIme = (animeTime - ne.lastTime);
			if (durTIme > 0.0001f)
				ne.irrSpeed = dtPos / durTIme;


			else ne.irrSpeed = dtPos * APP_FPS;
			if (node.exd.realSpdVec) {
				auto delta = curPos - node.exd.realLastPos;
				node.exd.realLastPos = curPos;
				node.exd.ms.spd = delta / Ctx->gd.deltaTime;
				node.exd.realSpdVec->addAndFloatValues(node.exd.ms.spd.x, node.exd.ms.spd.y, node.exd.ms.spd.z);
			}
#if MMD_SAVE_VMD_MOTION
			if (vmdWriteFile && ((node.exd.flag & mnfSaveVMD) || vmdWriteFile->recordingAllNode))
			{
				onMotionFinish(node);
			}

#endif
			if ((ne.eId == EId::handRf2h) || (ne.eId == EId::handLf2h)
				//|| (ne.eId == EId::handLf23)
				)
			{

				if (ne.eId == EId::handRf2h) {
					auto m = node.GetGlobalTransform();
					glm::vec3 scale;
					glm::quat rotation;
					glm::vec3 translation;
					glm::vec3 skew;
					glm::vec4 perspective;
					glm::decompose(m, scale, rotation, translation, skew, perspective);
					fwtSrc = translation;
					//fwtRtt = glm::eulerAngles(rotation);
					//nodeToFwSrc(ne, matAbs);
					//DP(("NS20 %f", translation.x));
				}
#if USE_SVG
				//if (svgMan) {
				//	svgMan->useMat = true;
				//	svgMan->posMatrix = matAbs;
				//}
				if (drawStrokeRatio > 0.0001f && drawFinished == false)
				{

					Eqv->LaunchFw3D(V3dToFloat3(pos), Eqv->getFwIdx(0, 0), ne.irrSpeed, { 1,1,1,0.3f + 0.7f * drawStrokeRatio });
				}
				//else if (Drawing) Eqv->LaunchFw3D(V3dToFloat3(pos), Eqv->getFwIdx(2, 1), { 0,0,0 }, { 1,1,1,1 });
				drawAbsPos = pos;
				if (Eqv->GetPPT()) Eqv->GetPPT()->setMovAtTgt(1, pos);
#endif

#if MMD_SAVE_PUNCH_MIDI
				static float hue = 0.0f; hue += 1;	float angle = hue / 360 * 2 * core::PI;	core::vector3df vec = node.exd.ms.spd;
				float vl = std::min(50.f, vec.getLength() / 2);
				SColorHSL hsl(hue, 50, vl);	SColor sc = hsl.toSColor(); sc.setAlpha(vl * 5);
				if (vl > 20)
					Eqv->LaunchFw3D(V3dToFloat3(curPos), Eqv->getFwIdxByFwIdStr(1, "punch"), node.exd.ms.spd, sc);
#endif
			}
			else if (ne.eId == EId::handL || ne.eId == EId::handR)
			{

#if CAM_ON_HAND || MMD_HAND_CAM

				if ((ne.eId == EId::handR))
				{
					static irr::scene::ICameraSceneNode* irrCam{};
					if (!irrCam) {
						irrCam = Ctx->getSceneManager()->addCameraSceneNode(0, { 0,0,0 }, { 0,0,0 }, -1, false);
						//Ctx->getSceneManager()->addCubeSceneNode(100, irrCam);
					}
					SceneManager->setActiveCamera(irrCam);
					core::matrix4 m = mmdBaseMat * node.GetGlobalTransform() * glm::translate(glm::mat4(1), { -1.25,-1.25,-1.5 });
					irrCam->setPosition(m.getTranslation());
					//irrCam->bindTargetAndRotation(true);
					//irrCam->setRotation(glm::eulerAngles(oud.h[1].r));
					irrCam->setTarget(headPos);
					lookTgtMmdPosAdd = glm::vec3(0, -2, 0);

				}
#endif
			}
			//else if (ne.eId == EId::chikubi   /*|| ne.eId == EId::ahogeT*/) { //左人指先
			//	auto ndGlbGlmM = node.GetParent()->GetGlobalTransform();

			//	//memcpy(&ms, &gm, sizeof(ms));
			//	core::matrix4& ms = *(core::matrix4*)&ndGlbGlmM;
			//	core::matrix4 m = mmdBaseMat * ms;
			//	//Eqv->setFwMatrix(0, m);
			//	auto parentPos = m.getTranslation();
			//	ne.direction = (pos - parentPos).normalize();
			//	if (ne.fwCount != 0)
			//		ne.fwt1Idx = Eqv->findPtrFwIdx(1, "jetOpai");

			//}
			else if (ne.eId == EId::pussy) {

				ne.direction = glm::normalize(glm::mat3(mmdBaseMat * node.GetGlobalTransform()) * node.posOffset);
			}
			else if (ne.eId == EId::yao)
			{
				centerPos = pos;
#if MMD_AR_BALL
				snBall->setMatrixTR(ndGlmIrrM); snBall->setScale(Pmx->ctrBallSize);
#endif
			}

			else if (ne.eId == EId::center)
			{
				auto pos = ndGlmIrrM.getTranslation();
				Box.MinEdge = pos - vector3df(5, 10, 5);
				Box.MaxEdge = pos + vector3df(5, 10, 5);
				//snBall->setPosition(pos);

			}

			//else if ((isR = ne.eId == EId::earR) || ne.eId == EId::earL)
			//{
			//	static bool bb = false;
			//	if (bb == isR) {
			//		bb = !bb; float bend = aniRateR * (0.5f + 0.5f * motionMul);
			//		auto tr = ndGlmIrrM.getRotationDegrees();
			//		tr.X += bend * -60;
			//		tr.Y += (MMD_JOYSTICK_GAMECAST? lookTgtMmdPosAdd.X: curSTP[0].pos.Z*60) * (isR ? -1 : 1);
			//		tr.Z += sin(animeTime*core::PI) * bend*30;
			//		ndGlmIrrM.setRotationDegrees(tr); //node.SetGlobalTransform(ndGlbGlmM);
			//	}
			//}
			//else if (ne.eId == EId::music1)
			//{
			//	float bend = aniRateR * (motionMul);
			//	auto tr = ndGlmIrrM.getRotationDegrees();
			//	tr += node.exd.direction * bend;
			//	ndGlmIrrM.setRotationDegrees(tr);// node.SetGlobalTransform(ndGlbGlmM);
			//}
			//else if (  ne.eId == EId::opaiAdj  )
			//{
			//}
#if 0
			else if (ne.eId == EId::ikFootL || ne.eId == EId::ikFootR)
			{
				SEvent evt{ EET_CMD_INPUT_EVENT };
				if (pos.Y < 15 && (node.exd.lastPos.y >= 15))
				{
					evt.CmdInput.cmdId = 2001000;  //water wave
					evt.CmdInput.pm1f32 = pos.X;
					evt.CmdInput.pm2f32 = pos.Y;
					Ctx->getLib()->libPostEvent(evt);
				}

				//if (glm::length(ne.aniSpeed) > 100)					ne.fwCount = 1;
			}
			else if (ne.eId == EId::head)
			{
				if (node.rb0)
				{
					btTransform wt; wt.setFromOpenGLMatrix(&node.GetGlobalTransform()[0][0]);
					node.rb0->GetRigidBody()->setWorldTransform(wt);
				}
			}
#endif
			//else if (ne.eId == EId::legL || ne.eId== EId::legR)
			//{
			//	if (node.rb0)
			//	{
			//		phyForceParam pm; pm.node = &node; pm.fY = 5600; pm.fw = "jetFoot"; pm.fwSubNode = 2;
			//		pm.hueAdd = ne.eId == EId::legR ? 30 : -30;
			//		phyForceOnNode(pm );
			//	}
			//}
			//else if (ne.eId == EId::upper2) //text from chichi
			//{
			//	core::matrix4 matAbsP = mmdBaseMat * node.GetGlobalTransform() * glm::translate(glm::mat4(1), { 0,-0.1,-1 });
			//	nodeToFwSrc(ne, matAbsP);
			//}

			else if (ne.eId == EId::headTgt) //head IK
			{
#if MOUTH_FW
				auto ndParent = node.GetParent();
				auto ndGlbGlmMP = ndParent->GetGlobalTransform();
				core::matrix4& msp = *(core::matrix4*)&ndGlbGlmMP;
				core::matrix4 matAbsP = mmdBaseMat * msp;
				vector3df posp = matAbsP.getTranslation();
				headVector = (pos - posp).normalize();
				headPos = ndHead->exd.lastPos;
				if (glm::length(node.exd.ms.spd) > 1) {
					Eqv->pfAddForce(Ctx->gd.time, node.exd.realLastPos, node.exd.ms.spd / 2.f, 3, 1);
				}
				ne.ms.spd += float3(headVector * 1000.f);
				Eqv->LaunchFw3D(node.exd.realLastPos, Eqv->getFwIdx(1, 2), node.exd.ms.spd * 11.1f * Eqv->GetPPT()->mPG->cb.eqvAvgPeak);
#endif
				//nodeToFwSrc(ne, matAbsP);
#if CAM_ON_HEAD
				if (camTgt)
				{
					canLookAtCam = false;
					SnArItem* it = (SnArItem*)camTgt;

					auto cam = Ctx->gd.CamNormal;
					core::matrix4 m = mmdBaseMat * node.GetGlobalTransform() * glm::translate(glm::mat4(1), { 0,0,-0.7 });
					cam->setPosition(m.getTranslation());
					cam->setTarget(it->saba->headPos + vector3df(0, -100, 0));
					//cam->setRotation(m.getRotationDegrees());
				}
#endif
			}

			else if (ne.eId == EId::camPos) //head IK
			{
				auto ops = ndGlmIrrM.getTranslation(); //ops += vector3df(-0, 3, -1);
				mmdBaseMat.transformVect(ops);
				eyePos.interpolate(ops, eyePos, core::clamp(eyePos.getDistanceFrom(ops) / 100.f, 0.2f, 0.5f));
				eyePosSet = true;
			}
			else  //finger fw
			{

			}

			if (!ne.footFw)
				onNodeFirework(dtPos, node);
			else {
				if (core::matrix4(Pmx->mmdRootMatInv * node.GetGlobalTransform()).getTranslation().Y < 2.f)
					onNodeFirework(dtPos, node);
			}
			ne.lastPos = pos;
			ne.lastMmdPos = ndGlmIrrM.getTranslation();
			ne.lastTime = animeTime;
		}
#if FRAME_NODE_FW
		else if (reason == saba::NodeCbStage::NS_NodeFw)
		{
			for (int i = 0; i < node.pmxBone->ndFwCount; i++)
				Eqv->LaunchFw3D(pos, Eqv->getFwIdxByFwIdStr(1, node.pmxBone->ndFwName), node.exd.irrSpeed, curDiffuse);

		}
#endif
		//if (ne.eId==1 || ne.eId==2)		setPosition(0, (FtHeight[0] + FtHeight[1])/2, 0);


		};


#if MMD_ADD_WINGX
	initWing();
#endif
#if !HAS_AR_EDITOR
#ifdef _WIN32
	if (Pm.idx == 0 || Pm.handWrite)
		Eqv->cbOnSvFwCenterPt = [this](EQVisual::SvTimePt& pt, int fromId, float deltaS) {
		onSvFwCenterPt(pt, fromId, deltaS);
		return;
		};
#endif
#endif
	auto driver = getSceneManager()->getVideoDriver();
	VkDriver* vkdrv = (VkDriver*)driver;
	auto mrMMD = (video::VkMaterialRenderer_MMD*)driver->getMaterialRenderer(EMT_MMD);
	dynBufAlignment = mrMMD->dynBufAligment();

	ieSize = mModel->GetIndexElementSize();
	size_t ibSize = u32(mModel->GetIndexCount());
	auto ibptr = mModel->GetIndices();


	if (useGPU && !Pm.fcp.forceReload) //Cache same data
	{
		sbd = Sbds.findRes(strModelID);
		if (!sbd)
			sbd = Sbds.addRes(strModelID, std::make_shared<SabaShareData>());
	}
	else sbd = std::make_shared<SabaShareData>();
	if (!sbd->drawIndexs) sbd->drawIndexs = (video::IHardwareBuffer*)driver->createHardwareBuffer(
		video::EHBT_INDEX, video::EHBA_DEFAULT,
		u32(ieSize * ibSize),
		0, ibptr);


	if (!useGPU)
	{

		if (!sbd->drawVertices) sbd->drawVertices = (video::IHardwareBuffer*)driver->createHardwareBuffer(
			video::EHBT_VERTEX, MMD_IRRMTR ? video::EHBA_DEFAULT_RW : video::EHBA_DEFAULT,
			u32(sizeof(video::S3DVertex) * (mModel->GetVertexCount())),
			0, nullptr);

		((VkHardwareBuffer*)sbd->drawVertices)->setCurrentCommandBuffer();//否则UV丢失：PMXModel uvUploaded地址改变？
#if MMD_IRRMTR
		video::S3DVertex* pvt = (video::S3DVertex*)sbd->drawVertices->lock();//why need

		for (int i = 0; i < mModel->GetVertexCount(); i++) {
			pvt->Color = 0xFFFFFFFF;
			pvt++;
		}

		sbd->drawVertices->unlock();
#endif
	}
#if MMD_GPU_TRANSFORM
	if (useGPU) {
		DP(("SIMPLE USE GPU"));
		if (mcs) delete mcs;
		mcs = new MmdCompute(Driver);
		mcs->sbd = sbd.get();
		mcs->prepareGPUTransform(mModel.get());
		Pmx->clearPNUV(Pm.freeAllVtxData);
	}
#endif

#if DRAW_SHADER_TOY_MMDTEX
	texSdtoy = driver->addRenderTargetTexture({ 1024,1024 }, "rtsdtoy");
#endif
	cbcps.resize(mModel->GetMaterialCount());

	cbDynVkBuf.createBuffer(vkdrv->mDevice,
		VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
		VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
		cbcps.size() * mrMMD->dynBufAligment(),
		nullptr);
	cbDynVkBuf.descriptor.range = sizeof(cbPerDraw);
	allShadowCastMat = true;
	for (size_t i = 0; i < mModel->GetMaterialCount(); i++)
	{
		auto& mmdMat = *(saba::MMDMaterial*)&mModel->GetMaterials()[i]; //to modify
		mmdMat.id = i;
		SMaterial mr;
		mr.RcvShadow = Pm.receiveShadow;
		mr.BackfaceCulling = mmdMat.m_bothFace ? false : true;
		if (!mmdMat.m_shadowCaster) allShadowCastMat = false;
		if (mmdMat.m_alpha == 0.0f) {
			DP(("ALPHA=0 %s", mmdMat.m_texture.c_str()));
		}

		DP(("M %d %f", i, mmdMat.m_alpha));
		auto& memo = mmdMat.m_memo;
		if (memo.size() >= 5 && memo.substr(0, 2) == "{\"") {
			parseMmdMatMemo(mmdMat);
		}

		auto& cp = cbcps[i];
		auto& cb = cp.cb;

		cb.Diffuse = float4(mmdMat.m_diffuse.r, mmdMat.m_diffuse.g, mmdMat.m_diffuse.b, mmdMat.m_alpha);

		float am = 1.f;
		cb.Ambient = float4(mmdMat.m_ambient.r * am, mmdMat.m_ambient.g * am, mmdMat.m_ambient.b * am, 1);
		//cb.Specular = float4(mmdMat.m_specular.r, mmdMat.m_specular.g, mmdMat.m_specular.b, mmdMat.m_specularPower);
		cb.Specular = float4(mmdMat.m_specular.r, mmdMat.m_specular.g, mmdMat.m_specular.b, mmdMat.m_specularPower) + specSets[specId];
		//DP(("SP %d %f %f %f  %f",i, mmdMat.m_specular.r, mmdMat.m_specular.g, mmdMat.m_specular.b, mmdMat.m_specularPower));
		//cb.Ambient = float4(0.5, 0.5, 0.5, 1);
		cb.lnv = lnvSets[lnvId];

		//cb.Specular = float4(1,1, 1, 5);
		cb.TextureModes = { 0,0,0,0 };
		cb.clipY = FF_CLIP_Y;

		{
			ITexture* tex{};
			//bool oldMipFlag = driver->setTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS, true);
#if DRAW_SHADER_TOY_MMDTEX
			if (isReplaceMat(mmdMat.m_name)) {
				tex = texSdtoy;
			}
			else
#endif

				tex = mmdMat.m_texture.empty() ? nullptr : getModelTexture(mmdMat.m_texture.c_str());
			//if (mmdMat.m_texture.find(ualib::WcstoUtf8(L"o-a_base")) != std::string::npos)		texQun = tex;
			//driver->setTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS, oldMipFlag);

			mr.TextureLayer[0].Texture = (IRR_MTR_CUBEMAP && MMD_IRRMTR) ? Ctx->gd.texCube : (tex ? tex : driver->NullTexture);
			mr.MaterialType = MMD_IRRMTR /*|| driver->getPassType() == IrrPassType_ShadowMap*/ ? (IRR_MTR_SSAO ? EMT_SSAO_GBUFFER : (EMT_CUBE_MAP)) :
				EMT_MMD;
			//mat.MaterialType = EMT_TRANSPARENT_ALPHA_CHANNEL ;
			//DP((L"T%d TEX   %p",i, tex));
#if EQV_TEXFU
			if (mmdMat.m_name.find(ualib::WcstoUtf8(L"02_Tops")) != std::string::npos)
				texCoat = tex;
			if (mmdMat.m_name.find(ualib::WcstoUtf8(L"01_Bottoms")) != std::string::npos)
				texBott = tex;

#elif MOD_TEXFU ||DRAW_SHADER_TOY_MMD_IRRTEX
			if (isReplaceMat(mmdMat.m_name))
#if  DRAW_SHADER_TOY_MMD_IRRTEX
				mat.MaterialType = EMT_SHADER_TOY;
#else
				texFu = tex;
#endif
#endif
			cb.TextureModes.x = tex ? 2 : 1;// always has alpha
			cb.TexMulFactor = mmdMat.m_textureMulFactor;
			cb.TexAddFactor = mmdMat.m_textureAddFactor;
		}

		//if (mmdMat.m_spTextureMode == MMDMaterial::SphereTextureMode::Add) mat.MaterialType = EMT_TRANSPARENT_ALPHA_CHANNEL;

		{
			auto tex = getModelTexture(mmdMat.m_toonTexture.c_str());
			//if (!tex && mmdMat.m_toonTexture.size())	tex = getModelTexture((std::string("toon/") + mmdMat.m_toonTexture).c_str());
			mr.TextureLayer[1].Texture = tex ? tex : driver->NullTexture;
			cb.TextureModes.y = tex ? 1 : 0;
			cb.ToonTexMulFactor = mmdMat.m_toonTextureMulFactor;
			cb.ToonTexAddFactor = mmdMat.m_toonTextureAddFactor;
			//DP((L"T%d TOON	 %s", i, fpath.c_str()));
		}
#ifdef _WIN32

		if (!mmdMat.m_spTexture.empty())
		{

			//auto tex = appContext.GetTexture(mmdMat.m_spTexture);
			//mat.m_spTexture = tex;
			cb.TextureModes.z = mmdMat.spaIsNormalMap?3:  (int)mmdMat.m_spTextureMode;
			auto tex = mmdMat.m_spTexture.empty() ? nullptr : getModelTexture(mmdMat.m_spTexture.c_str());
			mr.TextureLayer[2].Texture = tex ? tex : driver->NullTexture;
			if (!tex) cb.TextureModes.z = 0;
			cb.SphereTexMulFactor = mmdMat.m_spTextureMulFactor;
			cb.SphereTexAddFactor = mmdMat.m_spTextureAddFactor;
			//DP(("T%d SP	 %s", i, mmdMat.m_spTexture.c_str()));
		}
		//mModel->GetPhysicsManager()->GetMMDPhysics()->SetMaxSubStepCount(30);
#endif
		if (useGPU)
			cp.hbVtx = mcs->mVbPosNorm;
		cp.cbDrawBuf = &cbDynVkBuf;
		cp.ds = mrMMD->newDS();
		cp.dsOfs = i * dynBufAlignment;
		cp.twoSide = MMD_FORCE_2SIDE ? 1 : mmdMat.m_bothFace;
		mr.pCbCp = &cbcps[i];

		mtrs.emplace_back(mr);
	}
	if (hasBloom) {
		hlMtrs = mtrs;
		hlCbcps.resize(mModel->GetMaterialCount());
	}
	if (!useGPU) {
		if (!fwLch) fwLch = new SbFwLauncher(this);
		fwLch->vtxFwInit();
	}


	if (isAiCharacter())
	{
	}
	else
	{
		//canLookAtCam = false;
	}
	//fwPrelaunch = (MmdMode == 1 || MmdMode == 10 || MmdMode == 11);
	return true;
}

void irr::scene::IrrSaba::launchNodeFw(saba::MMDNode& node)
{
	auto pos = mmd2irr(node.rb0->getPosition());
	auto ba = node.pmxBone->anim; //shared
	auto fwid = 0;
	if (ba->ndFwId >= 0)
		fwid = ba->ndFwId;
	else if (ba->ndFwName.size())
		ba->ndFwId = fwid = Eqv->getFwIdxByFwIdStr(ba->ndFwType, ba->ndFwName);
	else fwid = Eqv->getCurPtrFwIdx(ba->ndFwType);
	if (ba)
	for (int i = 0; i < ba->ndFwCount; i++)
	{
		float ratio = float(i + 1) / (float)ba->ndFwCount;
 
		auto pos = node.rb0->hasPhyState ? (glm::mix(node.rb0->lpos, node.rb0->getPosition(), ratio)) : node.rb0->getPosition();
		pos += UaRandVec3() * ba->ndFwEmtSize.x;
		auto vel = node.rb0->getLinearVel() + glh::matRotateVec(node.rb0->GetTransform()*node.rb0->rttMat,ba->ndFwVel);
		pos += vel * gFrameTime * MMDPhysics::stepTimeMul;

		LfwParam pm;
		if (ba->ndMatFw) {
			if (node.exd.mid == 0) {
				// Allocate a material ID for the node
				node.exd.mid = Eqv->GetPPT()->vtxMats.alloc({});
				node.exd.ndNextFwTime = gSceneTime+(UaRandF()*0.5f+0.5f)* ba->ndFwIntv;
			}	
			pm.mid = node.exd.mid;
			EQVisual::VtxMatStruct vm{};
				auto irm = mmdBaseMat * node.rb0->getNodeTransform(); 
				vm.m = irm.getTransformTR();
				vm.v0 = vec3(0);// glm::vec3(mmd2irr(node.rb0->getLinearVel())); 
				vm.v1 = vec3(0); //glm::vec3((node.rb0->getPosition()));
				Eqv->GetPPT()->setVtxMat(node.exd.mid, vm);
				// Update vertex material with transformation data
			if (node.exd.mid > 0 && gSceneTime > node.exd.ndNextFwTime) {
				node.exd.ndNextFwTime = gSceneTime + (UaRandF() * 0.5f + 0.5f) * ba->ndFwIntv;
				
				

				Eqv->LaunchFw3D(vec3(0), fwid, UaRandVec3()*100.f, curDiffuse, &pm);
			}
		}
		else Eqv->LaunchFw3D(mmd2irr(pos), fwid, mmd2irr(vel), curDiffuse, &pm);
	}
}

void irr::scene::IrrSaba::launchCbNodeFw(saba::MMDNode& node, float countMul, float spdMul)
{


	const int SMUL = 6;//
	bool boost = Ctx->getEvtRcv()->IsKeyDown(KEY_NUMPAD2);
	glm::vec3 dir = node.rb0->parentRb ? mmd2irr((node.rb0->pos - node.rb0->parentRb->pos)).getNormalizeCopy() : vector3df(0);
	auto nodeVel = node.exd.irrSpeed * 0.5f;

	auto nodeLen = glm::length(nodeVel);
	auto nodeDir = nodeLen > 0.00001f ? glm::normalize(nodeVel) : glm::vec3(0);
	float limit = node.absScale.x * 10000.f * std::clamp(sqrt(handFwSpeed * (boost ? 2.f : 1.f) / 100), 1.f, 2.f);
	nodeVel = nodeDir * std::min(limit, nodeLen);
	glm::vec3 vel = (dir * (handFwSpeed * SMUL) + nodeVel) * spdMul;
	dir = nodeLen > 0.00001f ? glm::normalize(vel) : glm::vec3(0);
	float velLen = glm::length(vel), velLen0 = glm::length(nodeVel);
	//DP(("velLen %8.f  O=%8.2f  lpos= %8.2f -  %8.2f",velLen, velLen0, node.rb0->lpos.x, node.rb0->pos.x));
	float velLim = std::min(limit, velLen * (boost ? 16.f : 1.f));
	int c = std::max(0, int((velLen * (boost ? 16.f : 1.f) / 1200) - (SMUL / 2) * 0)) * countMul;

	glm::vec3 fwVel = dir * velLim;

	for (int i = 0; i < c; i++) {
		float ratio = float(i + 1) / (float)c;

		float t = ratio * Ctx->gd.deltaTime;
		auto pos = mmd2irr(glm::mix(node.rb0->lpos, node.rb0->getPosition(), ratio));
		glm::vec3 v = glm::mix(node.exd.lastFwVel, fwVel, ratio) + ualib::UaRandVec3() * 0.25f * velLim;
		Eqv->LaunchFw3D(pos, Eqv->getFwIdxByFwIdStr(1, "nodeSpdFwM0"), v * 0.5f, SColorf(SColor(node.exd.color)));
	}
	node.exd.lastFwVel = fwVel;
}

void irr::scene::IrrSaba::onClear()
{
	for (int i = 0; i < 16; i++) stkLineDel(i); stkRbReset = true;
	setWriting(false); Drawing = (false);

}

bool irr::scene::IrrSaba::pinOnRoot(glm::mat4 mRoot)
{

	if (ndPinHandL) return false;
	if (!isAiCharacter()) return false;
	if (!ndPinHandL) ndPinHandL = Pmx->GetNodeManager()->FindNode(L"pinHandL");
	if (!ndPinHandR) ndPinHandR = Pmx->GetNodeManager()->FindNode(L"pinHandR");
	//if (!ndPinFootL) ndPinFootL = Pmx->GetNodeManager()->FindNode(L"pinFootL");
	//if (!ndPinFootR) ndPinFootR = Pmx->GetNodeManager()->FindNode(L"pinFootR");
	//if (!ndPinYao) ndPinYao = Pmx->GetNodeManager()->FindNode(L"pinYao");
	//if (!ndPinYao) return false;
	Pmx->clearVtxPickNodes();
#if 1
	setAllDynRbActive(true);// canLookAtCam = false;
	//Pmx->ResetPhysics(); // 	Pmx->ResetPhysics(); Pmx->ResetPhysics();
	//if (ndHead) setBonePhsActive(ndHead, false);
	//setBonePhsActive(ndUpper2, false, false);
	//setBonePhsActive(ndHandL, false, false);
	//setBonePhsActive(ndHandR, false, false);
	//setBonePhsActive(ndCenter, false, false);
#else
	setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"左腕"), true);
	setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"右腕"), true);
	setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"右足"), true);
	setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"左足"), true);
	setBonePhsActive(ndYao, true, false);
#endif
	//enableIK(false);
	Pmx->UpdateNodeAnimation(false);
	Pmx->UpdateNodeAnimation(true);
	//node.UpdateLocalTransform();

#if 1
	auto mRootI = glm::inverse(mRoot);
	auto ma = glm::translate(glm::mat4(1), glm::vec3(0, 0, 100));

	//ndPinHandL->setAnimationMatrix(mRootI * ndHandL->GetGlobalTransform(), false);
	//ndPinHandR->setAnimationMatrix(mRootI * ndHandR->GetGlobalTransform(), false);
	//ndPinFootL->setAnimationMatrix(mRootI * ndFootL->GetGlobalTransform(), false);
	//ndPinFootR->setAnimationMatrix(mRootI * ndFootR->GetGlobalTransform(), false);
	//if (ndUpper2) ndPinYao->setAnimationMatrix(mRootI * ndYao->GetGlobalTransform(), false);
	//ndPinYao->jointAddT = 0.f;
	//ndPinYao->jointAddR = core::PI/10;

	//if (itemIdx > 0) return false;

#if  MMD_USE_LEAP || MMD_JOYSTICK_GAMECAST || 1

	PMXJoint jt{};
	jt.limitMinT = vec3(-1);
	jt.limitMaxT = vec3(1);
	jt.setLocalPos = true;
	jt.springT = vec3(10000.f);
	jt.springR = vec3(1000.f);
	jt.dampingT = vec3(100.f);
	jt.dampingR = vec3(10.f);
	jt.rotate = glm::vec3(90, 0, 0);
	Pmx->connectRb(ndPinHandL->rb0, ndHandL->rb0, false,  false, jt);
	Pmx->connectRb(ndPinHandR->rb0, ndHandR->rb0, false,  false, jt);
	//Pmx->connectRb(ndPinHandL->rb0, ndArmL->rb0->parentRb->parentRb, true, { 0,0,0 }, false, {});
	//Pmx->connectRb(ndPinHandR->rb0, ndArmR->rb0->parentRb->parentRb, true, { 0,0,0 }, false, {});


	//Pmx->getRbByVtxIdOrNode(0, 0, ndFootL, ndPinFootL, false, 0x10); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr);
	//Pmx->getRbByVtxIdOrNode(0, 0, ndFootR, ndPinFootR, false, 0x10); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr);
	//Pmx->getRbByVtxIdOrNode(0, ndYao, ndPinYao, true); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr); pinedYao = true;//ndPinYao->setTranslationGlobal(ndPinYao->pmxBone->m_position);
#endif

	//if (itemIdx==0 || itemIdx==2)
	//Pmx->getRbByVtxIdOrNode(0,itemIdx==0?ndHandL:ndHandR, ndPinYao); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr);



#endif

	if (ndPinHandR) {
		ndPinHandR->SetAnimationTranslate(vec3(-4, 50, 0));  
		ndPinHandL->SetAnimationTranslate(vec3(4, 50, 0));  
		//ndArm1L->joint2p()->setRttDegAdd(vec3{ 0,-90, 0 })->lockRtt(true, vec3{ 0.1 });
		//ndArm1R->joint2p()->setRttDegAdd(vec3{ 0,90, 0 })->lockRtt(true, vec3{ 0.1 });

	}
	return true;
}


void irr::scene::IrrSaba::processNodeJPV(saba::MMDNode* nd)
{
	auto &jv = *nd->pmxBone->pjv;
	Json::Value v = jv["linkRoot"];
	if (!v.isNull()) {
		auto model = v["model"].asString();
		auto toNdName = v["to"].asString();
		auto toNd = Pmx->GetNodeManager()->FindNode(toNdName);
		if (!toNd ) return;
		int lkN = 2, lkLen = 6, rows =1;
		IrrSaba* lsb{};
		vec3 zDir = { 0,0,-3 };
		for (int z = 0; z < rows; z++)
		for (int i = 0; i < lkN; i++) for (int j = 0; j < lkLen; j++)
		{
			PMXFileCreateParam cp{ model };
			cp.modelScale = true; cp.modelScaleVec3 = vec3(2.f);
			auto sbw = gLoadSabaModel(cp,  0x1)->sb;
			auto pos = nd->getGlobalPos() + zDir * float(z) +float(i)/(lkN -1)*(toNd->getGlobalPos()- nd->getGlobalPos());
			saba::PMXJoint jp{};
			jp.translate = j == 0 ? pos:vec3(0);
			jp.setLocalPos = j > 0;
			sbw->Pmx->moveAllOffset(pos - sbw->Rb0()->getPosition());
			auto lsbSubRootRb = j> 0 ? lsb->findNode(L"SubRootLink")->rb0 : Rb0();
			sbw->Pmx->connectRbM(lsbSubRootRb, sbw->Rb0(), true, false,jp,false);
			//sbw->Pmx->connectRb(sbw->findNode(L"watch")->rb0,sb0->ndHandR->rb0,  true, { 3.27f,0,0 }, false, {});
			lsb = sbw;
			Ctx->setSharedRbCb(lsb->findNode(L"SubRootLink")->rb0);

		}

	}

}
void irr::scene::IrrSaba::setAllDynRbActive(bool active, int actCD)
{
	Pmx->setDynRbActive(active);
	rbActCD = actCD;
	//ndHandL->EnableDeformAfterPhysics(active);	ndHandR->EnableDeformAfterPhysics(active);
//	if (ndHeadIK && ndHead)
//	{
//#if ALL_NODE_PHYSICS
//
//		ndHead->ikIgnoreAnim = 0; ndHead->EnableDeformAfterPhysics(0); 	ndHeadIK->EnableDeformAfterPhysics(0);
//#endif
//	}
}
void irr::scene::IrrSaba::pinWriteNodes()
{
#if SVG_MMD_WRITE_USING_LEG
	//if (!ndPinFootL) ndPinFootL = Pmx->GetNodeManager()->FindNode(L"pinFootL");
	//if (!ndPinFootR) ndPinFootR = Pmx->GetNodeManager()->FindNode(L"pinFootR");
	if (!ndPinYao) ndPinYao = Pmx->GetNodeManager()->FindNode(L"pinYao");
	auto ndc = Pmx->GetNodeManager()->FindNode(L"上半身");
	ndPinYao->pmxBone = ndc->pmxBone;
	Pmx->clearVtxPickNodes();	Pmx->UpdateNodeAnimation(false);
	Pmx->UpdateNodeAnimation(true);
	//Pmx->getRbByVtxIdOrNode(0, ndFootL, ndPinFootL); Pmx->getRbByVtxIdOrNode(-2, 0, ndPinFootL);
	//Pmx->getRbByVtxIdOrNode(0, ndFootR, ndPinFootR); Pmx->getRbByVtxIdOrNode(-2, 0, ndPinFootR);
	//auto ndRoot = Pmx->GetNodeManager()->GetMMDNode(Pmx->rootRbBoneIdx);
	Pmx->getRbByVtxIdOrNode(0, ndc, ndPinYao); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr);

#else
	if (ndFingerR) return;
	if (!ndFingerR) ndFingerR = setNodeFlag(L"右人指３", EId::handL, 0);
	//if (!ndPinHandL) ndPinHandL = Pmx->GetNodeManager()->FindNode(L"pinHandL");
	if (!ndPinHandR) ndPinHandR = Pmx->GetNodeManager()->FindNode(L"pinHandR");

	Pmx->clearVtxPickNodes();
	Pmx->UpdateNodeAnimation(false);
	Pmx->UpdateNodeAnimation(true);

	//Pmx->getRbByVtxIdOrNode(0, ndHandL, ndPinHandL); Pmx->getRbByVtxIdOrNode(-2, 0, ndPinHandL);
	//if (itemIdx==0 && SVG_MMD_WRITE) Pmx->getRbByVtxIdOrNode(0, ndFingerR, ndPinHandR); Pmx->getRbByVtxIdOrNode(-2, 0, ndPinHandR);
	//Pmx->getRbByVtxIdOrNode(0, Pmx->GetNodeManager()->FindNode(L"上半身"), ndPinYao); Pmx->getRbByVtxIdOrNode(-2, 0, nullptr);
#endif
}


std::vector<std::wstring> listFilesMatchingPattern(const std::wstring& directoryPath, const std::wstring& pattern) {
	std::vector<std::wstring> matchingFiles;
	std::wregex regexPattern(pattern);
	if (!fs::exists(directoryPath))
		return {};
	for (const auto& entry : fs::directory_iterator(directoryPath)) {
		if (fs::is_regular_file(entry.path())) {
			std::wstring filename = entry.path().filename().wstring();
			if (std::regex_match(filename, regexPattern)) {
				matchingFiles.push_back(entry.path().wstring());
			}
		}
	}

	return matchingFiles;
}

void irr::scene::IrrSaba::parseMmdMatMemo(saba::MMDMaterial& mmdMat)
{
	UaJson js;
	if (!js.ParseStr(mmdMat.m_memo))
		return;
	Json::Value root = js.copyRootValue();
	auto hl = root["bloom"];
	if (!hl.isNull()) {
		mmdMat.Bloom = UaJson::parseHex(hl["color"].asString());
		hasBloom = true;
		setPassType(IrrPassType_GlowBase, true);
	}

	auto rndTex = root["rndTexDir"].asString();//REGEX match files in sub dir {"rndTexDir":"mj", "regex":".*\\.png"} = mj1.png,mj2.png...
	if (rndTex.size() > 0) {
		if (rndTex[0] == '=') {
			int id = atoi(rndTex.substr(1, -1).c_str());
			if (id < mModel->GetMaterialCount())
				mmdMat.m_texture = mModel->GetMaterials()[id].m_texture;
		}
		else {
			fs::path fp = ualib::Utf8toWcs(mmdMat.m_texture);
			auto dir = fp.parent_path() / rndTex;
			std::vector<std::wstring> files = listFilesMatchingPattern(dir.wstring(), ualib::Utf8toWcs(root["regex"].asString()));
			if (files.size() == 0)
				return;
			mmdMat.m_texture = ualib::WcstoUtf8(files[UaRand(files.size())]);
		}
	}


	if (mmdMat.rainbow = root["rainbow"].asInt()) {
		mmdMat.rainbowSpeed = root.get("rainbowSpeed", 1.f).asFloat();
		mmdMat.rainbowSbOfs = root.get("rainbowSbOfs", 1.f).asFloat();
		mmdMat.rainbowMtrOfs = root.get("rainbowMtrOfs", 1.f).asFloat();
		if (mmdMat.rainbow & 0x10)
		mmdMat.diffuseMul = vec4(1, 0, 0, 1);
	}
	if (root.isMember("spaIsNormalMap")) {
		mmdMat.spaIsNormalMap = root["spaIsNormalMap"].asInt();
	}
	if (mmdMat.subMeshAlphaMod = root["smAlpha"].asInt()) {

	}

	mmdMat.centerTransparent = root["ctrTrans"].asInt();
	if (root["oit"].asInt() == 1) useOIT = true;
	if (root.isMember("alphaMul"))
		mmdMat.alphaMul = root["alphaMul"].asFloat();

}

void irr::scene::IrrSaba::createIkLabels(int labelFlag, saba::MMDNode* rootNode, bool isRight)
{
	if (curLabelCreateMark[labelFlag] != 0) return;
	if (!ikVRoot) {
		ikVRoot = new CLabelSceneNode(100, this, SceneManager, -1);

		ikVRoot->drop();
	}
	ikVRoot->setPassType(IrrPassType_Normal, false); ikVRoot->setPassType(IrrPassType_3D_UI, true);

	auto onNode = [=](saba::MMDNode* nd, int flag, int& lbId, bool forceAdd = false)
		{
			if (nd && nd->exd.ctrlLabel == -1 && (
				forceAdd ||
				(flag == 0 && nd->IsIK() && nd->flVisible)
				|| (nd->GetIndex() == 1 && nd->GetParent() == nullptr)
				|| ((//!nd->IsPhysicsActive &&
					(nd->flRotate || nd->flMove)) && (needControl(nd, flag)))
				) && nd != ndHeadIK			//&& nd->flVisible
				)
			{
				CLabelSceneNode::Item it;
				float sr = flag == 0 ? 100 : 50;
				if (IS_WIN) sr /= 2;
				it.size.set(sr, sr);
				auto& n = nd->GetNameU();
				it.eType = nd->flMove ? CLabelSceneNode::Item::eliRect : CLabelSceneNode::Item::eliRing;

				labelItems.push_back(it);
				PickNodesInfo pi{}; pi.nd = nd; pi.lbId = lbId++; pi.sizeR = sr;
				pi.color = nd->GetIndex() < 2 ? 0xFFFF8000 : (nd->flMove ? (n[1] == L'足' ? 0xC000FFFF : n[1] == L'つ' ? 0xC0008080 : 0xC000FF00) : 0x60808080);
				snMarks[nd->GetIndex()] = pi;
				nd->exd.ctrlLabel = flag;
				if (rootNode)
				{
					auto& ws = nd->GetNameU();
					if (ws[1] == L'親')  nd->exd.rttRecursionAdd = glm::vec3(-0.5, isRight ? 0.15 : -0.15, 0);
					else	nd->exd.rttRecursionAdd = glm::vec3(0, 0, isRight ? 1 : -1);
				}
				DPWCS((L"Vnode %d %d %s", nd->flRotate, nd->flMove, nd->GetNameU().c_str()));
			}
		};
	//	SceneManager->addEmptySceneNode(this);

	size_t ndSize = mModel->GetNodeManager()->GetNodeCount();
	if (!Pmx->isCharacter)
		onNode(mModel->GetNodeManager()->GetMMDNode(0), 0, lbId, true);
	if (rootNode) {
		rootNode->forEachSubNodes([&](MMDNode* node) {
			DPWCS((L"n %s", node->GetNameU().c_str()));
			if (snMarks.find(node->GetIndex()) == snMarks.end())
				if (!node->IsIK()) onNode(node, labelFlag, lbId);
			});
	}
	else for (size_t i = 0; i < ndSize; i++)
	{
		auto node = mModel->GetNodeManager()->GetMMDNode(i);
		//if (i < 6 && snMarks.size()>1 ) 	continue;
		if (Pmx->getTmpIkNode() != node)
			onNode(node, labelFlag, lbId);
	}
	if (labelItems.size() == 0)
		onNode(mModel->GetNodeManager()->GetMMDNode(0), 0, lbId, true);
	if (labelItems.size()) {
		ikVRoot->craeteItems(labelItems);
		curLabelCreateMark[labelFlag] = 1;
	}

}



irr::video::ITexture* irr::scene::IrrSaba::getModelTexture(irr::io::path fp) {
	return mModel->getFileArchive()
		? Driver->getTextureFromArchive(fp, mModel->getFileArchive(), 3)
		: Driver->getTexture(fp);
}


bool irr::scene::IrrSaba::loadPose(irr::io::path file, int slot)
{
	VPDFile vf{};
	if (slot < 0)
		mModel->setPoseId(slot);
	else
		if (saba::ReadVPDFile(&vf, file.c_strA()))
		{
			mModel->LoadPose(vf, 30, true, slot);
			mModel->setPoseId(slot);
			return true;
		}
	return false;
}

bool irr::scene::IrrSaba::loadPoseFromJson(Json::Value jsv)
{
	mmdPoseFromJson(mModel.get(), jsv);
	return false;
}


//animation

bool irr::scene::IrrSaba::loadMotion(io::path filepath, uint32_t addKeyFrame)
{
	auto sb = this;
	if (filepath.size() == 0) return false;
	sb->setPlaying(false);
	sb->startRagDoll({ 0,0.1 });
	if (filepath.pathGetExt().make_lower() == ".vpd") {
		if (sb->hasAnimation())
		{
			auto ret = sb->loadPose(filepath, 0);
			if (addKeyFrame) sb->addAnimationKeyFrame(addKeyFrame);
			return ret;
		}
		else
		{
			sb->syncToFrame(99999, 30, 2);
			return sb->loadPose(filepath, 0);
		}
	}
	else
		return sb->loadAnimation(filepath);

	return false;
}

bool irr::scene::IrrSaba::loadAnimation(irr::io::path file, bool isCamAni, bool resetPhy)
{
	bool append = Vmd != nullptr && Ctx->getEvtRcv()->IsKeyDown(KEY_LSHIFT);

	if (file.size() < 1) file = motionFile;
	if (file.size() < 1)
		return true;

	bool loaded = Pmx->vmdMap.contains(file.c_str());
	// Load VMD animation
	auto vmdAnim = append ? Vmd : loaded ? Pmx->vmdMap[file.c_str()] : std::make_shared<saba::VMDAnimation>();
	if (!vmdAnim->Create(mModel))
	{
		std::cout << "Failed to create VMDAnimation.\n";
		return false;
	}


	if (loaded)
	{
		resetAnimation(0);
		Vmd = vmdAnim;
	}
	else
	{
		double ofs = 0;

		std::string pathPhoneme = Pm.fcp.phlabPath.size() > 1 ? ualib::WcharToAnsi(Pm.fcp.phlabPath) : Pm.mmd->mdplr.getLabFile(itemIdx, ofs);
		if (!append) vmdAnim->loadPhoneme(pathPhoneme, ofs, 1);// double(SYNC_MIDI_BPM) / 120.0);


		saba::VMDFile vmdFile;
		if (!saba::ReadVMDFile(&vmdFile, file.std_string().c_str()))
		{
			std::cout << "Failed to read VMD file.\n";
			return false;
		}
		if (Pm.skipLastKey)
			saba::RemoveMaxFrameMotion(&vmdFile);
		if (!isCamAni)
			if (!vmdAnim->Add(vmdFile))
			{
				std::cout << "Failed to add VMDAnimation.\n";
				return false;
			}
		if (vmdFile.pJvRoot) {
			vee = new VmdEventExt();
			vee->loadFromJson(*vmdFile.pJvRoot);
			vee->onEvent = std::bind(&IrrSaba::onVmdEvent, this, std::placeholders::_1);
			for (auto& pa : vee->phans) {
				pa.nd = findNode(ualib::Utf8toWcs(pa.node));
				if (pa.nd) {
					setPhyAnim(pa.nd, true, false);
					Pmx->rootTr = {}; Pmx->rootRt = quat(1, 0, 0, 0);
					Pmx->rt1Tr = { 0,0,0 }; Pmx->rt1Rt = { 0,0,0 };
					Pmx->UpdateNodeAnimation(false);
					pa.nd->phyAnimRatT = pa.linVelMul;
					pa.nd->phyAnimRatR = pa.angVelMul;
				}
			}
		}
		if (!vmdFile.m_cameras.empty())
		{
			isCamAni = true;
			//assert(isCamAni);
			//if (!isCamAni)				return false;
			auto vmdCamAnim = std::make_shared<saba::VMDCameraAnimation>();
			if (!vmdCamAnim->Create(vmdFile))
			{
				std::cout << "Failed to create VMDCameraAnimation.\n";
			}
			m_vmdCameraAnim = std::move(vmdCamAnim);

		}
		if (!isCamAni && !append) {
			clearAnimation();
		}

		if (!isCamAni)
		{
			if (!append) {
#if MMD_SAVE_VMD_MORPH || MMD_SAVE_VMD_MOTION
				writeVmdFile = file;
#endif

				motionFile = file;

				Vmd = std::move(vmdAnim);
				Pmx->vmdMap[file.c_str()] = Vmd;
				Vmd->filePath = file.c_str();
				mModel->vmdAddAnims.clear();

				resetAnimation(0, false, resetPhy ? (IS_WIN ? 60 : 30) : 0);

				for (auto& hdl : nodeHandlers)
					hdl->onAnimeLoaded();
			}
		}
		else
		{
			if (Pm.needSaveCamVmd)
				writeVmdFile = file;
		}
	}

	//vmdAnim->SyncPhysics(0.0f );





	//exportAnimationStart();
	return true;
}
void irr::scene::IrrSaba::clearAnimation() {
	if (!Vmd.get())
		return;
	for (size_t i = 0; i < mNodeMan->GetNodeCount(); i++)
		mNodeMan->GetMMDNode(i)->nodeCtrl = nullptr;
	Vmd = nullptr;
	Pmx->LoadBaseAnimation(Pmx->fcp.addRootBone);
};
//nodefw mmdfw
void irr::scene::IrrSaba::onNodeFirework(irr::core::vector3df& dtPos, saba::MMDNode& node)
{
#define  FW_FORCE_LAUNCH 1

	if (!Pm.hasFW || MMD_FW == 0)
		return;
	irr::core::vector3df lastPos = node.exd.lastPos;
	auto& ne = node.exd;
	if (ne.fwCount == 0) return;
	core::matrix4& ndGlmIrrM = *(core::matrix4*)&node.GetGlobalTransform();
	auto apy = ndGlmIrrM.getTranslation();
	//DP(("apy %f,%f,%f   %s",apy.X,apy.Y,apy.Z, node.GetName().c_str()));
	bool bfw = (Ctx->gd.time - ne.fwLastFwS >= ne.fwIntvl
		//&& ne.lastModelPos.y>apy.Y  && apy.Y < 1.5f
		);
	//DP(("apy %f,   %s !!!!!!!!!!!!!!!!!!!!!!!!!!!", apy, node.GetName().c_str()));
	if (bfw) ne.fwLastFwS = Ctx->gd.time;

	auto fw = ne.fwt1Idx == 0 ? Eqv->getCurPtrFw(1) : Eqv->getPtrFw(1, ne.fwt1Idx);
	int fid = fw->gfd.FwId;

	float durTIme = (animeTime - ne.lastTime);

	if (FW_FORCE_LAUNCH || durTIme > 0.0001f)
	{

		int fwDis = fw->cldFwDis; fwDis = fwDis < 1 ? 8 : fwDis;
		ne.disAcc += dtPos.getLength();
		int steps = FW_FORCE_LAUNCH ? 1 : int(ne.disAcc / fwDis);
		ne.disAcc -= steps * fwDis;
		//DbgPrint("lfw %d", steps);
		steps = core::clamp(steps, 0, 10);
		auto posStep = dtPos / steps;
		float timeStep = durTIme / steps;
		core::vector3df  cPos;
		if (FW_FORCE_LAUNCH || ne.irrSpeed.length() > 2)
		{
			for (int i = 1; i <= steps; i++)
			{
				cPos = lastPos + posStep;
				cPos.Y = std::max(90.f, cPos.Y);
				lastPos = cPos;

				int c = 1;
				for (; c > 0; c--) {
					static float hue = 0.0f;

#if MMD_HAND_FW_MODE
					if (ne.fwCount > 0) ne.fwCount--;
					hue += 0.1;
					SColorHSL hsl(hue, 50, 50);
					SColor sc = hsl.toSColor();
					core::vector3df vec = ne.aniSpeed;
#else
					hue += 0.2;
					const float limMinV = 10;
					core::vector3df vec =
						ne.direction * 1000.f;
					float vlen = vec.getLength();
					SColorHSL hsl(hue, 50, FW_FORCE_LAUNCH ? 50 : std::min(50.f, vlen / 5));
					SColor sc = hsl.toSColor();
					if (!FW_FORCE_LAUNCH) sc.setAlpha((hsl.Luminance) * 255 / 100);
					if (FW_FORCE_LAUNCH || vlen > limMinV && sc.getAlpha() > 32)
#endif
					{
						if (bfw) Eqv->LaunchFw3D(V3dToFloat3(cPos),
							//Eqv->getFwIdx(ne.fwGroup, ne.fwId),
							fid, vec
							, sc);
						Eqv->pfAddForce(Ctx->gd.time + .036f, cPos, ne.irrSpeed, -1, 1);
					}
				}
				//DP((L"Spd %d %f,%f %s  ", i, ne.aniSpeed.x, ne.aniSpeed.y, ws.c_str()));
			}
		}
	}
}

//! pre render event
void irr::scene::IrrSaba::OnRegisterSceneNode()
{
	bool isPickPass = Driver->getPassType() == IrrPassType_PickPoint;
	if (IsVisible) {
		setAutomaticCulling(Eqv->pvp->recording || MMD_AR_BALL || IS_WIN ? scene::EAC_OFF : scene::EAC_FRUSTUM_BOX);
		//if (!isPickPass   || Pm.mmd->cs->pickPhysics)

		if (!isPickPass || isPickPass
#if MMD_PICK_CHARACTER_ONLY
			&& isHumanModel()
#endif
			) {
			isOIT = FW_HAS_OIT && OIT_ON && useOIT && Driver->getPassType() == IrrPassType_Normal;
			SceneManager->registerNodeForRendering(this, MULTI_THREAD_RENDERING && Pm.mmd->paraRender ? ESNRP_PARALLEL : (isOIT) ? ESNRP_TRANSPARENT : ESNRP_SOLID);
		}
	}
	if (ikVRoot) ikVRoot->setVisible((Pm.mmd->ikVisable && mdViewNode && !Eqv->pvp->recording /*&& !Pmx->GetPhysicsManager()->getDynRbActive()*/ || isPickPass)
		&& Pm.snArItem && Pm.snArItem->isCur());

	ISceneNode::OnRegisterSceneNode();//children
}

void irr::scene::IrrSaba::updateTransform()
{
	if (parentAttechNode && (!parentAttechNode->rb0 || !parentAttechNode->rb0->GetActivation()) )
	{
		Parent->updateTransform();
		auto ms =  parentAttechNode->GetGlobalTransform();
		AbsoluteTransformation = Parent->getAbsoluteTransformation() * core::matrix4(ms) * getRelativeTransformation();// *matToParentNode;
	}
	else {
		Parent->updateTransform();
		core::matrix4& ms = *(core::matrix4*)&Parent->getAbsoluteTransformation();
		AbsoluteTransformation = Parent->getAbsoluteTransformation() * getRelativeTransformation();// *matToParentNode;
	}
}

void irr::scene::IrrSaba::updateAbsolutePosition()
{
	if (parentAttechNode && (!parentAttechNode->rb0 || !parentAttechNode->rb0->GetActivation()))
	{
		core::matrix4& ms = *(core::matrix4*)&parentAttechNode->GetGlobalTransform();
		Pmx->mtRootParent = ms * getRelativeTransformationTR();//phy not support scale
		AbsoluteTransformation = Parent->getAbsoluteTransformation() * Pmx->mtRootParent;//  *matToParentNode;
	}
	else {
		AbsoluteTransformation = Parent->getAbsoluteTransformation() * getRelativeTransformation();// *matToParentNode;
	}
}

void irr::scene::IrrSaba::OnAnimate(u32 timeMs)
{


	if (frameRtt)
	{
		auto nd = ndFrameRtt ? ndFrameRtt : ndRoot;
		nd->SetAnimationRotate(nd->GetAnimationRotate() * glm::quat(frameRttVec));
	}


	updateAbsolutePosition();//need?

	mmdBaseMat = AbsoluteTransformation;
	//assert(mmdBaseMat[0] == Pm.mmd->baseMatrix[0] || getItemIdx()<0);
	//if (itemIdx == 0)	Pm.mmd->baseMatrix = mmdBaseMat;
	mmdBaseMat.getInverse(mmdBaseInv);
	//Pmx->mmdRootMat = getRelativeTransformation();
	//Pmx->baseMatInv =  glm::inverse(Pmx->mmdRootMat);
	auto cam = Ctx->getViewCamera();
	//cam->bindTargetAndRotation(true); cam->updateTransform(); cam->updateMatrices();
	if (phyObj && phyObj->ndLock.size() > 0) {
		auto nd = (*(phyObj->ndLock.begin()))->model->saba->ndHead;
		mmdLookAt = nd->rb0->getNodeTransform()[3];
		mmdCamRtt = quat_cast(nd->rb0->getNodeTransform());
		isLookAtCam = false;
	}
	else if (lookAt_mmdLookAt)
	{
		isLookAtCam = false;
		sbFw2D("sw21s", mmdLookAt, vec3(0, 0, 0), 0x3FFF0000);
		sbFw2D("sw2", ndHead->rbPos() + vec3(0, 3, 0), vec3(0, 0, 0), 0xFFFF0000);
	}
	else
	{
		mat4 mc = mmdBaseInv * cam->getAbsoluteTransformation();
		glm::vec3 scale;
		glm::vec3 skew;
		glm::vec4 perspective;
		glm::decompose(mc, scale, mmdCamRtt, mmdLookAt, skew, perspective);
		isLookAtCam = true;

	}
	mtCam = glm::translate(mat4(1), mmdLookAt) * glm::toMat4(mmdCamRtt);


	//ndHandR->rb0->addLinearVelToPos({ 0, 10, 1 },200);
	//DP(("CAMPOS %8.3f  %8.3f  %8.3f",mmdCamPos.x, mmdCamPos.y, mmdCamPos.z));
	if (sbMpa) sbMpa->update();
	if (IsVisible || HAS_PIANO)
		updateMMD(Pm.mmd->updating && timeMs > 0 ? Driver->dsd.deltaTime : 0.f);
	if (IsVisible)	if (sbCloth && svgMan && svgMan->isWriteOnCloth)
		sbCloth->vtxMatScale = svgPm.vtxMatScale / svgMan->totalMaxXY / MMD_SABA_SCALE;

	//CEmptySceneNode::OnAnimate(timeMs);
	ISceneNodeList::Iterator it = Children.begin();
	if (!paraChildrenAnimate || Children.size() < 2)
		for (; it != Children.end(); ++it)
		{
			ISceneNode* sn = *it;
			if (!sn->skipOnAnimateByParent)
				sn->OnAnimate(timeMs);
		}


	//if (itemIdx == 0) 	Pom->groundRb->SetCoMTransform(ndUpper2->GetGlobalTransform()*glm::translate(mat4(1),vec3(0,-20,0)));

	if (MMDPhysics::phyDbgView) {
#if SABA_USE_PHYSX
		if (itemIdx == 0)
		{

			mmdPhysics->getDbgLineBuf();

		}
#endif
	}
	if (Pm.mmd->camSb == this && Pm.mmd->camSbTgtSb.get() && Pm.mmd->camSbTgtSb.get() != this && Pm.mmd->camSbTgtNd)
	{
		auto nd = //ndCamPos ? ndCamPos :
			ndHead;
		auto rb0 = nd->rb0 ? nd->rb0 : nd->GetParent()->rb0;
		rb0->setAngVelToPos(Pm.mmd->camSbTgtNd->getGlobalPos(), 3);
		auto tsb = Pm.mmd->camSbTgtSb;
		if (Pm.mmd->camSbTgtNd == tsb->ndHead || tsb->ndHead && Pm.mmd->camSbTgtNd->GetParent() == tsb->ndHead) {
			tsb->ndHead->rb0->setAngVelToPos(nd->getGlobalPos(), 100);
		}
	}

	if (isCamerer) {
		setPassType(IrrPassType_ShadowMap, false);
		mat4 mc = mmdBaseInv * cam->getAbsoluteTransformation();
		glm::vec3 scale, pos, skew;
		glm::quat qr;
		glm::vec4 perspective;
		glm::decompose(mc, scale, qr, pos, skew, perspective);
		if (!ndHead->rb0->GetActivation()) {
			//setAllDynRbActive(true); localPhyAnim = true;//setBonePhsAnime(ndYao, true, true);
			//ndHead->rb0->SetActivation(true);
		}
		loadPose("data/mmd/camerer.vpd", 0);
		mc = glm::translate(mat4(1), pos) * glm::toMat4(qr) * glm::translate(mat4(1), vec3(0, -9, -5.3)) * glm::rotate(mat4(1), core::PI, vec3(0, 1, 0));
		//ndHead->rb0->scaleVel(0.5f, 3);
		//ndHead->rb0->addLinearVelToPosLimitDis(glh::matTransformVec(mc, vec3(0, 3, -3)), 30, 0, 2);
		//ndHead->rb0->addRotationToMatOnNode(mc , 100);
		//ndUpper2->rb0->addRotationToMatOnNode(mc , 100);
		ndRoot->setAnimationMatrix(mc);

	}
	if (firstUpdate)
	{
		firstUpdate = false;
		int origNodes = mNodeMan->GetNodeCount() - 1;
		for (int i = 0; i < origNodes; i++) {
			auto nd = mNodeMan->GetMMDNode(i);

			if (nd->pmxBone->pjv)
			{
				processNodeJPV(nd);
			}
		}
	}
}



void irr::scene::IrrSaba::mmdFw(float3 pos, int fwIdx, float3 vec, SColorf col, LfwParam* pm, float3* tcl)
{
	if (MMD_SABA_SCALE != 1.f)
	{
		pos = mmd2irr(pos); vec = mmd2irr(vec);
	}
	else
	{
		//pos *= 100.f / MMD_SABA_SCALE; vec *= 100.f / MMD_SABA_SCALE;
	}
	LfwParam lp; lp.kldMode = 1;
	lp.kldCount = 8; pm = &lp;
	Eqv->LaunchFw3D(pos, fwIdx, vec, col, pm, tcl);
}
void irr::scene::IrrSaba::mmdFw(int ptrType, std::string_view idStr, float3 pos, float3 vel, SColor color)
{
	int fwIdx = Eqv->getFwIdxByFwIdStr(ptrType, idStr);
	if (MMD_SABA_SCALE != 1.f)
	{
		pos = mmd2irr(pos); vel = mmd2irr(vel);
	}
	else
	{
		//pos *= 100.f / MMD_SABA_SCALE; vec *= 100.f / MMD_SABA_SCALE;
	}
	Eqv->LaunchFw3D(pos, fwIdx, vel, SColorf(color), 0, 0);
}
void irr::scene::IrrSaba::mmdFwLine(int ptrType, std::string_view idStr, float3 pos, float3 pos2, SColor color, float step, float startStepRate)
{
	step *= MMD_SABA_SCALE / 100.f;
	int fwIdx = Eqv->getFwIdxByFwIdStr(ptrType, idStr);
	pos = mmd2irr(pos); pos2 = mmd2irr(pos2);
	int steps = int(glm::length(pos2 - pos) / step);
	float3 ofs = (pos2 - pos) / float(steps);
	pos += ofs * startStepRate;
	for (int i = 0; i < steps; i++) {
		Eqv->LaunchFw3D(pos, fwIdx, float3(0), SColorf(color), 0, 0);
		pos += ofs;
	}

}


void irr::scene::IrrSaba::setAddRttNode(size_t& ndAddRttIdx, const glm::quat& ndAddRttQt, size_t idx, core::vector3df rtt, bool reset)
{
	if (idx < mNodeMan->GetNodeCount()) {
		if (ndAddRttIdx < mNodeMan->GetNodeCount())
			setNodeAddRotation(nullptr, ndAddRttIdx, nullptr);
		ndAddRttIdx = setNodeAddRotation(nullptr, idx, &ndAddRttQt);
	}
}

size_t irr::scene::IrrSaba::setNodeAddRotation(const wchar_t* name, size_t idx, const glm::quat* rtt)
{
	if (name)
		idx = mNodeMan->FindNodeIndex(name);
	if (idx > 0)
		mNodeMan->GetMMDNode(idx)->setAddRotation(rtt);

	return idx;
}

core::vector3df irr::scene::IrrSaba::mapOnCloth(core::vector3df pos, SColorf* scf)
{
	auto p3d = sbCloth->snCloth->mapTextureToModel(pos, scf);
	return p3d;
}

void irr::scene::IrrSaba::onVmdEvent(const SVmdEvent& e)
{
	switch (e.eventType)
	{
	case SVmdEvent::eAddVel:
	{
		DP(("onVmdEvent::eAddVel"));
		auto pos = ndHandR->rb0->getPosition(), dir = glm::mat3(ndHandR->rb0->getNodeTransform()) * e.addVelParams.dir;
		FrameWaiter fw;
		//auto pVel = make_SPHolder(e.addVelParams.vel);
		static float vel = 100.f;
		fw.waitNframeAndRun(0, [=](FWTask& t) {
			PH::rbBlow(pos, 2.f, glm::vec3{ 0,0,-1 }, vel, [=](vec3 pos) { MMDFW(2, "sw1s", pos, vec3(0), SColorf(1, 1, 1, 0.33f)); });
			//DP(("pVel %f uc%d tcd:%d", pVel->x,pVel.use_count(),t.repeatCountDown));
			if (t.repeatCountDown == 1)
				vel *= 2.f;
			//t.repeatCountDown = 0;
			}, 1, 1);
	}
	break;
	default:
	{
		cbVmdEvent(e);
	}
	break;
	}
}


MMDNode* irr::scene::IrrSaba::setNodeFlag(const wchar_t* name, EId eid, uint32_t flag, bool addFlag)
{
	for (int i = 0; i < mNodeMan->GetNodeCount(); i++)
	{
		MMDNode* n = mNodeMan->GetMMDNode(i);
		if (n->GetNameU() == name)
		{
			if (addFlag) n->exd.flag |= flag;
			else  n->exd.flag = flag;
			n->exd.eId = eid;
			return n;
		}
	}
	return nullptr;
}
MMDNode* irr::scene::IrrSaba::findNode(const std::wstring name)
{
	for (int i = 0; i < mNodeMan->GetNodeCount(); i++)
	{
		MMDNode* n = mNodeMan->GetMMDNode(i);
		if (n->GetNameU() == name)
		{
			return n;
		}
	}
	return nullptr;
}

MMDNode* irr::scene::IrrSaba::setNodeFlag(int nodeIdx, EId eid, uint32_t flag, bool addFlag)
{
	if (nodeIdx < mNodeMan->GetNodeCount())
	{
		MMDNode* n = mNodeMan->GetMMDNode(nodeIdx);
		if (addFlag) n->exd.flag |= flag;
		else  n->exd.flag = flag;
		n->exd.eId = eid;
		return n;
	}
	return nullptr;
}
MMDMorph* irr::scene::IrrSaba::setMorphFlag(const wchar_t* name, int mid, uint32_t flag, bool addFlag)
{
	MMDMorphManager* mm = mModel->GetMorphManager();
	int mc = mm->GetMorphCount();
	for (int i = 0; i < mc; i++)
	{
		MMDMorph* n = mm->GetMorph(i);
		if (n->GetNameW() == name)
		{
			n->exd.eId = mid;
			if (addFlag) n->exd.flag |= flag;
			else  n->exd.flag = flag;
			return n;
		}
	}
	return nullptr;
}

void irr::scene::IrrSaba::setMorphValue(const wchar_t* name, float val)
{
	MMDMorphManager* mm = mModel->GetMorphManager();
	int mc = mm->GetMorphCount();
	for (int i = 0; i < mc; i++)
	{
		MMDMorph* n = mm->GetMorph(i);
		if (n->GetNameW() == name)
		{
			n->SetWeight(val);
			break;
		}
	}
}

void irr::scene::IrrSaba::switchToEyeCam(bool toEye)
{
#if !USE_AR_DATA
#if MMD_JOYSTICK_GAMECAST
	if (!eyePosSet) return;
	if (toEye)
		camId = Ctx->getCameraId();
	Ctx->setCameraId(toEye ? 0 : camId);
#else

#endif
	if (!toEye)
		Ctx->gd.CamNormal->interpolateToCam(Ctx->gd.CamRtt, 3.f);
#endif
}



void irr::scene::IrrSaba::enableLeap(bool isOn)
{
	if (DISABLE_LEAP_ONSTART && ndIKArmL) { ndIKArmL->EnableIK(isOn); ndIKArmR->EnableIK(isOn); }

	leapOn = isOn;
}


void irr::scene::IrrSaba::createInflate()
{
#if PHY_GPU
	if (HAS_INFLATE && itemIdx == 0 && !snInflate) {
		FrameWaiter fw; fw.waitNframeAndRun(1, [=](FWTask& t) {
			snInflate = new SnInflatable(10.f, this, SceneManager, -1);
			});
	}
	else if (snInflate) snInflate->resetPos();
#endif
}



void irr::scene::IrrSaba::addRb2NdPosForce(saba::MMDNode* nd, float xm, float ym, float zm, float rttmul)
{
	if (!nd || nd->phyAnim) return;
	auto yp = nd->rb0->getPosition();
	auto dir = (nd->getRbAnimGlobalPos() - yp);
	if (dir.y > 0) dir.y = pow(dir.y, 1); else dir.y = 0; dir.x *= xm; dir.y *= ym; dir.z *= zm;
	if (centerForceMul >= 0.5f) nd->rb0->scaleVel(pow(0.78f, centerForceMul), 1);

	nd->rb0->addLinearVel(dir * ((centerForceMul < 1.f ? centerForceMul * centerForceVec : vec3(1)) * (1.f))); //CENTER FORCE
	nd->rb0->setAngVelToRotateOnNode(nd->mGlobalAnim, rttmul * (centerForceMul < 1.f ? centerForceVec.y : 1));
}

void irr::scene::IrrSaba::updateMMD(float deltaTime)
{
	stepTime = deltaTime;
	zyTimer -= deltaTime; zyPast += deltaTime;

	if (!hasModel) return;
	if (parentAttechNode && parentAttechNode->rb0 && parentAttechNode->rb0->GetActivation()) {
		auto m = parentAttechNode->rb0->getNodeTransform();
		auto rtt = quat_cast(m);
		auto pos = vec3(m[3]);
		Pmx->rootRt = rtt;
		Pmx->rootTr = pos;
	}
	//else if (ndYao && ndYao->rb0->GetActivation() && localPhyAnim)
	//{
	//	auto m = ndYao->rb0->getNodeTransform();	
	//	auto rtt = quat_cast(m);
	//	rtt.x = rtt.z = 0;
	//	Pmx->rootRt = rtt; //ndRoot->SetAnimationRotate(rtt);		
	//}
 
	mmdPhysics->SetFPS(Eqv->pvp->working ? PHYSICS_FPS_RENDER : PHYSICS_FPS);
	//updatePhysicsStep(0, 3);
	if (glm::length2(phyRtt) >= 0.01f && ndRbRoot) {
		ndRbRoot->rb0->addTorqueOnMatRtt(ndRbRoot->GetGlobalTransform(), phyRtt * float(1000.f / Ctx->gd.timeMul), true);
	}

	if (hasBloom) Pm.mmd->hasBloom = true;// update every frame
	if (frameRestore) {
		Pmx->restoreState(std::max(100.f, rd.phyAniMul * 1000), 1);

	}
	for (auto& hdl : nodeHandlers)
		hdl->onUpdate(Ctx->gd.time, deltaTime);
	//if (snPickBall) snPickBall->setMaterialTexture(0, Ctx->gd.texBg);
	auto driver = SceneManager->getVideoDriver();
#if DRAW_SHADER_TOY_MMDTEX
	{
		driver->setRenderTarget(texSdtoy, 1, 1, 0);
		Eqv->drawShaderToy(nullptr);
		driver->flush();
	}
#else
	curPeak = glm::mix(curPeak, Eqv->CurWavePeak, curPeak < Eqv->CurWavePeak ? 0.5 : 0.5);
	if (texCoat)
	{

		if (!texFuRt) texFuRt = driver->addRenderTargetTexture(texCoat->getSize(), "texFuRt");
		if (!texFuCl) texFuCl = texCoat->Clone("texFuCl");
		driver->setRenderTarget(texFuRt);

		video::SMaterial mr;
		mr.MaterialType = DRV_GET_MT(driver, Fx2DIdEnum::Fx2D_ProcessImage);
		mr.setTexture(0, texFuCl);
		video::VkMr2D* mr2d = (video::VkMr2D*)driver->getMaterialRenderer(mr.MaterialType);
#if EQV_TEXFU
		mr2d->cbDraw.processImage.mode = CbMr2D::epi_transGrY;
		mr2d->cbDraw.processImage.fv.y = std::max(0.5, curPeak * 0.7 + 0.3);
#else
		int maxc = 60;
		static int cc = 0; cc = (cc + 1) % maxc;

		mr2d->cbDraw.processImage.mode = CbMr2D::epi_hsl;
		mr2d->cbDraw.processImage.fv.x = float(cc) / maxc;
#endif
		driver->draw2DImageMr(mr, texFuCl->getRectI(), texFuCl->getRectI());
		driver->flush();
		//driver->saveTexture(texFuRt, "out/out.png");
		texFuRt->copyTo(texCoat);

	}
	if (texBott)
	{
		if (!texFuRt) texFuRt = driver->addRenderTargetTexture(texBott->getSize(), "texBottRt");
		if (!texBtCl) texBtCl = texBott->Clone("texBottCl");
		driver->setRenderTarget(texFuRt);

		video::SMaterial mr;
		mr.MaterialType = DRV_GET_MT(driver, Fx2DIdEnum::Fx2D_ProcessImage);
		mr.setTexture(0, texBtCl);
		video::VkMr2D* mr2d = (video::VkMr2D*)driver->getMaterialRenderer(mr.MaterialType);
		mr2d->cbDraw.processImage.mode = CbMr2D::epi_transGrY;
		mr2d->cbDraw.processImage.fv.y = std::max(0.0, curPeak * 0.75 + 0);
		driver->draw2DImageMr(mr, texBtCl->getRectI(), texBtCl->getRectI());
		driver->flush();
		//driver->saveTexture(texFuRt, "out/out.png");
		texFuRt->copyTo(texBott);

		if (Eqv->isPeakPeak) {
			auto p = core::vector3df(ndYao->GetGlobalTransform()[3]); mmdToIrrPos(p);
			Eqv->LaunchFw3D(p, Eqv->getFwIdx(1, 2), { 0,0,0 });

		}
	}
	else if (Eqv->isPeakPeak && ndYao) {
		auto p = core::vector3df(ndYao->GetGlobalTransform()[3]); mmdToIrrPos(p);
		//Eqv->LaunchFw3D(p, Eqv->getFwIdx(1, 2), { 0,0,0 });
		//ndYao->rb0->addLinearVel({ 0,Eqv->AvrWavePeak*600.f,0 });
#if 0
		auto freqToMidiKey = [](float freq) {
			if (freq <= 0.0f)	return 0;
			int midiKey = static_cast<int>(round(12.0 * log2(freq / 440.0) + 69.0));
			return core::clamp(midiKey, 0, 127);
			};
		actVoiceFx(getItemIdx() + Pm.mmd->mdplr.mmdCount, 5, freqToMidiKey(Eqv->peakFreq), L"ha",1);
#endif
	}
#endif

	float frame = animeTime * 30.0f;
	//DP(("renderS %f", frame));
	if (mPlaying)
	{
		//if (!mModel->syncingPhysics)		animeTime += deltaTime * mmdSpeedMul * (reversePlay ? -1 : 1);
		if (reversePlay && animeTime < 0.f) animeTime = 0.f;
		//DP(("UP+"));
	}
#if MMD_MOUTH_FROM_LYRIC
	//mouth
	sidTimer -= Ctx->gd.deltaTime;
	if (sidTimer < 0.f)
	{
		curSid = nextSid;
		sidRatio = 1;
		sidTimerDur -= Ctx->gd.deltaTime;
		if (sidTimerDur < 0.f)
		{
			nextSid = 0;
			sidRatio = 0;
			sidTimer = sidTimerMax = SID_TIMER_S * 5;
			sidTimerDurMax = sidTimerDur = 0;
		}
	}
	else
	{
		sidRatio = 1.f - sidTimer / sidTimerMax;
	}
	//DP(("SID %f",sidRatio));
#endif
	if (hasBeat)
	{
		frame = beatVal * std::min(30, Vmd ? Vmd->GetMaxMotionTimeFrame() / 2 : 1);
	}
	else
		if (Vmd && Pm.mmd->owMmdTimeUs >= 0) {
			if (Pm.mmd->owMmdTimeUs == 0) {
				camWarped = false;
			}
			float addtime = (Ctx->gd.time - Pm.mmd->owSetTime);
			if (Pm.mmd->owDeltaUs >= 0)
				addtime = Pm.mmd->owDeltaUs / 1000000.f * (addtime) * 30.f;

			animeTime = Pm.mmd->owMmdTimeUs / 1000000.f + addtime;
			animeTime *= mmdSpeedMul;
			deltaTime = animeTime - lastOwTime;

			deltaTime = core::clamp(deltaTime, 00.f, 0.2f);
			if (Pm.mmd->pauseDemoing)
				animeTime = Pm.mmd->owMmdTimeUs / 1000000.f;
			//DP(("animeTime %f   d=%f",animeTime, deltaTime));
			lastOwTime = animeTime;
			frame = animeTime * 30.0f;
			if (animCycleStart >= 0)
				frame = fmodf(frame, Vmd->GetMaxKeyTimeFrameDeprecated());

			//DP(("pd %d  df %d  frame %f  time %f", Pm.mmd->pauseDemoing, Pm.mmd->pauseDemoFr, frame, m_animTime));
		}
		else
			frame = animeTime * 30.0f;
	curDeltaTime = deltaTime;
	{
		drawPosC.interpolate(drawPos, drawPosC, 0.1f);
		drawCtrPosC.interpolate((Drawing ?
			vector3df(drawPosC.X * -1,
				(drawCtrPos.Y > 3 ? drawCtrPos.Y - 3 : (drawCtrPos.Y < 1 ? drawCtrPos.Y - 1 : 0)) * 6,
				drawCtrPos.Z
			)
			: vector3df(0, 0, 0)), drawCtrPosC, 0.1f);
		//drawDyC.interpolate(drawPos - drawCtrPos, drawDyC, 0.075f);
	}

	if (lastFrame != frame || Pmx->syncingPhysics || Pmx->needPhysics || !mPlaying || selectedNode)
	{
		// Update Animation
		mModel->BeginAnimation();
		mModel->DoCallback(NodeCbStage::NS1);
#if MMD_MONTAGE
		//右手首.. animations


#if MMD_MTG_HANDSEAL
		const float maxFrame = 10.f;
		mModel->vmdAddAnimId[0] = 0;
		mModel->vmdAddAnimId[1] = 1;
		float ratio = aniRateR * std::min(maxFrame, std::max(0.2f, lastTimer) * maxFrame);
		mModel->vmdAddAnims[0].enable = 1;
		mModel->vmdAddAnims[0].weight = handUpRatR;

		mModel->vmdAddAnims[0].frame = 20 * adAnmId - ratio;

		mModel->vmdAddAnims[1].enable = 1;
		mModel->vmdAddAnims[1].weight = 1 - handUpRatR;

		mModel->vmdAddAnims[1].frame = 20 * adAnmIdLast - ratio;
#else


		mModel->vmdAddAnimId[0] = adAnmId;
		mModel->vmdAddAnimId[1] = std::max(0, adAnmIdLast);

#if MMD_ACTION_ANIM
		if (mModel->vmdAddAnims.size() > adAnmId && adAnmId >= 0) {
			mModel->vmdAction = 1;
			auto va = mModel->curVA;
			float at = gSceneTime - Pmx->vas[0].start;
			float dur = va->vi.actT;
			va->afterT = at > dur;
			va->afterE = at > va->vi.actE;
			if (va->faceCam && at / dur < 1.1f && at >= 0.f)
			{
				ndRoot->SetAnimationRotate(glm::slerp(va->faceCamRtSrc, va->faceCamRtTgt, std::min(1.f, at / dur)));
			}
		}
#elif MMD_PLAY_GUQIN
		mModel->vmdAddAnimCount = 2;
		//DP(("VMD  %d,%d ", adAnmId, adAnmIdLast));
		for (int i = 0; i < mModel->vmdAddAnims.size(); i++) {
			bool aOn = i == adAnmId;
			mModel->vmdAddAnims[i].enable = aOn || (i == adAnmIdLast);
			mModel->vmdAddAnims[i].weight = aOn ? handUpRatR : 1 - handUpRatR;
			float maxFrame = std::min(lastTimer * 60, (float)mModel->vmdAddAnims[i].maxFrame);
			mModel->vmdAddAnims[i].frame = aniRateR * std::min(maxFrame, std::max(0.2f, lastTimer) * maxFrame);
			//DP(("VMDs %d,%d,%f", i, mModel->vmdAddAnims[i].enable, mModel->vmdAddAnims[i].frame));
		}
#else
		mModel->vmdAddAnimCount = 2;
		if (adAniDur > 0.01f)
		{
			if (adAniTime > 0.f) {
				adAniTime -= deltaTime;
				handUpRatR = std::min(1.f, 1.f - adAniTime / adAniDur);
				if (adAniDur <= 0.f)
					adAniDur = 0.f;
			}
		}
		for (int i = 0; i < mModel->vmdAddAnims.size(); i++) {
			bool aOn = i == adAnmId;
			mModel->vmdAddAnims[i].enable = aOn || (i == adAnmIdLast);
			mModel->vmdAddAnims[i].weight = aOn ? handUpRatR : 1 - handUpRatR;
			float maxFrame = (float)mModel->vmdAddAnims[i].maxFrame;
			mModel->vmdAddAnims[i].frame = handUpRatR * maxFrame;
			//DP(("VMDs %d,%d,%f", i, mModel->vmdAddAnims[i].enable, mModel->vmdAddAnims[i].frame));
		}
#endif

#endif
#endif


		bool aprmo = mdApproachingMotion && Pm.mmd->cs->animEditOn == 0;
		if (Pmx->isCharacter) {
			leapUpdate();

			if (ndHead && ndHead->rb0 && ndHead->rb0->GetActivation()) {
				if (LOOKAT_SB_TORQUE && canLookAtCam && !isLookAtCam)
				{
					//ndHead->rb0->setAngVelToPos(mmdLookAt, LOOKAT_SB_TORQUE);
				}
				else if (LOOKAT_CAM_TORQUE_ALL != 0 && isLookAtCam && canLookAtCam && (!isAiCharacter() || LOOKAT_CAM_TORQUE == 0))
				{
					ndHead->rb0->setAngVelToPos(mmdLookAt, LOOKAT_CAM_TORQUE_ALL);
				}
			}
#if 0
			if (ndUpper2) {
				float fy = std::max(0.f, (10.f - ndYao->GetGlobalTransform()[3][1]) * 8.5f);
				fy = std::min(3000.f, fy * fy);
				//DP(("fy %f",fy));
				phyForceParam pm; pm.node = ndYao; pm.fY = 100 + fy; pm.fw = "jetCenterDroplets";
				pm.fwSubNode = 0;
				phyForceOnNode(pm);
			}
#endif
		}

		mModel->UpdateAllAnimation(
			lastFrame != frame || aprmo ? Vmd.get() : nullptr,
			aprmo ? headDis : frame,
			deltaTime);
#if DEBUG_UpdateGlobalTransform
		static int gtucc = 0;
		int delta = saba::gd.globalTransformUpdateCount - gtucc;
		DP(("gtucc %d delta %d ", saba::gd.globalTransformUpdateCount, delta));
		gtucc = saba::gd.globalTransformUpdateCount;

#endif
#if 0//USE_LEAP
		if (leap && leapOn)
			for (int i = 0; i < 2; i++) {
				auto& lhd = leap->Lhd[i];
				{
					auto ndh = i == LEAP_SWAP_LR ? ndHandL : ndHandR;
					if (lhd.grab > 0.9) {
						vector3df pos(i == LEAP_SWAP_LR ? 1 : -1, -.69, 0);
						auto m = ndh->GetGlobalTransform();
						matrix4 gm = m;
						gm.transformVect(pos);
						mmdToIrrPos(pos);
						glm::quat r = m;
						//float deg = 30;			glm::quat q(glm::cos(glm::radians(deg)), 0, glm::sin(glm::radians(deg)), 0);
						glm::vec3 vel = r * glm::vec3(i == LEAP_SWAP_LR ? -1.f : 1.f, -1, 0);
						vel *= (1 - lhd.grab) * 10;
						vel += ndHandR->exd.aniSpeed * 0.001f;
						vel = glm::mat4(mmdBaseMat) * glm::vec4(vel, 1.f);
						Eqv->LaunchFw3D(V3dToFloat3(pos), Eqv->getFwIdxByFwIdStr(1, "handFw"), vel);

					}
				}
			}

#endif
#if MMD_SAVE_VMD
		if (vmdWriteFile && vmdWriteFile->recordingAllNode)
		{
			auto np = ndYao->GetParent(), nd = ndYao;
			while (np && np->GetParent()) {

				auto mp = nd->GetGlobalTransform() * glm::inverse(nd->mLocalInit);
				np->SetGlobalTransform(mp);
				nd = np;
				//auto t = mp * np->GetInverseInitTransform();
				np = np->GetParent();
			}
		}
#endif
		mModel->DoCallback(NodeCbStage::NS200);

		mModel->EndAnimation();

		//if (ndPinHandL) { DP(("ndPinHandL2 %f ", ndPinHandL->getGlobalPos().x)); }
		//phyUpdate(deltaTime);

		if (Pm.mmd->ikVisable && mdViewNode) {
			if (!ikVRoot) 	createIkLabels(0);
			if (ikVRoot && ikVRoot->isVisible())
			{
				int id = 0;
				for (auto& it : snMarks)
				{
					core::matrix4 mm = it.second.nd->GetGlobalTransform();
					ikVRoot->setItemPos(it.second.lbId, it.second.pos = (mm).getTranslation(),
						{ it.second.nd == selectedNode ? 1 : 0,0 });
					ikVRoot->setItemVisual(it.second.lbId, it.second.nd == Pm.mmd->tempIKRoot ? 0xFFFFFF00 : it.second.color);
					id++;
				}
			}
		}
		//ikVRoot->setSelectIdx();
		aniUpdated = true;

		lastFrame = frame;
		//exportAnimationFrame(); //see svn 2021.7.16
		if (mPlaying)if (!mModel->syncingPhysics)
			animeTime += deltaTime * mmdSpeedMul * (reversePlay ? -1 : 1);
		if (vee) {
			vee->updateFrame(frame + int(MMD_ACTION_TIME * 30 + 0.5) + 0.00001f);

		}

	}
	else
	{
		DP(("skip frame"));
	}

#if MMD_VIRTUAL_SINGER
	if (itemIdx >= 0 && isAiCharacter())
		mmdSynthVSingNote();
#endif

	if (mPlaying && Vmd) {
		//DP(("vmd at=%f, fr=%f ,m=%d",m_animTime, frame, Vmd->GetMaxKeyTime()));
#if IS_VMD_CONVERTER
		if (vmdWriteFile && frame > Vmd->GetMaxKeyTimeFrame() - 0.01f)
		{
			vmdWriteFile->recordingAllNode = false;
			auto fp = writeVmdFile.pathWithOutExt() + L"_IKLeg2D.vmd";
			vmdWriteFile->SaveToFile(fp.c_str());
			mPlaying = false;
		}

#else
#if MMD_SAVE_VMD_MOTION
		if (!vmdWriteFile->recordingAllNode)
#endif
			if (reversePlay)
			{
				if (animCycleStart >= 0) if (frame <= 0) {
					animeTime = Vmd->GetMaxMotionTimeFrame() / 30.f;;
				}
			}
			else if (frame > Vmd->GetMaxMotionTimeFrame()) {
				if (animCycleStart >= 0)
					animeTime = animCycleStart;
				else
					if (!leapOn) enableLeap(true);
			}

#endif


	}

	if (m_vmdCameraAnim)
	{
		float fradd = 0.f;
#ifdef __ANDROID__
		if (!Pm.mmd->autoPause) fradd = 30.f;
#endif
		float camframe = (mCamPlaying ? frame + fradd : 0.f);
		//if (camframe > 50) camframe = 60;
		updateCamera(camframe);


	}
	else {
		camWarped = false;

	}

	//auto cam = SceneManager->getActiveCamera();

	//DP(("CAMDIRRATE %f,%f %f %f",pos.X,pos.Z, deg,mModel->camDirRate));
#if MMD_SAVE_VMD
	//recordCamFrame();
#endif



	if (fwPrelaunch && Pmx->isCharacter)	updateMmdMode1(deltaTime);

#if USE_SVG && SVG_DRAW_NORMCAM
	//if (!snCamCube) snCamCube = SceneManager->addCubeSceneNode(10, Ctx->gd.CamNormal);
	if (Drawing && svgPm.mmdPos) {
		Ctx->gd.CamNormal->setPosition(eyePos);
		vector3df lookPos = lookTgtMmdPos; mmdToIrrPos(lookPos);
		lookPosC.interpolate(lookPos, lookPosC, core::clamp(lookPosC.getDistanceFrom(lookPos) / 2000.f, 0.01f, 0.5f));
		//Ctx->gd.CamNormal->setUpVector({ 0,1,0 });
		Ctx->gd.CamNormal->bindTargetAndRotation(true);
		Ctx->gd.CamNormal->setTarget(lookPosC);

		if (snCamCube) snCamCube->updateTransform();
	}
#endif


	if (!paraAnimating)
		updateVtxMat();

	if (Pmx->isCharacter) for (auto en : Pmx->extNodes) if (en->rb0)
	{
		en->rb0->updateStateOnNode();
		sbFw2D("sw", en->getGlobalPos(), vec3(0), 0xFFFFFFFF);
		sbFw2D("sw", en->rb0->getPosition() + vec3(0, 0.1, 0), vec3(0), 0xFF0000FF);
	}
}

void irr::scene::IrrSaba::updateVtxMat()
{
	if (aniUpdated)
	{
		aniUpdated = false;
		if (useGPU && mcs)
		{
			mModel->Update(true);
			mcs->runCS(mModel.get());
			mModel->vtxPosUpdated = true;
		}
		else if (hasModel)
			vtxProcessCPU();
	}
}

void irr::scene::IrrSaba::enableIK(bool en)
{
	for (auto ik : ikNodes)
		ik->EnableIK(en);
}

void irr::scene::IrrSaba::updateCamera(float camframe, bool onlyCalc)
{
	if (!m_vmdCameraAnim) return;
	m_vmdCameraAnim->Evaluate(camframe);

	const MMDCamera& mmdcam = m_vmdCameraAnim->GetCamera();
	updateCameraOnMmdCamera(mmdcam, onlyCalc);
}

void irr::scene::IrrSaba::updateCameraOnMmdCamera(const MMDCamera& mmdcam, bool onlyCalc)
{
	if (!camTgt) {
		camTgt = Ctx->getSceneManager()->addEmptySceneNode(this);
		camTgt1 = Ctx->getSceneManager()->addEmptySceneNode(camTgt);
		camTgt2 = Ctx->getSceneManager()->addEmptySceneNode(camTgt1);
	}
	auto finalTgt = (camTgt);
	if (!irrCam) {
		irrCam = Ctx->getSceneManager()->addCameraSceneNode(0, { 0,0,0 }, { 0,0,0 }, -1, false);
		irrCam->setName(ualib::strFmt("s%d", getItemIdx()).c_str());
		//Ctx->getSceneManager()->setActiveCamera(irrCam);
#if DBG_CUBE
		static SColor DbgColor[] = { 0x7FFF0000,0x7F00FF00,0x7F0000FF };
		auto box = Ctx->getSceneManager()->addCubeSceneNode(DMY_WIDTH, finalTgt, -1, { 0,0,0 })->setAlphaAndDiffuse(DbgColor[Pm.idx]);
		Ctx->getSceneManager()->addCubeSceneNode(DMY_WIDTH / 2, box, -1, { 0,DMY_WIDTH,0 })->setAlphaAndDiffuse(DbgColor[Pm.idx]);
		auto camBox = Ctx->getSceneManager()->addCubeSceneNode(DMY_WIDTH, irrCam, -1, { 0,0,0 })->setAlphaAndDiffuse(DbgColor[Pm.idx]);
		Ctx->getSceneManager()->addCubeSceneNode(DMY_WIDTH, camBox, -1, { 0,DMY_WIDTH,0 })->setAlphaAndDiffuse(DbgColor[Pm.idx]);
#endif
		irrCam->setNearValue(1.f);
		irrCam->setFarValue(60000);
		camTgtDmy = Ctx->getSceneManager()->addEmptySceneNode(irrCam, -1); camTgtDmy->setPosition({ 0,0,1000 });
		if (drawCam) {
			if (!snCam) {
				auto ms = SceneManager->getMesh(IS_WIN ? "res/camera.obj" : "res/cameraFrame.obj");
				snCam = SceneManager->addMeshSceneNode(ms, irrCam);
				snCam->setMaterialDiffuse(0xFF800000);
				snCam->getMaterial(0).SpecularColor = 0;
				//snCam->setMaterialFlag(EMF_LIGHTING, false);
			}
		}
	}
	if (!onlyCalc) {
		irrCam->setVisible(true);

		if (drawCam) {
			SColor sc = 0xFFFF8000;
			sc.setBlue(Pm.mmd->pauseIdx * 8); sc.setGreen(Pm.mmd->pauseIdx * 8);
			snCam->setMaterialDiffuse(sc);
		}
		else if (!drawCam && Pm.activeNodeCam) {
			 Ctx->setViewCamera(irrCam,0,1);

		}
		if (Pm.mmd->autoPause) {
			static uint32_t cc = 0;
			//if (Pm.mmd->pauseDemoing) Ctx->getSceneManager()->setActiveCamera((++cc % 30>=15) ? Ctx->gd.CamNormal : irrCam);
			//else Ctx->getSceneManager()->setActiveCamera(Ctx->gd.CamNormal);
		}
	}
	auto cam = irrCam;
	vector3df tgt(mmdcam.m_interest);
	vector3df rot(-mmdcam.m_rotate.x, -mmdcam.m_rotate.y, -mmdcam.m_rotate.z);
#ifdef _DEBUG
	if (m_vmdCameraAnim && !m_vmdCameraAnim->isPlaying()) 	if (Pm.idx == 1) {
		DP(("CAM END %f %f %f,rot %f %f %f >>>>>>>>>>>>>>>>>>>>>>>>>>>>", tgt.X, tgt.Y, tgt.Z, rot.X, rot.Y, rot.Z));
	}
#endif

	rot = (rot * core::RADTODEG);

	cam->bindTargetAndRotation(false);

	camTgt->setPosition(tgt);
	rot += Pm.mmd->dbgAddRt;

	camTgt->setRotation({ rot.X,rot.Y,0 });

	//camTgtSub->setRotation({ rot.X,0,0 });
	camTgt->updateAbsolutePosition();

	auto getMaxDistance = [=](float minY) {
		float distance = -mmdcam.m_distance;
		glm::vec3 posTgt = camTgt->getPosition();

		glm::quat qr = glm::quat(glm::vec3(camTgt->getRotation() * core::DEGTORAD));
		glm::vec3 rotatedCamDir = qr * glm::vec3(0, 0, 1);
		glm::vec3 normalizedCamDir = glm::normalize(glm::vec3(rotatedCamDir));
		if (abs(normalizedCamDir.y) < 0.0001f) return distance;
		// Calculate how far we can move in this direction before hitting minY
		float deltaY = posTgt.y - minY;  // vertical distance to minimum Y
		// If camera is moving upward (negative Z creates upward rotation)
		if (normalizedCamDir.y < 0)
			return distance;  // no constraint needed, camera moving away from ground
		// Calculate maximum allowed distance based on Y component
		float maxDistance = deltaY / normalizedCamDir.y;
		return glm::min(distance, maxDistance);
		};

	cam->setPosition({ 0,0,IRR_DRAW_MIRROR ? -getMaxDistance(0.1) : mmdcam.m_distance });
	cam->setRotation({ 0,0,rot.Z });
	cam->setParent(finalTgt);

	//camTgtDmy->updateAbsolutePosition();

	//if (Pm.idx==1)			SceneManager->setActiveCamera(irrCam);
	//tgt = finalTgt->getAbsolutePosition();
	//rot = camTgt->getRotation() * core::DEGTORAD;
	//core::quaternion qt = rot;
	//DP(("tgt %d: %f,%f,%f  %f %f %f",Pm.idx, tgt.X, tgt.Y, tgt.Z, rot.X, rot.Y, rot.Z));
	cam->updateTransform();
	camTgtDmy->updateTransform();
	cam->upNode->updateAbsolutePosition();
	cam->setUpVector(cam->getUpVectorByNode());

	cam->setTarget((mmdcam.m_distance > -1 ? camTgtDmy : finalTgt)->getAbsolutePosition());
	//cam->setTarget(camTgt->getAbsolutePosition());
	//float fov=atan(tan(mmdcam.m_fov  / 2) * Ctx->gd.scrHeight / Ctx->gd.scrWidth) * 2;
	cam->setFOV(mmdcam.m_fov * MMD_FOV_MUL);
	float dis0 = cam->getAbsolutePosition().getDistanceFrom({ 0,0,0 });
	DP(("DIS0 %f", dis0));
	//cam->setFarValue(std::max(320.f , dis0 * 5));
	//cam->setNearValue(std::max(MMD_SCALE_OLD, dis0 - 60 ));

	if (onlyCalc)
		cam->updateMatrices();
	//DP(("CAM tgt %.3f trt %.3f,%.3f POS %f",tgt.Y, rot.X, rot.Y, cam->getAbsolutePosition().Y));

#if 0
	Ctx->gd.CamNormal->morphStage = 0;
	Ctx->gd.CamNormal->bindTargetAndRotation(false);
	Ctx->gd.CamNormal->setPosition(cam->getAbsolutePosition());
	Ctx->gd.CamNormal->setTarget(cam->getTarget());
	Ctx->gd.CamNormal->setUpVector(cam->getUpVectorByNode());
	Ctx->gd.CamNormal->updateAbsolutePosition();
#endif
	//Ctx->gd.CamNormal->updateMatrices();


}
int irr::scene::IrrSaba::isCamJump(float ts)
{
	int ret = 0;
	if (ts < 0.0001f) {
		lastTsFrame = -1;
	}
	float frame = ts * 30.f + 0.0001f - 1.f + Pm.mmd->owTimeMul / 100 / Pm.mmd->owTimeDiv;
	if (m_vmdCameraAnim && (frame - lastTsFrame > 2.f) && m_vmdCameraAnim->Evaluate(frame)) {
		auto mc = m_vmdCameraAnim->GetCamera();
		Pm.mmd->pauseDemoFr = 0;
		ret = 3;

		if (!Pm.mmd->pauseDemoing) {
			if (m_vmdCameraAnim->m_cameraController->camRttDis < 0.001f)
				ret = 9;
			lastTsFrame = frame;
		}
	}
	else
	{
		Pm.mmd->pauseDemoFr++;
		if (ts < lastTs && Pm.mmd->pauseDemoing)
		{
			//DP(("DTT %f - %f  =  %f", m_animTime , lastOwTime,deltaTime));
			Pm.mmd->pauseDemoFr = 0;
		}
	}
	lastTs = ts;
	return ret;
}

int irr::scene::IrrSaba::updateCameraAtOffsetTime(float timeOfs, bool fromStart)
{
	updateCamera(((fromStart ? 0 : animeTime) + timeOfs) * 30.f, abs(timeOfs) > 0.000001f);
	return 0;
}


void irr::scene::IrrSaba::vtxProcessCPU()
{

	S3DVertex* vtx = (S3DVertex*)sbd->drawVertices->lock(MMD_IRRMTR);
	mModel->gpuVtxPtr = vtx;
	mModel->vtxPosUpdated = false;
	mModel->gpuVtxStride = sizeof(S3DVertex);
	mModel->gpuNormalOfs = offsetof(S3DVertex, Normal);
	mModel->gpuUVOfs = offsetof(S3DVertex, TCoords);

	//CPU_COUNT_BEGIN("mmda2");
	mModel->Update(false);
	//CPU_COUNT_END("mmda2");//2019 PC 3ms    2023 PC 0.12ms

	if (!mModel->vtxPosUpdated)
	{
		size_t vtxCount = mModel->GetVertexCount();

		const glm::vec3* positions = mModel->GetUpdatePositions();
		const glm::vec3* normals = mModel->GetUpdateNormals();
		const glm::vec2* uvs = mModel->GetUpdateUVs();

		static int cc = 0; cc = (cc + 1) % 10;
		if (lastUpdateVtx != vtxCount)	for (size_t i = 0; i < vtxCount; i++)
		{
			lastUpdateVtx == vtxCount;
			memcpy(&vtx[i].Pos, positions + i, sizeof(vector3df));
			memcpy(&vtx[i].Normal, normals + i, sizeof(vector3df));
			//vtx[i].Color = 0xFFFFFFFF;
			vtx[i].TCoords.X = uvs[i].x;
			vtx[i].TCoords.Y = uvs[i].y;
#if 1
			if (i % 10 == cc) {
				auto cPos = vtx[i].Pos;		mmdBaseMat.transformVect(cPos);
				Eqv->LaunchFw3D(V3dToFloat3(cPos), Eqv->getFwIdx(1, 5), vtx[i].Normal, { 1,1,1,1 });
			}
#endif
		}
		else 	for (size_t i = 0; i < vtxCount; i++)
		{
			memcpy(&vtx[i].Pos, positions + i, sizeof(vector3df));
			memcpy(&vtx[i].Normal, normals + i, sizeof(vector3df));

		}
	}

	else
		fwLch->vtxSetMat(vtx);

	if (lauchAllVtxFw > 0)
	{
		fwLch->vtxFwFrameOnceAll(vtx, lauchAllVtxFw == 1);
		lauchAllVtxFw = 0;
	}
	sbd->drawVertices->unlock();

}

void irr::scene::IrrSaba::preRender()
{
	updateVtxMat();
	if (paraAnimating) { assert(0); }//if para animating , should move to before ppt fw update
}

void irr::scene::IrrSaba::render()
{
	//CPU_COUNT_B(MMD_VTX);
	//DP(("render %d",phyObjId));

	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	auto passType = driver->getPassType();
	if ((passType & passTypeFlags) == 0)
		return;

	if (MMDPhysics::phyDbgView && passType == IrrPassType_Normal) renderPhysxDbgVisualLines(mmdPhysics);
	if (!hasModel) return;
	auto eit = ieSize == 2 ? EIT_16BIT : EIT_32BIT;
	size_t subMeshCount = mModel->GetSubMeshCount();

	//TODO use view size
	//smEdgeCP.fvecs[0].x = Ctx->gd.scrWidth;	smEdgeCP.fvecs[0].y = Ctx->gd.scrHeight;//smEdgeCP.fvecs[0].z = Ctx->gd.scrHeight;
	auto vertexs = sbd->drawVertices;
	auto indexs = sbd->drawIndexs;
	if (!isOIT && !ALLOW_GPU && allShadowCastMat && (passType == IrrPassType_ShadowMap || passType == IrrPassType_GBuffer /*|| passType == IrrPassType_Models*/))
	{
		//if (!useGPU)
		{
			static SMaterial sm;
			driver->setMaterial(sm);
			Driver->setTransform(ETS_WORLD, mmdBaseMat);
			driver->drawHardwareBufferParallel(currentParaId, sbd->drawVertices, sbd->drawIndexs, EVT_STANDARD, EPT_TRIANGLES, eit, 1, mModel->GetIndexCount(), 0);
			//driver->drawHardwareBufferParallel(currentParaId, drawVertices , drawIndexs , EVT_STANDARD, EPT_TRIANGLES, eit, 1, mModel->GetSubMeshes()[0].m_vertexCount, mModel->GetSubMeshes()[0].m_beginIndex);
		}
	}
	else for (size_t i = 0; i < subMeshCount; i++) 	// draw mtrs
	{

		const auto& subMesh = mModel->GetSubMeshes()[i];
		auto mr = mtrs[subMesh.m_materialID];
		auto& mdm = Pmx->Materials[i];

#if MMD_IRRMTR
		if (mdm.m_spTextureMode == MMDMaterial::SphereTextureMode::Add && mdm.m_edgeFlag == 0) continue;
#endif
		bool noDraw = false;
		if (mr.MaterialType == EMT_MMD) {
			video::MMDCbCp& md = *(video::MMDCbCp*)mr.pCbCp;
			auto& cb = md.cb;
			cb.dp = Pm.mmd->shdpm;
			float mta = mdm.m_alpha * modelAlpha * mdm.alphaMul;
			if (mdm.subMeshAlphaMod) {
				if (mdm.subMeshAlphaDec > 0) {
					mdm.subMeshAlphaDec -= stepTime * saba::MMDPhysics::phyTimeMul;
					mta *= core::clamp(1 - blendInOutAtT(core::clamp(1 - mdm.subMeshAlphaDec, 0.f, 1.f), 0.2f), 0.f, 1.f);
				}
				else mta *= mdm.subMeshAlphaMul;
			}
			//if (mdm.alphaMul < 1 && passType == IrrPassType_Normal)
			//	{DP(("1"));}
			cb.Diffuse = float4(mdm.m_diffuse.x, mdm.m_diffuse.y, mdm.m_diffuse.z, mta) * mdm.diffuseMul;
			if (mdm.rainbow) {
				SColorHSL hsl(SColorf(cb.Diffuse.x, cb.Diffuse.y, cb.Diffuse.z, 1));
				mdm.rainbowSpeed = 1;
				hsl.Hue = mdm.rainbowSbOfs*itemIdx+ mdm.rainbowMtrOfs * i + 360 * fmod(objId / 12.f + hsl.Hue / 360 + gGameTime * mdm.rainbowSpeed / core::PI * 0.5f + itemIdx / std::max(eqvBandCount, 6), 1.f);
				SColorf scf = hsl; 
				cb.rainbow = mdm.rainbow;// mdm.rainbow;//1=ofs only, 2=rbw on time; 
				if (mdm.rainbow ==0) cb.TexMulFactor = float4(1,1,1, mdm.m_alpha);
				else 
					cb.TexMulFactor = float4( glm::fract(hsl.Hue / 360.f), 1, 1, mta);// 
			 
				curDiffuse = hsl.toSColor();
			}

			cb.oit = md.useOIT = isOIT && passType == IrrPassType_Normal;
			if (md.useOIT && cb.oit)
				mr.MaterialType = EMT_MMD_OIT;
#if 1
			if (passType == IrrPassType_ShadowMap && (!mdm.m_shadowCaster || mta <= 0.995f))
				continue;
			if (passType == IrrPassType_PickPoint && mta < 0.2f)
				continue;
#endif
			//else		cb.dp.colorMode = 1;
			md.useCsTransVtx = useGPU ? 1 : 0;

			md.twoSide = MMD_FORCE_2SIDE || passType == IrrPassType_Mirror ? 1 : mdm.m_bothFace;
			cb.outNormalMap = Pm.mmd->outNormalMap;
			if (cb.Diffuse.a < 0.01f)
				noDraw = true;
			float4x4 xW;// = *(float4x4*)Driver->getTransform(video::ETS_WORLD).pointer();
			IrrMatrix_to_VkMatrix(mmdBaseMat, xW);
			cb.g_mWorld = DRV_TransposeMatrix(xW);
			if (Driver->getDynamicLightCount()) {
				SLight dl = Driver->getDynamicLight(0);
				cb.LightColor.r = dl.DiffuseColor.r;
				cb.LightColor.g = dl.DiffuseColor.g;
				cb.LightColor.b = dl.DiffuseColor.b;
				cb.LightDir.x = -dl.Position.X;
				cb.LightDir.y = -dl.Position.Y;
				cb.LightDir.z = -dl.Position.Z;
			}


			cb.TextureModes.w = 0;
			if (mr.RcvShadow && (mdm.m_shadowReceiver || isHumanModel()) && Driver->getShadowOn() && (passType == IrrPassType_Normal || passType == IrrPassType_Mirror))
			{
				cb.TextureModes.w = 1;
				cb.dp.shadowBlurCount = (passType == IrrPassType_Normal ? 3 : 1);
			}
			cb.g_mLightVP = Driver->dsd.mlightVPt;
			cb.passType = passType;
			if (passType != IrrPassType_Models && Pm.mmd->shaderNoColor) cb.Flag |= MMDSD_NO_COLOR;	else cb.Flag &= ~MMDSD_NO_COLOR;
			cb.Flag &= ~MMDSD_IS_CAMSB;
			cb.Flag &= ~MMDSD_CTR_TRANSPARENT;
			if (Pm.mmd->renderingByCamSb) {
				if (Pm.mmd->camSb == this)cb.Flag |= MMDSD_IS_CAMSB;
				if (mdm.centerTransparent) {
					cb.Flag |= MMDSD_CTR_TRANSPARENT;
					auto rtsize = driver->getCurrentRenderTargetSize();
					cb.res = vec2(rtsize.Width, rtsize.Height);
				}
			}

		}
#if IRR_MTR_SDTOY
		else if (mat.MaterialType == EMT_SHADER_TOY) {
			ShaderToyCb& scb = *(ShaderToyCb*)mat.pCbCp;
			scb.iResolution = float3(Ctx->gd.scrWidth, Ctx->gd.scrHeight, 1);
			scb.iTime = Ctx->gd.time; scb.iTimeDelta = Ctx->gd.deltaTime;
			auto cam = SceneManager->getActiveCamera();
			auto mv = cam->getViewMatrix(), mp = cam->getProjectionMatrix();
			auto mvp = mp * mv;	core::vector3df scrCtr, scrPosCtr = centerPos;
			mvp.transformVect(scrCtr);	mvp.transformVect(scrPosCtr);
			scrPosCtr /= scrCtr.Z;	scb.iMouse = float4(0, 0, Ctx->gd.pointerClick.X, Ctx->gd.pointerClick.Y);
			scb.iFrame = 0;	scb.iDate = {};	scb.fv.x = Eqv->CurWavePeak; //AvrWavePeak;
			scb.is_debugdraw = scb.is_pause = false;	scb.main_image_srgb = false;
		}
#endif

#if  0//IS_WIN
		if (!useGPU && currentParaId < 0 && passType == IrrPassType_Normal)
			if (mdm.m_edgeSize > 0.1f) {
				smEdge.DiffuseColor = 0x80000000;
				driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);
				smEdgeCP.fv = mdm.m_edgeSize * 1;
				driver->setMaterial(smEdge);
				driver->drawHardwareBufferParallel(currentParaId, vertexs, indexs, EVT_STANDARD, EPT_TRIANGLES, eit, 1, subMesh.m_vertexCount, subMesh.m_beginIndex);
			}
#endif

		if (passType == IrrPassType_PickPoint)
		{
			auto M2 = mr;
			M2.MaterialType = EMT_MMD_PICK;
			video::MMDCbCp& md = *(video::MMDCbCp*)mr.pCbCp;
			auto& cb = md.cb;
			float4x4 xW;
			IrrMatrix_to_VkMatrix(mmdBaseMat, xW);
			cb.g_mWorld = DRV_TransposeMatrix(xW);
			md.cb.pickId = MatBase.PickColor;
			driver->setMaterialPara(currentParaId, M2);
		}
		else if (passType == IrrPassType_GlowBase)
		{
			int mi = subMesh.m_materialID;
			//hlMtrs[mi] = mr;
			hlCbcps[mi] = *(video::MMDCbCp*)mr.pCbCp;
			hlMtrs[mi].pCbCp = &hlCbcps[mi];
			auto& cb = hlCbcps[mi].cb;
			if (!mdm.Bloom) {
				cb.Ambient = float4(0, 0, 0, 0);
				cb.TextureModes = int4(2, 0, 0, 0);
				cb.Diffuse = float4(0, 0, 0, 1);
			}
			else {
				SColorf cf = SColor(mdm.Bloom);
				cb.TextureModes = int4(0, 0, 0, 0);
				//cb.Ambient = float4(0, 0, 0, 0); //cb.Ambient = float4(1, 1, 1, 1);
				cb.Diffuse = float4(1, 1, 1, 1);//
				// float4(cf.r, cf.g, cf.b, cf.a);
			}
			driver->setMaterialPara(currentParaId, hlMtrs[mi]);
		}
		else {

			Driver->setTransform(ETS_WORLD, mmdBaseMat);
			driver->setMaterialPara(currentParaId, mr);
		}
		//if (passType == IrrPassType_Models && i > 3)	continue;

		driver->drawHardwareBufferParallel(currentParaId, vertexs, indexs, EVT_STANDARD, EPT_TRIANGLES, eit, 1, noDraw ? 0 : subMesh.m_vertexCount, subMesh.m_beginIndex);
		vertexs = nullptr; indexs = nullptr;
	}
	UploadCbData(passType == IrrPassType_GlowBase ? hlCbcps.data() : cbcps.data(), cbcps.size());
	//CPU_COUNT_E(MMD_VTX);

}

const core::aabbox3d<f32>& irr::scene::IrrSaba::getBoundingBox() const
{
	return Box;
}
void irr::scene::IrrSaba::UploadCbData(irr::video::MMDCbCp* pb, int count)
{

	VK_CHECK_RESULT(cbDynVkBuf.map());
	for (size_t i = 0; i < count; i++)
	{
		cbPerDraw::MMD* pd = (cbPerDraw::MMD*)((uintptr_t)cbDynVkBuf.mapped + (dynBufAlignment * i));
		*pd = pb[i].cb;
	}

	// memcpy(cbDynVkBuf.mapped, cbByteBuf, std::min((size_t)mFxCbDraw.mSize, count * dynamicAlignment));
	//DP(("FFDRMap = %p", mFxCbDraw.mapped));
	cbDynVkBuf.unmap();
}

void irr::scene::IrrSaba::onJoyEvent(const irr::SEvent::SJoystickEvent& je)
{
	if (Ctx->scenePaused) return;
#if MMD_JOYSTICK_GAMECAST
	if (MMD_JOYSTICK_GAMECAST && hdlJgc && !hdlJgc->getPlayback())	hdlJgc->onJoyEvent(je);
#endif
#if MMD_JOYSTICK_BODY

	if (MMD_JOYSTICK_BODY && hdlJbd)	hdlJbd->onJoyEvent(je);
#endif

}

void irr::scene::IrrSaba::resetAnimation(float startTime, bool reverse, int phySyncFrame)
{
	if (Vmd && Vmd->GetMaxMotionTimeFrame() < 1.1f && !MMD_SAVE_VMD)
		return;

	animeTime = startTime;
	lastOwTime = 0.f;
	reversePlay = reverse;
	rootLocked = false;
	//if (phySyncFrame > 0) {
	//
	//	for (auto it = Pom->phyObjs.begin(); it != Pom->phyObjs.end();) {
	//		auto& b = *it;
	//		if (!b.pm.psn)
	//		{
	//			assert(0); //todo do not remove
	//			b.pm.sn->remove();
	//			Pmx->GetMMDPhysics()->RemoveRigidBody(b.rb); //delete b.rb;
	//			it = Pom->phyObjs.erase(it);objRec[
	//		}
	//		else it++;
	//	}
	//	Pmx->ResetPhysics();
	//	Pmx->setDynRbActive(false);
	//}
	if (Vmd && reversePlay)
		animeTime = Vmd->GetMaxKeyTimeFrameDeprecated() / 30.f;
#if MMD_SAVE_VMD
	if (!Pm.needSaveCamVmd)	 if (MMD_SAVE_VMD_MORPH | MMD_SAVE_VMD_MOTION) {
		mModel->saveVMD = MMD_SAVE_VMD_MOTION;
		vmdWriteFile = vmd::VmdMotion::LoadFromFile(writeVmdFile.size() > 0 ? ualib::WcharToAnsi(writeVmdFile.c_str()).c_str() : "d:/mmd/vmd/std.vmd");
		vmdWriteFile->skipIKs = MMD_SAVE_VMD_MOTION;
		vmdWriteFile->recordingAllNode = MMD_SAVE_VMD_MOTION_ALLNODE;

		std::string s1;	ecv.Utf16ToCp932(L"左足", 2, &s1);
		std::string s2;	ecv.Utf16ToCp932(L"右足", 2, &s2);
		std::string s3;	ecv.Utf16ToCp932(L"左ひざ", 3, &s3);
		std::string s4;	ecv.Utf16ToCp932(L"右ひざ", 3, &s4);
		std::string s5;	ecv.Utf16ToCp932(L"左足首", 3, &s5);
		std::string s6;	ecv.Utf16ToCp932(L"右足首", 3, &s6);
		std::string s7;	ecv.Utf16ToCp932(L"左つま先", 4, &s7);
		std::string s8;	ecv.Utf16ToCp932(L"右つま先", 4, &s8);
		std::vector<vmd::VmdBoneFrame> vb;
		for (auto& bf : vmdWriteFile->bone_frames)
		{
			if (bf.name != s1 && bf.name != s2 && bf.name != s3 && bf.name != s4 && bf.name != s5 && bf.name != s6 && bf.name != s7 && bf.name != s8)
				vb.push_back(bf);
		}
		vmdWriteFile->bone_frames = vb;
	}
#endif
	//vmdWriteFile.face_frames.clear();
	//vmdWriteFile.bone_frames.clear();

	dsLast.drawing = false;
	if (phySyncFrame > 0 //&& !Rb0()->GetActivation()
		&& Vmd) Vmd->SyncPhysics(animeTime, phySyncFrame);

#if MMD_SAVE_PUNCH_MIDI
	midiArr.clear();
#endif
}

void irr::scene::IrrSaba::saveCam(irr::scene::ICameraSceneNode* cam)
{
	if (!camSave)
		camSave = SceneManager->addCameraSceneNode(0, { 0,0,0 }, { 0,0,0 }, -1, false);
	if (cam)
		cam->copyDataTo(camSave);
	if (svgMan) svgMan->recordData();

}

Json::Value irr::scene::IrrSaba::poseToJson()
{
	return  mmdPoseToJson(Pmx);
}

void irr::scene::IrrSaba::showGuqin(bool show)
{
	DP(("ShowGuqin"));
	if (!snGuqin && show) {
		GuqinParam gpm{ Eqv,Ctx };
		snGuqin = new SnGuQin(this, SceneManager, -1, gpm);
	}
	if (snGuqin)
		snGuqin->setVisible(show);
}


void irr::scene::IrrSaba::addCameraFrameToFitChildBase(IrrSaba* sb)
{
	//matrix right *, quaternion left *
	mModel->UpdateAllAnimation(Vmd.get(), 99999, 0);
	updateTransform();

	core::matrix4& ms = *(core::matrix4*)&sb->parentAttechNode->GetGlobalTransform();
	core::matrix4 mbr; mbr.setRotationDegrees(sb->getRotation());
	core::matrix4 m = ms * sb->getRelativeTransformation();// *sb->rootMat;
	core::matrix4 mt, mr;

	auto pos = sb->irrCam->getPosition();
	auto tpos = sb->camTgt->getPosition();

	mt = m * sb->camTgt->getRelativeTransformation();

	mt.transformVect(pos);

	tpos = mt.getTranslation();
	vector3df rtt;

	glm::mat4 transformation = IrrMatToGlm(mt * sb->irrCam->getRelativeTransformation()); // your transformation matrix.
#if 0
	glm::vec3 scale;
	glm::quat rotation;
	glm::vec3 translation;
	glm::vec3 skew;
	glm::vec4 perspective;
	glm::decompose(transformation, scale, rotation, translation, skew, perspective);
#endif
	vector3df rttYXZ;
	glm::extractEulerAngleYXZ(transformation, rttYXZ.Y, rttYXZ.X, rttYXZ.Z);

	//glm::extractEulerAngleZYX(transformation, rtt.Z, rtt.Y, rtt.X);
	rtt = rttYXZ;
#if DBG_CUBE
	static auto box = Ctx->getSceneManager()->addCubeSceneNode(DMY_WIDTH, this, -1, tpos)->setAlphaAndDiffuse(0x7fFF0000);
	box->setRotation(rtt * core::RADTODEG);
#endif

	//DP(("CAM BEG %f %f %f,rot %f %f %f <<<<<<<<<<<<<<<<<<<<<<<<<<<<", tpos.X, tpos.Y, tpos.Z, rtt.X, rtt.Y, rtt.Z));

	auto& keys = m_vmdCameraAnim->m_cameraController->m_keys;
	auto& lastKey = keys[keys.size() - 1];
	lastKey.m_distance = -(tpos - pos).getLength();
	lastKey.m_interest = tpos;

	//if (rtt2.Z > core::PI) rtt2.Z -= core::PIx2;
	//if (rtt2.Z < -core::PI) rtt2.Z += core::PIx2;
	rtt.set(-rtt.X, -rtt.Y, -rtt.Z);
	if (rtt.Y > lastKey.m_rotate.y + core::PI + 0.0001f) rtt.Y -= core::PIx2;
	if (rtt.Y < lastKey.m_rotate.y - core::PI) rtt.Y += core::PIx2;
	if (rtt.X > lastKey.m_rotate.x + core::PI) rtt.X -= core::PIx2;
	if (rtt.X < lastKey.m_rotate.x - core::PI) rtt.X += core::PIx2;

	lastKey.m_rotate = rtt;
#if 0
	if (Pm.needSaveCamVmd) {
		using namespace vmd;
		std::sort(vmdWriteFile->camera_frames.begin(), vmdWriteFile->camera_frames.end(),
			[&](VmdCameraFrame& a, VmdCameraFrame& b) {return a.frame < b.frame; });
		vmd::VmdCameraFrame& k = *(vmdWriteFile->camera_frames.end() - 1);
		k.distance = lastKey.m_distance;
		memcpy(&k.position, &lastKey.m_interest, sizeof(k.position));
		memcpy(&k.orientation, &rtt, sizeof(k.orientation));
		vmdWriteFile->SaveToFile(L"d:/mmd/vmd/camout.vmd");
	}
#endif
	mModel->UpdateAllAnimation(Vmd.get(), 0, 0);
}



#if HAS_MIDI
#define KEY_FW_HIGHT 7
#define KEY_FW_RADIOUS 6
void irr::scene::IrrSaba::midiEventToEmb(int mode, irr::scene::SabaEmb& emb, midilib::MidiEventStruct& evt, float time)
{
	emb.life = emb.timer = MIDI_DELAY;
	//if (evt.channel > 0)  return;

	switch (mode)
	{
	case 1:
	{
		int key = evt.key - 36;
		//emb.tgt.set(ualib::UaRandm1to1(), ualib::UaRandm1to1(), 0);
		const int diver = 120, diverm1 = diver - 1;

		float y = key / diver;
		//float mul = (key / diver) % 2 ? 1 : -1;
		float x = key % diver;
		int k1 = (midiNodeFlag == 3) ? (evt.key < 60) : 0;
		x = (-3 - 30 * (x / diverm1) + 6 - k1 * 6);
		//x = ((evt.key -60) / 180.f +0.5) * PI;// +Ctx->gd.time * PI;
		float r = KEY_FW_RADIOUS;
		emb.tgt.set(
			x, KEY_FW_HIGHT, -9
			//cos(x) * r, sin(x) * r+r
			//cos(x) * r, KEY_FW_HIGHT,-sin(x) * r

		);
		DP(("key %d, %d,v=%f, %f", key, evt.track, evt.velocity, emb.tgt.Y));
	}
	break;
	case 10: //piano
	{
		if (!piano) break;
		emb.tgt.set(piano->getKeySn(emb.key)->getAbsolutePosition());

		//emb.tgt.Z += piano->keyBL / 4;// : piano->keyWL / 2;
		irrToMmdPos(emb.tgt);
	}
	break;
	case 11: //guqin
	{
		const int diver = 12, diverm1 = diver - 1; float ct = diverm1 / 2;
		float oct = evt.key / diver;
		float note = evt.key % diver;
		emb.tgt.set((oct - 6) * 0.1, 0, 2 * ((note - ct) / diverm1));
		emb.tgL.set(0.5 - (oct - 6) * 0.2, 0, 1.7 * ((note - ct) / diverm1));
	}
	break;
	case 21:
	{

	}
	break;
	}

}

void irr::scene::IrrSaba::onMidiEvent(midilib::MidiEventStruct evt)
{
	//if (evt.channel != 0)		return;

	//if (Ctx->gd.time - lastMidiTime[evt.track] < 0.05)		return;
	lastMidiTime[evt.track] = Ctx->gd.time;
	SabaEmb emb; emb.me = evt; emb.mePlayed = false;

	emb.key = evt.key;
	//emb.isBlack = piano->isBkey[emb.key];
	emb.ch = evt.track;// evt.track;
	emb.vel = evt.velocity;
	midiEventToEmb(MmdMode, emb, evt, Ctx->gd.time);
	embs.push_back(emb);
}



#endif

void irr::scene::IrrSaba::updateMmdMode1(float deltaTime)
{
#if HAS_MIDI
	//if (Pm.idx != 0) return;
	std::vector<SabaEmb> embsNew;
	bool removed[8] = {};
	for (size_t i = 0; i < embs.size(); i++)
	{
		SabaEmb& emb = embs[i];
		float ratio = emb.timer / emb.life;
		float ratio1 = (emb.timer - deltaTime) / emb.life;
		core::vector3df pos = emb.tgt, posG, vel;

		pos = pos * svgPm.drawSize + posIKArmR;

		switch (MmdMode)
		{
		case 1:
			//pos.X += 1;
			//pos.X += 0;//emb.tgt.X * 2 * ratio;
		{
			//pos.Y =   pos.Y* (ratio);
			//pos.Z +=  -ratio * 20;
			pos.Y = KEY_FW_HIGHT + 49 - pow((1 - ratio) * 7, 2);
			vel.set(0, KEY_FW_HIGHT + 49 - pow((1 - ratio1) * 7, 2) - pos.Y, 0);
		}
		break;
		case 10:
		{
#if 0
			pos.Y = 1 + 49 - pow((1 - ratio) * 7, 2);
			vel.set(0, 1 + 49 - pow((1 - ratio1) * 7, 2) - pos.Y, 0);
#else
			auto pt = pos;
			vector3df po = pos + vector3df{ 0,50,0 };
			//Pmx->GetNodeManager()->getRootNode()->getGlobalPos();
		//pos.Y = 1+ratio*(po.Y-1);
			pos = pt + ratio * ratio * (po - pt);
			pos.Y = pt.Y + ratio * (po - pt).Y;

			vel = pt + ratio1 * (po - pt) - pos;
#endif

		}break;
		case 11:
			pos.Y += 10 * ratio;
			break;
		}
		posG = pos;
		emb.curpos = posG;
		mmdBaseMat.transformVect(posG);// posG.Z -= piano->getKeySn(emb.key)->getAbsoluteTransformation().getScale().Z/2-20;

		static float hue = 0.0f; hue += 0.01;
		float angle = hue / 360 * 2 * core::PI;
		hue = hue + this->itemIdx * 180 + emb.ch * 90;
		if (HAS_PIANO) hue = 360.f * (emb.key % 88) / 88.f + Ctx->gd.time * 60;
		SColorHSL hsl(hue, 50, 50);
		SColor sc = hsl.toSColor();

		emb.timer -= deltaTime;

		if (emb.timer > 0.f) {
			if (emb.timer > 0.001f) {
				vector3df v = vel / deltaTime; mmdToIrrPos(v);
				EQVisual::LfwParam lpm; lpm.pmz = drawStrokeRatio;
				lpm.pv.x = 0;
				lpm.pv.y = emb.me.duration;
				Eqv->LaunchFw3D(V3dToFloat3(posG), Eqv->getFwIdx(2, 1), v, sc, &lpm);
			}
			embsNew.push_back(emb);
			if (removed[emb.ch])
			{
				removed[emb.ch] = false;
				lastTimer[emb.ch] = emb.timer;

#if MMD_MONTAGE
				adAnmIdLast = adAnmId;
#if MMD_PLAY_GUQIN
				maxrhanim = mModel->vmdAddAnims.size() / 2;
				adAnmId = (emb.tgt.Z > lastEmbPos.Z) ? maxrhanim : 0;//out in
				rhAnimCC[adAnmId] += 1;
				if (lastTimer > 0.33)
					adAnmId += rhAniIdAdd % maxrhanim;//rhAnimCC[adAnmId] % 2;
#elif MMD_MTG_HANDSEAL
				adAnmId = emb.key % 12 + 1;
#else
				maxrhanim = mModel->vmdAddAnims.size();
				if (maxrhanim) {
					adAnmId = emb.key;
					adAnmId = (adAnmId + rhAniIdAdd) % maxrhanim;
				}
#endif
#endif
			}
		}
		else {
			handUpRatR = 0.f;
			if (ndHandL) {
				if (midiNodeFlag == 1) {
					//if (emb.ch == 0)
					//ndFootL->rb0->applyCentralImpulse(float3{ 000,1600,-1000 }*(std::max(-1.0f, 1 - emb.timer)));
					//ndLegL->rb0->applyCentralImpulse(float3{ 000,360,-100 }*(std::max(-1.0f, 1 - emb.timer)));
					//ndFootR->rb0->applyCentralImpulse(float3{ -000,1600,-1000 }*(std::max(-1.0f, 1 - emb.timer)));
				}
				if (midiNodeFlag == 2) {
					//if (emb.ch == 0)
					ndHandL->rb0->addForce(float3{ 000,1600,-1000 }*(std::max(-1.0f, 1 - emb.timer)), true);
					//else
					ndHandR->rb0->addForce(float3{ -000,1600,-1000 }*(std::max(-1.0f, 1 - emb.timer)), true);
				}
			}
			auto fw = Eqv->getFwIdxByFwIdStr(2, "poFlower");
			if (!HAS_PIANO) for (int i = 0; i < 1; i++)
			{
				float size = 2;
				PhyObjParam pm{ 1 ,100.f * size * size * size,{size,size,size },{ 0,0,0 },{},{ 30,30,33 } };
				pm.visible = 0;
				pm.autoKill = true;
				pm.camShoot = 0;
				pm.hasFw = true;
				pm.fwIdx = fw;
				pm.gravityMul = 0.3f;
				pm.color = sc.color;
				pm.timer = 3.f;
				vector3df pos = posG; irrToMmdPos(pos);
				pm.pos = pos;
				vector3df hp = ndHead->getGlobalPos() //{ -17, -0, 2 };//
					+ float3(UaRandm1to1() * 6, UaRandm1to1() * 6 + 3, UaRandm1to1() * 6 - 3);
				pm.vel = (hp - pos) * 1;

				rbt = Pom->addObj(pm)->rb;
			}
#if MMD_POSE_VIEWER

			static int pcc = 0;
			float durNext = 1.f; if (i < embs.size() - 1) durNext = embs[i + 1].timer;
			Pm.mmd->loadPoseId(ualib::UaRand(12),
				//pcc++ % 12, //
				//emb.key % 12,
				std::max(12.f, durNext * 60));
			vtxFwId = (vtxFwId + 1) % 12;
#endif
			Eqv->LaunchFw3D(V3dToFloat3(posG), Eqv->getFwIdxByFwIdStr(1, "soundFw1"), (emb.ch == 0 ? ndHandR : ndHandL)->exd.ms.spd, sc);

			lastEmbPos = emb.tgt;
			removed[emb.ch] = true;
			lastHitTime[emb.ch] = Ctx->gd.time;
#if HAS_MIDI
			if (snGuqin) snGuqin->onMidiEvent(emb.me);
			if (!emb.mePlayed)
			{
				//Pm.midi->playEvent(emb.me.midevt);
				//DP(("LOST PLAY %d", emb.me.key));
				emb.mePlayed = true;
			}
#endif
		}
	}
	embs = std::move(embsNew);
	//ndGroup->SetAnimationTranslate({0,sin(Ctx->gd.time*core::PI)-2,0});

	EQVisual::SvTimePt stp;

	int chProced[MAX_MMD_MIDI_CH] = {};
	//int chFirst[MAX_MIDI_CH] = {};
	//vector3df chTgt[MAX_MIDI_CH];
	//int chNum[MAX_MIDI_CH] = {};
	//for (int i=0;i<embs.size();i++)
	//{
	//	auto& emb = embs[i];
	//	if (!chFirst[emb.ch]) {
	//		chFirst[emb.ch] = i; chTgt[emb.ch] = emb.tgt; chNum[emb.ch] = 1;
	//	}
	//	else if (abs(emb.timer - embs[chFirst[emb.ch]].timer) < 0.01) {
	//		chTgt[emb.ch] += emb.tgt; chNum[emb.ch]++;
	//	}
	//}
	//for (int i = 0; i < MAX_MIDI_CH; i++) if (chNum[i] > 0)	chTgt[i] = chTgt[i] / chNum[i];
	lookTgtMmdPos = SceneManager->getActiveCamera()->getAbsolutePosition();
	mmdBaseInv.transformVect(lookTgtMmdPos);//cam pos to model space

	for (auto& emb : embs)
	{
#if MMD_MONTAGE
		if (chProced[emb.ch]) continue;
		chProced[emb.ch] = 1;

		if (emb.timer < 0.6)
			lookTgtMmdPos = emb.curpos + vector3df{ 0.f, 1.f, -6.f }; lookTgtMmdPos.Y = std::max(lookTgtMmdPos.Y, 10.f);

		handUpRatR = std::max(0.f, 1.f - std::min(1.f, emb.timer / lastTimer[emb.ch]));
#if MMD_PLAY_GUQIN
		if (handUpRatR > 1 - sqrt(0.5f)) aniRateR = cos((.707f * (handUpRatR - (1 - sqrt(0.5f)))) * core::PI);
		else aniRateR = sin(pow(1 - handUpRatR, 2) * core::PI);
#elif MMD_MTG_HANDSEAL
		aniRateR = (sin((handUpRatR - 0.25) * core::PI * 2) + 1) / 2;
#else
		aniRateR = sin(handUpRatR * core::PI);
#endif
		//if (emb.ch == 0)			mpA->SetWeight(core::clamp(0.7f - aniRateR*0.7f,0.f,0.7f));
		stp.color = 0xFFFFFFFF;
		//stp.flag = 0x20;
		stp.spFlag = 0x22;
		static int CC = 0; CC++; static int DD = 0; DD++;
		MMDNode* nd = {};
		if (midiNodeFlag == 1) nd = (emb.ch == 0 ? ndHandR : ndHandL);
		else if (midiNodeFlag == 2)	nd = emb.ch == 0 ? ndFootR : ndFootL;
		else if (midiNodeFlag == 3)	nd = ndUpper2;

		else nd = emb.ch == 0 ? (CC % 2 ? ndHandR : ndHandL) : (DD % 2 ? ndFootR : ndFootL);
		auto rb = nd->rb0;

		bool bnear = emb.timer < 0.1;
		auto tgt = emb.tgt + vector3df{ 0.f, emb.timer > 0.2f ? 2.f : bnear ? 1.f : 3.f, 0.f };

		if (emb.timer < 0.001f) {
			vector3df pos = tgt; mmdToIrrPos(pos); pos.Y = 1;
			//Eqv->LaunchFw3D(V3dToFloat3(pos), Eqv->getFwIdxByFwIdStr(1, "footDown"), nd->exd.ms.spd);

		}
		//else
		if (lastTimer[emb.ch] > 0.000001f) {
			float r = uu::blendFun(uu::Ebf::easeInOutSine, handUpRatR);

			//emb.tgt =
			vector3df yadd(0, aniRateR * lastTimer[emb.ch] * 1.f, 0);
			stp.pos = tgt;//  +yadd;
			stp.poL = emb.tgL;
			stp.ik = emb.ch;
			curSTP[stp.ik] = stp;
			drawAdd = false;
			if (cbOnSvFwCenterPt) Eqv->cbOnSvFwCenterPt(stp, 5, 1 / 60.f);
			else //onSvFwCenterPt(stp, 5, 1 / 60.f);
			{
				evtMoving[stp.ik] = true;
				drawPos = stp.pos;
				curSTP[stp.ik].pos = drawPos;
				//auto nd = emb.ch == 0 ? ndPinHandR : ndPinHandL;nd->SetAnimationTranslate(emb.tgt);
				mModel->addAnimOn = true;
			}

			auto mpos = stp.pos;// irrToMmdPos(mpos);
			//mpos.Y +=  std::max(1.f, aniRateR * 3 - 2);//			DP(("r %f",aniRateR));
			vector3df spos = float3(nd->rb0->GetTransform()[3]);
			vector3df dir = mpos - spos;;
			//

			if (emb.timer < 0.03 //||  Ctx->gd.time-lastHitTime[emb.ch]<0.3f && !bnear
				)
			{
				matrix4 mp; mp.setTranslation(mpos);
				rb->setLinearVel(rb->getLinearVel() * 0.1f);

				if (emb.timer < 0.001) {
					rb->addForce({ 0,3900,0 }, true);
					if (spos.Y < 2) {
						float3 yap = ndYao->getGlobalPos();
						ndYao->rb0->addForce({ 0,9900, (-3 - yap.z) * 1000 }, true);
					}
					//vector3df pos = float3(rb->GetTransform()[3]); mmdToIrrPos(pos); Eqv->LaunchFw3D(pos, Eqv->getFwIdx(2, 0), nd->exd.ms.spd);
				}
			}
			else
			{
				float dq = dir.getLengthSQ(); //if (dq<1) mRb->setLinearVelocity(mRb->getLinearVelocity() * 0.5f);
				float fm1 = core::clamp(dq, 0.01f, 2.f);
				vector3df force = dir.normalize() * fm1
					* 10
					* core::clamp(dq, 0.01f, 2.f) * (bnear ?

						emb.vel * (midiNodeFlag == 3 ? 16 : 1) :
						(midiNodeFlag == 3 ? 16 : midiNodeFlag == 2 ? 18 : 18)
						* (std::max(-0.1f, 1.f - emb.timer / 2)))
					//+ (midiNodeFlag == 2&& emb.timer<0.6f ? vector3df{0,900,0} : vector3df{})
					;
				rb->addForce(force, true);

			}

			auto rdir = dir; rdir.Y = -rdir.Y;
			matrix4 mTurni; //mTurni.setRotationDegrees(rdir.getHorizontalAngle());
			//ndYao->rb0->applyTorqueRotateToI(glm::mat4(mTurni), 3);
			matrix4 mTurn; //mTurn.setRotationDegrees(rdir.getHorizontalAngle());
			//ndYao->rb0->applyTorqueRotateTo(glm::mat4(mTurn), 10);
			//ndUpper->rb0->applyTorqueRotateTo(glm::mat4(mTurn), 10);
			ndUpper2->rb0->applyTorqueRotateTo(glm::mat4(mTurn), 3);
			vector3df footC = ((ndFootL ? ndFootL : ndFootR)->getGlobalPos() + ndFootR->getGlobalPos()) / 2.f;
			footC.Y = 15 + aniRateR * 0.5 + emb.timer * 1.25;// ndCenter->GetTranslate().y;
			//footC.Y = ndPinYao->rb0->GetTransform()[3][1];
			//footC.X = 0; 	footC.Z = 0; //footC.Y = 16;
			pinYaoTgt.interpolate(footC, pinYaoTgt, 0.07f);

			matrix4 pym; pym.setTranslation(pinYaoTgt - Pmx->rt1Tr);
			//if (removed[0] || removed[1]) ndUpper2->rb0->applyCentralImpulse({ 0,100000,0 });
			//ndUpper2->rb0->SetCoMTranslate(glm::vec3(pinYaoTgt));
			if (midiNodeFlag == 2 && ndPinYao) {
				ndPinYao->setAnimationMatrix(pym);
			}
		}
#endif
	}

	if (midiNodeFlag == 1) {
		//phyLeadTo(ndHandL->rb0, {-2,15,-10}, {3,20,0},300);
		//phyLeadTo(ndFootR->rb0, { -10,0,-16 }, { 2,10,-3 }, 300);
		phyLeadTo(ndFootL->rb0, { -3,0,-16 }, { 7,10,-3 }, 300);
	}
	if (midiNodeFlag == 2) {
		//ndUpper2->rb0->addForce(vector3df{0,600,0}, true);
	}
	//float dyy = ndYao->getGlobalPos().y - (Pmx->yaoPos.y+5);
	//if (dyy<0)	ndUpper2->rb0->addForce({ 0,100000*(-dyy),1000});

	if (!chProced[0])
	{
#if MMD_PLAY_GUQIN
		handUpRatR += 0.033; lastTimer = 0.33;
		if (handUpRatR > 1.f) handUpRatR = 1.f;
		if (handUpRatR > 1 - sqrt(0.5f)) aniRateR = cos((.707f * (handUpRatR - (1 - sqrt(0.5f)))) * core::PI);
		else aniRateR = sin(pow(1 - handUpRatR, 2) * core::PI);
		stp.color = 0xFFFFFFFF;
		//stp.flag = 0x20;
		stp.flag = 0x22;
		stp.pos.X = 0; stp.pos.Y = 0;
		drawAdd = true;
		Eqv->cbOnSvFwCenterPt(stp, 5, 1 / 60.f);
#endif
	}
	aniRateR = core::clamp(aniRateR, 0.f, 1.f);
	motionMul = lastTimer[0] * 1;
#endif
}

//  *******************************************  MMD MIDI PLAYER  *******************************************
//#pragma optimize( "", off )
void irr::scene::IrrSaba::mmdSynthVSingNote()
{
	if (!Pmx->isCharacter) {

		//	return;
	}


	if (gSceneTime - lastRbHitTime > 5.5f) {
		auto ndAou = [=](MMDNode* node, const std::vector<std::wstring>& words) {
			if (!node) return false;
			bool impact = false;
			if (node->rb0) {
				//node->rb0->usrDat.callHitCb = true;
				//node->rb0->cbHit = [=](saba::PhysicsEngineObjectUserData* hit)
				//	{
				//	DPWCS((L"HITRB %s - %s", hit->owner->node->GetNameU().c_str(), hit->lastContact->owner && hit->lastContact->owner->node ? hit->lastContact->owner->node->GetNameU().c_str() : 0));
				//	bool impact = false;
				//	if (glm::length2(hit->hitImp) >= 1.f && gSceneTime - lastRbHitTime > 0.3f
				//		//&& glm::length(node->rb0->vel - node->rb0->lvel) / MMDPhysics::phyTimeMul > 0.f
				//		) {
				//		if (hit->lastContact->owner && hit->lastContact->owner->node)
				//			assert(hit->lastContact->owner->node->model != Pmx);
				//		lastRbHitTime = gSceneTime;
				//		mmd->mdplr.aouWords(itemIdx, words);
				//		impact = true;
				//	}
				//	mmdFw(1, impact ? "daTieHua" : "daTieHuaS", node->rb0->pos, hit->hitImp, 0xFFFFFFFF);
				//	};
				auto rb = node->rb0;
				const saba::PhysicsEngineObjectUserData& ud = rb->usrDat;
				if ((gPhyTime - ud.contactTime < 0.1f)) {

					vec3 dtVel = (node->rb0->vel - node->rb0->lvel) / MMDPhysics::phyTimeMul;
					float dtVelLen = glm::length(dtVel) / MMDPhysics::phyTimeMul;

					if (dtVelLen > 20.f) {
						lastRbHitTime = gSceneTime;

						mmd->mdplr.aouWords(itemIdx, words);
						
						impact = true;
					}
					if (ndManco) for (int i = 0; i < 10;i++) {
						mmdFw(1, "jetOpi", ndManco->getGlobalPos(), ndLower->rb0->vel*2.f, 0xFFFFFFFF);
					}
					if (dtVelLen > 3.f) mmdFw(1, impact ? "daTieHua" : "daTieHuaS", node->rb0->pos, dtVel, 0xFFFFFFFF);
				}
			}
			return impact;
			};
		std::vector<std::pair<saba::MMDNode*, std::vector<std::wstring>>> nodes = {
			{ndHandL, {L"o", L"te", L"te", L"te" }},
			{ndHandR, {L"o", L"te", L"te", L"te" }},
			{ndFootL, {L"a", L"shi", L"a", L"shi"}},
			{ndFootR, {L"a", L"shi", L"a", L"shi"}},
			//{ndHead, {L"attama"}},
			{ndOpaiL, {L"ta", L"ma", L"ta", L"ma"}},//{L"mu", L"ne", L"mu", L"ne"}},
			{ndOpaiR,  {L"ta", L"ma", L"ta", L"ma"}},//{L"mu", L"ne", L"mu", L"ne"}},
			{ndLegL, {L"fu", L"to", L"mo", L"mo"}},
			{ndLegR, {L"fu", L"to", L"mo", L"mo"}}
		};

		for (const auto& node : nodes) {
			if (ndAou(node.first, node.second)) {
				break;
			}
		}
	}


	//if (itemIdx == 0) 	Pm.mmd->mdplr.updateFrame(stepTime);

	int ch = itemIdx;
	auto ndHdt = ndHeadTgt ? ndHeadTgt : ndHead ? ndHead : ndRoot;
	vector3df pos = ndHdt->getGlobalPos();// glm::vec3(ndHeadTgt->rb0->GetTransform()[3]);
	//DP(("an %f,%f,%f", ndHead->rb0->getAngularVel().x, ndHead->rb0->getAngularVel().y, ndHead->rb0->getAngularVel().z));
	vector3df vel = (ndHdt->exd.irrSpeed + //(ndHandL?(ndHandL->exd.aniSpeed + ndHandR->exd.aniSpeed): ndHdt->exd.aniSpeed*2.f)) * 0.1f
		(ndOpaiL ? (ndOpaiL->exd.irrSpeed + ndOpaiR->exd.irrSpeed) : ndHdt->exd.irrSpeed * 2.f)) * 0.33f
		//(ndFootL ? (ndFootL->exd.aniSpeed + ndFootR->exd.aniSpeed) : ndHdt->exd.aniSpeed * 2.f)) * 0.1f
		;		mmdBaseInv.transformVect(vel);
	//vel += ndHead->rb0->getAngularVel() * 10.f;
	/*(ndHead->rb0->getLinearVel()
		+ ndHandL->rb0->getLinearVel() + ndHandR->rb0->getLinearVel()
		)
		* 0.1f;*/
	auto irrpos = pos; mmdToIrrPos(irrpos);
	//DP(("Y %f", vel.Y));
	//pos.Y -= ndHead->pmxBone->m_position.y;
	//pos = vec3(ndHead->rb0->getLinearVel()*0.03f)+vec3(0,0.5,0);
	auto& plr = Pm.mmd->mdplr;
	auto& cd = plr.chd[ch];
	if (Pm.mmd->sb0->mic)	cd.camDis = glm::length(Pm.mmd->sb0->mic->Rb0()->getPosition() - ndHdt->getGlobalPos());
	else   cd.camDis = glm::length(float3(mmdLookAt) - ndHdt->getGlobalPos())/ ndRbRoot->absScale.x;
	cd.camPos = mmd->camPos / ndRbRoot->absScale.x;
	cd.camVel = mmd->camVel / ndRbRoot->absScale.x;
	if (ch < 16) Pm.mmd->mdplr.updateMdNode(ch, pos / ndRbRoot->absScale, vel / ndRbRoot->absScale, MMDPhysics::phyTimeMul);// 1 - lhd.grab, 1 - lhd.pinch);

#if 0
	if (cd.act == 1) {
		if (mic) {
			matrix4 th, t = mic->Pmx->GetNodeManager()->GetMMDNode(2)->GetGlobalTransform();
			th = mic->Pmx->mtRootParent * mat4(t); t = mic->mmdBaseMat * t;
			Eqv->LaunchFw3D(t.getTranslation(), Eqv->getFwIdxByFwIdStr(1, "handFw"), t.getRotatedVect({ 0,0,-10 }));
			ndHandL->rb0->addForce(t.getRotatedVect({ 0,0,1 }) * 0.01f, true);
		}
		if (ndHandL->rb0) {
			//ndHandL->rb0->addForce({ 0.f,0.2f * core::clamp(cd.ckey - 60,1,10),0.f }, true);
			//ndHandR->rb0->addForce({ 0.f,1.f * (cd.ckey - 60),0.f }, true);
		}
		ndYao->rb0->addForce({ 0.f,1.f * (cd.ckey - 50),-10.f }, true);
	}
#endif

	if (lipArr.size() > lipId) {
		if (lipArr[lipId].timeS <= Ctx->gd.time) {
			float dur = lipId < lipArr.size() - 1 ? lipArr[lipId + 1].timeS - lipArr[lipId].timeS : 1.f;
			appendLipMorph(lipArr[lipId].txt, dur);
			DP(("LIP %d %s %f", lipId, lipArr[lipId].txt.c_str(), dur));
			lipId++;
		}
	}
}

void irr::scene::IrrSaba::setFxMpCallback(int ch)
{
	auto& plr = Pm.mmd->mdplr;
	plr.Sbs[ch] = this;
	plr.cbOnFxText[ch] = [=](std::string txt, float timeS, int reset) {

		if (reset) {
			lipArr.clear();
			lipId = 0;
			lipStartS = Ctx->gd.time;
			Pmx->GetMorphManager()->resetAll();
			if (lastMp) lastMp->aaUpdate(99.f);
		}
		lipArr.push_back({ txt,float(Ctx->gd.time + timeS) });

		if (mpSpr) mpSpr->appendMorph(2.0, 0.f, 0.2f);
		//if (mpXiao) mpXiao->appendMorph(2.0, 0.f, 0.2f);
		//Pmx->addBodyVel({ 0,10,0 });
		};


	plr.onNoteCb[ch] = [=](int ch, SvNoteData& note) {
		if (note.rap) {
			ndUpper2->rb0->addLinearVel({ 0,90,0 });
			ndUpper->rb0->addLinearVel({ 0,60,0 });
			ndYao->rb0->addLinearVel({ 0,30,0 });
		}
		if (onSvNoteCb)
			onSvNoteCb(ch, note);
		};
}

//#pragma optimize( "", on )
//LLLLLLLLLLLLLLEAP UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU
void irr::scene::IrrSaba::leapUpdate()
{
	auto rb0 = Pmx->getRb(0);
	bool isPhy = rb0 && rb0->GetActivation();
	auto nm = Pmx->GetNodeManager();
#if USE_LEAP
	if (ndFgrs[0][0][1])
	{

		saba::MMDNode** bfg[] = {
			ndFgrs[0][4] ,ndFgrs[0][3] ,ndFgrs[0][2] ,ndFgrs[0][1] ,ndFgrs[0][0] ,
			ndFgrs[1][0] ,ndFgrs[1][1] ,ndFgrs[1][2] ,ndFgrs[1][3] ,ndFgrs[1][4]
		};
		for (int i = 0; i < 10; i++)
		{

			if (auto n = bfg[i][1]) {
				vec3 v = -vec3(std::clamp((bandv[i] - 0.02f), -0.02f, 0.7f), 0, 0);
				vec3 v0 = glm::eulerAngles(n->GetAnimationRotate());
				v = glm::mix(v0, v, 0.5f);
				bfg[10 - 1 - i][1]->SetAnimationRotate(quat(v));
				bfg[10 - 1 - i][2]->SetAnimationRotate(quat(v));

				//fg->SetAnimationRotate(glm::quat(m)); fg->noAnim = true;
				//fg->debug = 1;
			}
		}
	}


	if (!leapOn) return;

	if (!leap)	return;

	if (itemIdx > 0)	return;
	//if (Pm.mmd->curSb != this) return;
	if (leap->Lhd[1].sn[0][3] && leap->sbR)//leap->sbR)
	{

		if (sbCloth) sbCloth->snCloth->setCornerPos({ {
				//irr2mmd(leap->Lhd[0].sn[0][4]->getAbsolutePosition()),irr2mmd(leap->Lhd[0].sn[1][4]->getAbsolutePosition()),			irr2mmd(leap->Lhd[1].sn[0][4]->getAbsolutePosition()),			irr2mmd(leap->Lhd[1].sn[1][4]->getAbsolutePosition())
				leap->getMmdNode(0, 0, 3)->getGlobalPos(),
				leap->getMmdNode(0, 1, 3)->getGlobalPos(),
				leap->getMmdNode(1, 0, 3)->getGlobalPos(),
				leap->getMmdNode(1, 1, 3)->getGlobalPos()
				} });

	}


	for (int i = 0; i < 2; i++) {
		auto& lhd = leap->Lhd[i]; auto& rhd = leap->Lhd[1 - i];
		{
			bool lh = i == 0;
			core::matrix4 m, mt;
			m = lhd.mtabs;// ort.getMatrix(); m.setTranslation(hd.pos);
			//m = matAbsInv * m;
			m = m.getTransformTR();


			if (isPhy) {
#if 1
				m.setTranslation(m.getTranslation() + vector3df(0, -16, 0));
				matrix4 mi; lhd.mt.getInverse(mi);
#endif
#if 0
				ndUpper2->
					//ndLegL->
					//rb0->applyTorqueRotateTo(glm::mat3(ndPinYao->GetGlobalTransform()), 60);
					//rb0->applyRotateTo(glm::eulerAngles(ndPinYao->GetAnimationRotate()), 60);
					rb0->SetCoMTransform(ndPinYao->GetGlobalTransform());
#endif
#if PUPPET_2
				matrix4 my = m, mbi;
				//mb.setTranslation({ 0, 0, 0 });
				mbi = mmdBaseInv;
				//leapMat(matrix4(), m, ndPinYao, { 1,1,1 }, { 0, 0, 0 });

				if (LEAP_HAND_MOVE_RB && lh)
				{
					auto m1 = m;
					m1.setTranslation(m1.getTranslation() + vector3df{ 0,30,10 });
					m1.setRotationDegrees(m1.getRotationDegrees() + vector3df{ 60,0,0 });
					ndUpper2->scaleVel(0.7f, 3);
					ndUpper2->rb0->setAngVelToRotateOnNode(mat3(m1), 20);
					//MMDFW(2, "sw21s", vec3(m1.getTranslation()), vec3(0,0,0), SColorf(1, 1, 0, 1));
					ndUpper2->rb0->addLinearVelToPos(m1.getTranslation(), 3);

					//ndUpper2->rb0->setRotateTo((m1* ndUpper2->rb0->getOfsMat()));
					//ndYao->rb0->setAngVelToRotateOnNode(mat3(m), 1000);
				}

				if (ndPinYao) {
					if (lhd.sb) {
						glm::vec3 rtt = lhd.mt.getRotationDegrees() * core::DEGTORAD;//		DP(("H rtt 1 %f,%f,%f", rtt.x, rtt.y, rtt.z));
						//ndUpper2->SetRbRotate(glm::quat(rtt));
						matrix4 mTurn;
						//ndUpper2->rb0->applyTorqueRotateTo(glm::mat4(mTurn), 3);
						auto ndm = lhd.sb->Pmx->GetNodeManager();
						auto mt = ndm->GetMMDNode(0)->GetGlobalTransform() * GlmTr(-6, -6, 6);
						//if (lhd.sb) ndPinYao->setAnimationMatrix(mt);
						//ndPinHandL->setAnimationMatrix(ndm->GetMMDNode(7)->GetGlobalTransform() * GlmTr(0, 0, 0), false);
						//ndPinHandR->setAnimationMatrix(ndm->GetMMDNode(3)->GetGlobalTransform() * GlmTr(1, 2, 0), false);
						//ndPinHandL->setAnimationMatrix(ndm->GetMMDNode(7)->GetGlobalTransform() * GlmTr(-6, 2, 3), false);
						//ndPinYao->setAnimationMatrix(ndm->GetMMDNode(0)->GetGlobalTransform() * GlmTr(3, -4, 6), false);
						//ndUpper2->
						 //rb0->SetCoMTransform(ndm->GetMMDNode(7)->GetGlobalTransform() * GlmTr(-3,-2,0) * GlmRt(0, 0, 1,-90) * GlmRt(0, 1, 0,-90)							);

#if BITE_FINGER
						auto fnd = leap->getMmdNode(0, 1, 3);
						float fdis = glm::length(fnd->getGlobalPos() - ndHead->getGlobalPos());
						if (fdis < 10)
						{
							lookTgtMmdPos = fnd->getGlobalPos();
							if (fdis < 10 && !leap->isNodeConnected(0, 1, 3))
							{
								if (toConnect < 1) {
									toConnect += Ctx->gd.deltaTime;
									ndHeadIK->EnableDeformAfterPhysics(true);
								}
								else {
									auto rbht = Pmx->getRb(Pmx->headTgtRbId);
									ndHeadIK->EnableDeformAfterPhysics(false);
									leap->connectNodeWithRb(0, 1, 3, rbht);
									Pm.mmd->mdplr.aouStart(itemIdx, 10, mmd2irr(ndHead->getGlobalPos()));
									toConnect = 0;
								}
							}
						}
#elif 1
						if (lh)
						{
							//matrix4 mt = leap->getMmdNode(0, 2, 1)->GetGlobalTransform() *GlmTr(0, -3, 0)* GlmRt(1, 0, 0, -90);
							ndPinYao->setAnimationMatrix(lhd.mt * GlmTr(0, -4, 3)
								//* GlmRt(0, 1, 0, -90)
								* GlmRt(1, 0, 0, -90)
								* GlmRt(0, 0, 1, -30)
							);
							//ndPinYao->SetGlobalTransform(mt);
							if (lhd.grab <= 1)
							{
								//rb0->addForce((tgt - ndYao->getGlobalPos()) * (1.01 - lhd.grab) * 10000.f, true); //CENTER FORCE
								matrix4 mTurn = mt;
								//rb0->setAngVelToRotateOnNode(glm::mat4(mTurn), 100);
								//rb0->SetCoMTransform(mt);
							}
						}
#endif
					}
					//if (lh) ndPinHandR->setAnimationMatrix(lhd.mt);
					//else ndPinHandL->setAnimationMatrix( lhd.mt);
					//ndPinFootR->setAnimationMatrix(mbi * lhd.sn[0][4]->getAbsoluteTransformation());
					//leapMat(my, mbi * lhd.mtf[4][4], ndPinHandR, { 1.f,lh ? 1.75f : 1.5f,0 }, { 0, 6,0 });
					//leapMat(my, mbi * lhd.mtf[0][4], ndPinFootR, { 1.95,1.5,1.75 }, { 0, -6, -10 });
				}
				else if (ndPinHandL && lh)
				{
					auto m1 = m;
					m1.setTranslation(m1.getTranslation() * 2 + vector3df{ 0,0,10 });
					m1.setRotationDegrees(m1.getRotationDegrees() + vector3df{ 60,0,0 });

					ndRoot->SetAnimationTranslate(m1.getTranslation());

					ndRoot->setAnimGlobalPos(m1.getTranslation());
					DP(("GRAB %f  %f", lhd.grab, lhd.pinch));
					bool isD = lhd.pinch < 0.3;
					//charAtk.legGrabRat = glm::mix(charAtk.legGrabRat,lhd.pinch *0.9f,0.1f);// std::clamp(charAtk.legGrabRat + (!isD ? 0.5 : -0.5), 0.0, 1.0);
					if (lhd.pinch == 0.f) phyObjForceAttack({ 1,  float3(0, -10, 0), 0.06f,-1,15.f,6.f });
				}
#else
				matrix4 my = m, mbi;
				//mb.setTranslation({ 0, 0, 0 });
				mbi.scaleBy(1 / MMD_SABA_SCALE);
				//leapMat(matrix4(), m, ndPinYao, { 1,1,1 }, { 0, 0, 0 });
				leapMat(my, lhd.mtf[lh ? 0 : 4][3], ndPinHandL, { 1.f,lh ? 1.5f : 1.75f,0 }, { 2, 20, 0 });
				leapMat(my, lhd.mtf[lh ? 4 : 0][3], ndPinHandR, { 1.f,lh ? 1.75f : 1.5f,0 }, { 0, 20,0 });

				//leapMat(my, mbi * lhd.mtf[lh ? 1 : 3][3], ndPinFootL, { 1.95,1.5,1.75 }, { 0, -6, -10 });
				//leapMat(my, mbi * lhd.mtf[lh ? 3 : 1][3], ndPinFootR, { 1.95,1.5,1.75 }, { 0, -6, -10 });
#endif

			}
			else if (ndIKArmR && MMD_LEAP_FINGER) //no phy
			{

				bool isl = (PUPPET_2 ? !lh : lh);
				//if (!isl) continue;;
				auto ndh = isl ? ndIKArmL : ndIKArmR;//ndCenter : ndIKArmR;//

				matrix4 my = m, mbi;
				mbi.scaleBy(1 / MMD_SABA_SCALE / MMD_LEAP_SCALE);
				//MMDNode* ndh;
				matrix4 smat = lhd.mtabs;//	smat.setTranslation(smat.getTranslation()* vector3df{ 1, 1, -1 });
				bool ctrL = ((lh && !LEAP_SWAP_LR)) || (!lh && LEAP_SWAP_LR);
				auto ndHandRt = ctrL ? ndHandL : ndHandR;
				auto ndArm1 = ctrL ? ndArm1L : ndArm1R;
				auto ndHandIK = ctrL ? ndIKArmL : ndIKArmR;
				//ndHandRt->noAnim = true; ndHandIK->noAnim = true;
				//if (ctrL||1)
				{	//ndh = ndHandL;
					if (PUPPET_2) {
						matrix4 mi, m = ndh->GetParent()->GetGlobalTransform(); m.getInverse(mi); mi.setScale(1 / Eqv->arCamPosMul);
						ndh->setAnimationMatrix(mi * smat);
						ndh->SetAnimationTranslate(ndh->GetAnimationTranslate() + glm::vec3{ ctrL ? 3 : -3, 0, 0 });
						//ndFootIKR->setAnimationMatrix( lhd.sn[1][4]->getAbsoluteTransformation());

					}
					else {
						float rym = (ctrL ? 1 : -1);
						glm::mat3 mr = ndHandRt->GetParent()->GetGlobalTransform();
						ndHandRt->SetAnimationRotate(glm::quat(
							glm::mat4(glm::inverse(mr))
							* glm::rotate(glm::mat4(1), rym * core::PI, glm::vec3(0, 1, 0))
							* glm::mat4(lhd.mt)
							* glm::rotate(glm::mat4(1), -rym * core::PI / 2, glm::vec3(0, 1, 0))
							* glm::rotate(glm::mat4(1), rym * core::PI / 4, glm::vec3(0, 0, 1))
						));
#if MMD_ARM1_IK
						glm::mat3 mr1 = ndArm1->GetParent()->GetGlobalTransform();
						ndArm1->SetAnimationRotate(glm::quat(
							glm::mat4(glm::inverse(mr1))
							* glm::rotate(glm::mat4(1), rym * core::PI, glm::vec3(0, 1, 0))
							* glm::mat4(lhd.mtArm)
							* glm::rotate(glm::mat4(1), -rym * core::PI / 2, glm::vec3(0, 1, 0))
							* glm::rotate(glm::mat4(1), rym * core::PI / 4, glm::vec3(0, 0, 1))

						));
#endif

#if 1
						ndHandIK->setAnimationMatrix(
#if MMD_ARM1_IK
							matrix4() * glm::translate(glm::mat4(1), glm::vec3(ctrL ? -1 : 1, 3, -6))
							* glm::scale(glm::mat4(1), float3{ -1.f,1.f,-1.f }*float3{ 1.f,2.f,2.f }*0.2f) * lhd.mtArm
#else
							matrix4() * glm::translate(glm::mat4(1), glm::vec3(ctrL ? -1 : 1, 1, -3))
							* glm::scale(glm::mat4(1), float3{ -1.f,1.f,-1.f }*0.3f) * lhd.mt
#endif
#else
						ndHandIK->setAnimGlobalPos(
#if MMD_ARM1_IK
							mat4(lhd.mtArm)[3]
#else
							matrix4() * glm::translate(glm::mat4(1), glm::vec3(ctrL ? -1 : 1, 1, -3))
							* glm::scale(glm::mat4(1), float3{ -1.f,1.f,-1.f }*0.3f) * lhd.mt
#endif
#endif

						);
					}

				}


				for (int i = 0; i < 5; i++)for (int n = 1; n <= (MMD_LEAP_FINGER == 2 ? 2 : 3); n++)
				{
					if (auto& fg = ndFgrs[ctrL ? 0 : 1][i][n]) {

						if (!lhd.sn[i][n]) break;
						float rym = (ctrL ? 1 : -1) * (i == 0 ? -2 : 1);
						rym *= 0.5f;
						glm::quat q = glm::quat(lhd.sn[i][n]->getRelativeTransformation());
						glm::vec3 rtt = glm::eulerAngles(q); rtt = -rtt;
						if (MMD_LEAP_FINGER == 2) { float t = rtt.z; rtt.z = rtt.x * (ctrL ? 1 : -1); rtt.x = 0; }
						else rtt.x = -rtt.x;
						if (!LEAP_SWAP_LR) rtt.y = -rtt.y;// if (i == 0 && n < 2) rtt.y += 0.6;
						q = rtt;
						glm::mat4 m0(1);
						//if (i == 0  && n <2 )							m0 =glm::rotate(glm::mat4(1), 20 * DEGTORAD * (lhd.hid?1:-1), glm::vec3(0, 1, 0))* glm::rotate(glm::mat4(1), 17 * DEGTORAD, glm::vec3(1, 0, 0));
						glm::mat4 m =//glm::rotate(glm::mat4(1), -core::PI / 4, glm::vec3(0, 0, 1))*
							glm::rotate(glm::mat4(1), rym * core::PI, glm::vec3(0, 1, 0)) *
							// glm::inverse(m0)*
							glm::mat4(q) //*m0
							* glm::rotate(glm::mat4(1), -rym * core::PI, glm::vec3(0, 1, 0))
							//* glm::rotate(glm::mat4(1), core::PI / 4, glm::vec3(0, 0, 1))
							;
						fg->SetAnimationRotate(glm::quat(m));
					}
				}

				if (itemIdx == 0 && lhd.grab > 0.9f && lhd.lastGrab < 0.9f && !lhd.grabSb) {
					static int nearId = lh ? 1 : 2; float dis = 99999;
					nearId++; if (nearId >= Pm.mmd->sabas.size()) nearId = 1;
					//for (int i = 1; i <= std::min(2, (int)Pm.mmd->sabas.size() - 1); i++) {				float d = glm::length(Pm.mmd->sabas[i]->ndYao->getGlobalPos() - ndHandRt->getGlobalPos());						if (d < dis)		nearId = i, dis = d;}
					if (/*lh &&*/ nearId > 0 && nearId < Pm.mmd->sabas.size() && Pm.mmd->sabas[nearId]->isAiCharacter()) {
						lhd.grabSb = Pm.mmd->sabas[nearId];
					}
				}
				else if (lhd.grab < 0.5 && lhd.grabSb) {
					lhd.grabSb->ndYao->rb0->scaleVel(0.f);
					//lhd.grabSb->ndYao->rb0->setLinearVel(lhd.grabSb->ndYao->rb0->getLinearVel()+float3{ 0,10000,-20000 });
					Pm.mmd->mdplr.aouStart(lhd.grabSb->getItemIdx(), 10, 0, false);
					lhd.grabSb = nullptr;
				}
				else if (lhd.grabSb) {
					//	lhd.grabSb->ndRoot->SetAnimationTranslate(ndHandRt->getGlobalPos());
					lhd.grabSb->ndYao->rb0->addForce((ndHandRt->getGlobalPos() + vec3(0, 0, -2) - lhd.grabSb->ndYao->rb0->getPosition())
						* 20.f, true); //CENTER FORCE
					lhd.grabSb->ndYao->rb0->scaleVel(0.5f);
					auto cammat = mmdBaseInv * SceneManager->getActiveCamera()->getAbsoluteTransformation();
					//lhd.grabSb->ndHead->rb0->setAngVelToPos(ndHeadTgt->getGlobalPos() + vec3(0, 3, -6), 10.f);
					//lhd.grabSb->ndUpper2->rb0->setAngVelToRotateOnNode(ndHandRt->GetGlobalTransform(),10000);
				}

				if (ndIKArmR && !isPhy) {

					vector3df tgt;
					if (PUPPET_2) {
						auto pos = ndIKArmL->getGlobalPos() + ndIKArmR->getGlobalPos()
							+ (ndFootIKL->getGlobalPos() + ndFootIKR->getGlobalPos()) * 2.f
							;
						tgt = pos / 6.f + glm::vec3(0, -6, +2);
						leapCtrTgt.interpolate(tgt, leapCtrTgt, 0.1f);
						//	ndGroup->SetAnimationTranslate(leapCtrTgt);
					}
					else {
						auto pos = ndIKArmL->GetAnimationTranslate() + ndIKArmR->GetAnimationTranslate();
						tgt = pos / 2.f * float3(0.5, 0.5, 0.5) * 0.5f + glm::vec3(0, -3, 0);
						leapCtrTgt.interpolate(tgt, leapCtrTgt, 0.05f);
						ndGroup->SetAnimationTranslate(leapCtrTgt);
					}
				}
			}
		}




	}
#endif
}



void irr::scene::IrrSaba::leapNode(irr::scene::ISceneNode* ndSrc, saba::MMDNode* node)
{
	auto rt = -ndSrc->getAbsoluteTransformation().getRotationDegrees() * core::DEGTORAD;// rtt.y = -rtt.y;
	node->SetRbRotate(glm::quat(rt));
}
void irr::scene::IrrSaba::leapMat(irr::core::matrix4 m0, irr::core::matrix4 m, saba::MMDNode* node, irr::core::vector3df mul, irr::core::vector3df ofs)
{
	if (!node) return;
	using namespace glm;
	m.setTranslation(m.getTranslation() * mul + ofs);
	m.setRotationRadians({ 0,0,0 });
	node->setAnimationMatrix(m0 * m, true);
	auto rtt = m0.getRotationDegrees();
	//DP(("rtt %f,%f,%f", rtt.X, rtt.Y, rtt.Z));
	//node->rb0->SetCoMTransform(m0 * m);
}
void irr::scene::IrrSaba::phyLeadTo(saba::MMDRigidBody* rb, float3 pmin, float3 pmax, float fmul)
{
	if (rbt) {
		pmin += float3(Pmx->rootM1[3]); pmax += float3(Pmx->rootM1[3]);
		auto s = float3(rb->GetTransform()[3]);
		float lmin = 10000.f;
		PhyObj* po{};
		for (auto& obj : Pom->phyObjs) {
			auto& b = *obj;
			auto t = float3(b.rb->GetTransform()[3]), s = float3(rb->GetTransform()[3]);
			if (t.x > pmin.x && t.y > pmin.y && t.z > pmin.z && t.x < pmax.x && t.y < pmax.y && t.z < pmax.z) {
				vector3df dir = t - s;
				auto l = dir.getLength();
				if (l < lmin && (t.y > s.y || s.y > 3) && t.y>1) {
					rbt = b.rb;
					lmin = l;
					po = &b;
				}
			}
		}
		if (lmin < 100.f) {
			auto t = float3(rbt->GetTransform()[3]); t.y -= 0.66f; vector3df dir = t - s;
			float3 force = dir.getNormalizeCopy() * std::max(dir.getLength(), 2.f) * fmul;
			rb->addForce(force, true);
			if (lmin < 1.f) {
				rb->setLinearVel(rb->getLinearVel() * 0.1f + rbt->getLinearVel() * 0.25f);
				rbt->setLinearVel(rbt->getLinearVel() * 0.2f + glm::normalize(float3(1, 16, -3) - t) * 10.f);

				po->timer = glm::clamp(po->timer + 1, 1.f, 3.f);
			}

		}
		else {

			rb->setLinearVel({ 0,0,0 });
		}
	}
}

void irr::scene::IrrSaba::onMotionFinish(saba::MMDNode& node)
{
#if MMD_SAVE_VMD_MOTION

	if (vmdWriteFile->recordingAllNode &&
		//(node.exd.flag & mnfLegD)
		node.IsIK()
		)
		return;
	float ff = animeTime * 30.f;
	float dd = ff - int(ff);

	if (dd > 0.1f && dd < 0.9f) {
		//DPWCS((L"save+ %6.2f %.2f %s", ff, dd, node.GetNameU().c_str()));
		//return;
	}
	auto ndGlbGlmM = node.GetGlobalTransform();
	auto parent = node.GetParent();


	core::matrix4& ms = *(core::matrix4*)&ndGlbGlmM;
	auto pos = ms.getTranslation();
	glm::quat qt(ndGlbGlmM);

	if (parent) {
		core::matrix4& msp = *(core::matrix4*)&(parent->GetGlobalTransform());
		core::matrix4 mspi;
		if (msp.getInverse(mspi)) {
			core::matrix4 m = mspi * ms;
			pos = m.getTranslation();
			qt = glm::quat(*(glm::mat4*)&m);
		}
	}

	pos -= node.GetTranslate();
	//MMDFWD(2, "sw", vec3(ndGlbGlmM[3]), vec3(0, 0, 0), SColorf(1,1, 0, 1));

	vmd::VmdBoneFrame bf{ "",0 };

	bf.frame = ff + 0.5;
	std::string s;
	ecv.Utf16ToCp932(node.GetNameU().c_str(), node.GetNameU().length(), &s);
	bf.name = s;

	bf.position[0] = pos.X;
	bf.position[1] = pos.Y;
	bf.position[2] = pos.Z;

	bf.orientation[0] = qt.x;
	bf.orientation[1] = qt.y;
	bf.orientation[2] = qt.z;
	bf.orientation[3] = qt.w;

	//DP(("P %d %f Q %f", int(animeTime * 30.f+0.5f),glm::length(node.exd.lsPos - glm::vec3(pos)), glm::length(node.exd.lsRtt - qt)));
	if ((glm::length(node.exd.lsPos - glm::vec3(pos)) > 0.001f || glm::length(node.exd.lsRtt - qt) > node.exd.motionRecRttDis)
		&& bf.frame != node.exd.lsFrame
		) {
		DPWCS((L"save- %d = %6.2f %s  r=%f,%f", int(animeTime * 30.f + 0.5f), ff, node.GetNameU().c_str(), bf.orientation[0], bf.orientation[1]));
		vmdWriteFile->bone_frames.push_back(bf);
		node.exd.lsPos = pos; node.exd.lsRtt = qt; node.exd.lsFrame = bf.frame;
	}
#endif
}
void irr::scene::IrrSaba::setAdAnim(int id, float dur, float delay, float anmOfs)
{
	if (id < 0) {
		mModel->addAnimOn = false;
		return;
	}
	maxrhanim = mModel->vmdAddAnims.size();
	if (id == 0x10000)	id = adAnmId;
	else if (id == 0x10001) id = (adAnmId + 1) % maxrhanim;
	mModel->addAnimOn = true;

	if (maxrhanim)
	{
		adAnmIdLast = adAnmId;
		adAnmId = id % mModel->vmdAddAnims.size();
#if MMD_ACTION_ANIM
		mModel->vmdAction = 1;
		mModel->vas[1] = mModel->vas[0];
		mModel->vas[0].start = gSceneTime + delay;
		mModel->vas[0].offset = anmOfs;
		mModel->curVA = &mModel->vmdAddAnims[adAnmId];
#else
		handUpRatR = 0; adAniTime = adAniDur = dur;
#endif
	}
}
int irr::scene::IrrSaba::setRndAdAnim(float& TdelayS, u32 group)
{
	if (mModel->vmdAddAnims.size() == 0) return 0;
	u32 size = mModel->vaIds[group].size();
	if (size == 0)
	{
		if (mModel->vaIds[group + 1].size() == 0)
			return 0;
		group = group + 1;
		size = mModel->vaIds[group].size();
	}
	int r = UaRand(size);
	int id = mModel->vaIds[group][r];
	float t = mModel->vmdAddAnims[id].vi.actT;

	if (TdelayS > t) {

		setAdAnim(id, 1, TdelayS - t);
	}
	else {
		setAdAnim(id, 1);
		TdelayS = t;
	}

	return id;
}
void irr::scene::IrrSaba::onMorph(int reason, saba::MMDMorph& morph)
{
	const float zyIntvTime = 3.f, zyDur = MMD_ZY_DUR;

	nearKiss = canLookAtCam;
	if (morph.exd.eId == 1)
	{	//DP(("眨眼" ));

		if (zyTimer < 0.f) {
			zyTimer = 6.f + ualib::UaRandm1to1(); zyPast = 0;
		}
		if (nearKiss && headDis < kissDis)
			morph.SetWeight(core::clamp((kissDis - headDis) / 2.f, 0.f, 0.f));
#if MORPH_EYE_TIMELY
		if (!Vmd || Vmd->eyeBlinks < 5)
			morph.SetWeight(zyTimer < zyDur ? sin(zyTimer / zyDur * core::PI) : 0);
#endif
		//else			morph.SetWeight(0);
	}
#if 0
	if (nearKiss && morph.exd.eId == 23)//mouth u
	{
		if (headDis < kissDis * 1.5f) {
			float w = core::clamp((kissDis - headDis) / 2.f, 0.f, 1.f);
			morph.SetWeight(w);
			if (w > 0.1f) {
				float3 tcl = Ctx->gd.CamNormal->getAbsolutePosition() + vector3df(ualib::UaRandm1to1(), ualib::UaRandm1to1(), ualib::UaRandm1to1()) * 100;
				core::matrix4 ndm; ndm = ndHead->GetGlobalTransform();
				ndm = mmdBaseMat * ndm;
				if (60.f > headVector.angleBetweenInDegree(-SceneManager->getActiveCamera()->getFrontVector()))
					Eqv->LaunchFw3D(ndm.getTranslation(), Eqv->getFwIdxByFwIdStr(1, "kissFW"), headVector * 1000, { 1,1,1,1 }, 0, &tcl);
			}
		}
	}
	else if (morph.exd.eId == 100 && nearKiss) {
		laughMorph = core::clamp((lookDis - headDis) / std::min(5.f, float(lookDis - kissDis)), 0.f, 1.f);
		if (headDis < kissDis)			morph.SetWeight(1 - core::clamp((kissDis - headDis) / 2.f, 0.f, 1.f));
		else			morph.SetWeight(laughMorph);
	}
	else if (morph.exd.eId == 201)		morph.SetWeight(catEarMorph);
	else if (morph.exd.eId == 202)		morph.SetWeight(animeTime > 3 ? core::clamp((lookDis - headDis) / std::min(5.f, float(lookDis - kissDis)), 0.f, 1.f) : 0);
#endif
#if SHOW_INNER
	else if (morph.exd.eId == 111 || (morph.exd.eId == 112))		morph.SetWeight(1);
#endif
	else if (morph.exd.eId == 900) {
		if (morph.GetWeight() > 0.5f) {
			charAtk.lauchAllVtxFw();
		}
	}
	else if (morph.exd.eId == 999)		morph.SetWeight(1);
	else if (morph.exd.eId >= 11 && morph.exd.eId <= 16)
	{
		if (MMD_MIDI && MMD_MOUTH_A_ON_RATIO && morph.exd.eId == 11)
			morph.SetWeight(aniRateR);
#if MMD_LOCK_ROOT_FRAME_ID>=0
		if (morph.exd.eId == 11 && Eqv->pvp->working && animeTime >= MMD_LOCK_ROOT_FRAME_ID / 30) {
			float f = std::min(1.f, glm::length(ndYao->exd.aniSpeed) / 200);
			morph.SetWeight(f);
			//DP(("mouth a %f", f));
		}
#endif
#if MMD_MOUTH_FROM_LYRIC
		int mpSid = morph.exd.eId - 10;
		if (mpSid == nextSid)
			morph.SetWeight(sidRatio);
		else if (mpSid == curSid)
			morph.SetWeight(1.f - sidRatio);
		else morph.SetWeight(0.f);
#if MMD_SAVE_VMD_MORPH
		if (Eqv->pvp->mediaTime > 0.000001f) {
			bool hasSid = false;
			float rt = 0;
			if (mpSid == nextSid) { rt = sidRatio; hasSid = true; }
			else if (mpSid == curSid) { rt = 1 - sidRatio; hasSid = true; }
			//else   { rt = 0; hasSid = true; }
			//if (morph.GetWeight() > 0.f || hasSid  )
			{
				vmd::VmdFaceFrame ff;
				std::string s;
				static saba::MMDMorph* mfs[] = { mpA, mpI, mpU, mpE, mpO };
				static float lastw[16] = { 0 };
				int id = mpSid - 1;
				auto ws = mfs[id]->GetNameW();
				float weight = core::clamp(morph.GetWeight(), 0.f, 1.f);
				ecv.Utf16ToCp932(ws.c_str(), ws.length(), &s);
				ff.face_name = s;
				if (lastw[id] < 0.0000001f && weight >= 0.0000001f)
				{
					ff.frame = animeTime * 30.0f - 1;
					ff.weight = 0;
					vmdWriteFile->face_frames.push_back(ff);
				}

				ff.frame = animeTime * 30.0f;;
				ff.weight = weight;
				if (ff.weight != lastw[id])
					vmdWriteFile->face_frames.push_back(ff);
				lastw[id] = ff.weight;
			}
		}
#endif

#elif SVG_MMD_WRITE_WITH_MOUTH
		morph.SetWeight(drawRat);
#elif MOUTH_MORPH_MUL100

		//to fix added lyric morph lost morph.SetWeight(std::clamp(morph.GetWeight(),0.f,1.f) * (MOUTH_MORPH_MUL100 / 100.f));
#endif

	}
#if MORPTH_XIAO_CONTROL
	else if (morph.exd.eId == 21)
	{
		morph.SetWeight(std::min(1 - mpBlink->GetWeight(), xiaoRat));
	}
#endif
	else if (&morph == mpStarEye) {


#if WRITE_FRAME_TXT
		if (mpStarEye->m_weight <= 0.1000) {
			//DP(("StEye %f", mpStarEye->m_weight));
			float p1 = mpStarEye->m_weight * 10;
			writeFrameTxtStr = strFmt(
				"(shame:%.2f),(open_mouth:%.2f),(closed_eyes:%.2f),(large_breasts:%.2f),(steam_from_mouth,tears:%.2f) ,(saliva:%.2f)  ",
				p1 * 1.2f + 0.5f, p1 * 1.f, p1 * 0.9f, p1 * 1.f, p1 * 1.33f, p1 * 1.f);
		}
#endif
	}

	if (morph.exd.eId > 0 && morph.exd.eId < 999) {

		morph.aaUpdate(Ctx->gd.deltaTime);
	}
}

void irr::scene::IrrSaba::freeRes()
{
	//video::IVideoDriver* driver = SceneManager->getVideoDriver();
	//for (auto& m : mtrs)
	//{
	//	driver->removeTexture(m.getTexture(0));
	//	driver->removeTexture(m.getTexture(1));
	//}

	for (auto& cp : cbcps)
	{
		auto mr = (video::VkMaterialRenderer_MMD*)Driver->getMaterialRenderer(EMT_MMD);
		mr->delDS(cp.ds);
	}
	cbcps.clear();

	//mtrs.clear();
}



void irr::scene::IrrSaba::drawReset()
{
	SvgDrawData::drawReset();
	if (svgMan && svgMan->isWriteOnCloth && sbCloth) {
		sbCloth->clearVtxPts();
	}

	lastWriteRbPos = float3(0);
	ndHandR->rb0->setCollideFilterMask(1, 0);
	pinWriteNodes();

	stkRbReset = true;

	PMXJoint jt{};
	jt.limitMinT = vec3(-1);
	jt.limitMaxT = vec3(1);
	jt.setLocalPos = true;
	jt.springT = vec3(20000.f);
	jt.springR = vec3(100.f);
	jt.dampingT = vec3(100.f);
	jt.dampingR = vec3(100.f);
	if (ndPinHandR && !jtWrite) jtWrite = Pmx->connectRb(ndPinHandR->rb0, ndHandR->rb0, false, false, jt);
}

void irr::scene::IrrSaba::drawEnd()
{
	if (jtWrite) delete jtWrite; jtWrite = nullptr;
	isDrawStartFootSet = false;
	ndHandR->rb0->setCollideFilterMask(0, 0);
}

void irr::scene::IrrSaba::onSvFwCenterPt(const EQVisual::SvTimePt& pt, int fromId, float deltaS)
{
	if (!isAiCharacter()) return;
	if (SVG_MMD_WRITE && sbCloth && sbCloth->snCloth) 	svgMan->isWriteOnCloth = 1;

#if USE_SVG || MMD_PLAY_GUQIN
	drawFinished = (pt.spFlag & 2);
	if ((fromId == 1) && pt.spFlag & 0x20) {
		Drawing = pt.spFlag != 0xF0000000;


		drawStroking3 = drawStroking2; drawStroking2 = drawStroking1; drawStroking1 = drawStroking;
		drawStroking = !(pt.spFlag & 1);
		drawCtrPos = pt.ctr;

		if (drawStroking1 != drawStroking) //stroke start or end
		{
			drawPtId = 0; //stk or move begin
		}
		else drawPtId++;
	}
	if ((fromId == 3))
	{
		drawPosAdd = (drawStroking) ? pt.pos + vector3df(0, 2, 0) : core::vector3df(0, 0, 0);
		//lastEmbPos = pt.pos*3;
		return;
	}

	evtMoving[pt.ik] = false;

	if (fromId == 5
		//&& MmdMode==11
		)
	{
		evtMoving[pt.ik] = true;
		drawPos = pt.pos;

		mModel->addAnimOn = true;
	}

#if SVG_MMD_WRITE
	else  if (Drawing) {

		if (!dsLast.drawing) {
			Ctx->gd.CamNormal->interpolateToCam(nullptr, 0.f);
			setWriting(true);
		}

		if (camSave) svgCam = camSave;
		//svgMan = Eqv->svgMan;

		float mx = SVG_MUL_BASE / 10;
		//itemDrawOfs = Pmx->rt1Tr+vec3(0,0,2);
		auto tr = drawUpdate(pt, deltaS * curSpeedMul, mx, true);



		float fwRadius = std::max(svgPm.minPtR, mx / SVG_MUL_BASE * (drawRat));
		posIKArmR = msR.pos;
#if DBG_STROKE  //Debug
		vector3df p = tr; mmdToIrrPos(p);
		Eqv->LaunchFw3D(p, Eqv->getFwIdx(2, 3), { 0,0,0 }, SColor(pt.stroking ? 0xFFFFFFFF : 0xFF808080));
		p = msR.pos; mmdToIrrPos(p);
		Eqv->LaunchFw3D(p, Eqv->getFwIdx(2, 3), { 0,0,0 }, SColor(pt.stroking ? 0xFF00FF00 : 0xFFFF0000));
#endif
		if (SVG_MMD_WRITE_SAVE_VMD) {
			if (!ndOpCtr) ndOpCtr = Pmx->GetNodeManager()->FindNode(L"操作中心");
			if (ndOpCtr) {
				matrix4 m; m.setTranslation(msR.pos); m.setRotationDegrees({ drawRat * 180 / core::PI / core::PI, 0, 0 });
				ndOpCtr->SetGlobalTransform(m);
				onMotionFinish(*ndOpCtr);
			}
		}
		writeWithLeg();
		bool useDrawSrc = false;
		vector3df drawSrc;
#if 0
		const WriteDrawData* a{}, * b{}; float ratio = 0;
		svgMan->ddr.getDataPtrUs((pt.timeS) * TIMEUSINS, a, b, ratio);
		vector3df tp = a->pos; mmdToIrrPos(tp); lastWritePos = tp;
		dsLast = { Drawing, drawRat, drawPos, msR.pos,msR.spd };
		svgMan->drawFw(this, a->size, tp, useDrawSrc ? &drawSrc : nullptr);
		mmdRhSpeed = tp;
#else
		bool ratCanDraw = (drawRat > svgPm.minDrawRat);
		bool drawn = false;
		vector3df tp = msR.pos; mmdToIrrPos(tp);
		vec3 actualPos = msR.pos;
		lookTgtMmdPos = tp;		matAbsInv.transformVect(lookTgtMmdPos); lookTgtMmdPos += vector3df{ 0,8, -100 };

		dsLast.drawing = Drawing; dsLast.drawRat = drawRat; dsLast.drawPos = drawPos; dsLast.mmdPos = msR.pos; dsLast.vel = msR.spd;
		int mid = 0;
		if (svgMan->isWriteOnCloth && sbCloth) {
			auto& vms = Eqv->GetPPT()->vtxMats;
			if (vms.usedSize() < FW_VTX_MAT_MAX - 1) {
				mid = vms.alloc({});
			}
			SnPhyCloth::ClothPt pt; pt.mid = mid;	pt.p3d.set(drawPos);
			sbCloth->snCloth->pts.emplace_back(pt);
			tp.set(0, 0, 1);
			//MMDFWD(2, "swLong",tr, vec3(0,0,0), SColorf(0.5, 1.0, 0.1, 1));

		}
		else  if (SVG_MMD_RB)
		{

			if (stkRbReset)
			{
				stkRbReset = false;
				curStkRb = nullptr; curStkJt0 = curLinkJt = nullptr;  curStkStokeId = -1; curStkRbId = 0;
				sls[curStkLine].init_reset();
				//curStkHue += 30;
				//curStkHue=itemIdx*30;
				//fwStrokeIdx = itemIdx;
			}
			const StrokeFwInfo& sfi = Eqv->getFwStk(fwStrokeIdx);
			auto& lineRbs = sls[curStkLine].curLineRbs;
			float rbD = std::max(0.0f, fwRadius * 0.2f * (svgPm.mmdPos == 1 ? 2 : 1)) * sfi.rbRadius;
			float linkLimit = rbD * sfi.linkRMul;
			auto mp = tp; mp.y = std::max(rbD / 2.f, mp.y);

			this->matAbsInv.transformVect(mp);
			auto& vms = Eqv->GetPPT()->vtxMats;
			fwRadius = rbD;

			auto baseRb = SVG_MMD_RB_ON_MMDBASE ? ndSubRbBase->rb0 : Pom->groundRb;//// ndUpper2->rb0; //

			if ((mp - lastWriteRbPos).getLength() > rbD / 2.f && pt.strokeIdx >= 0 && pt.stroking && drawRat > 0.5f && ratCanDraw) //add rb
			{
				int stkHead = 0; 		curStkHue += 0.5;
				if (curStkStokeId != pt.strokeIdx)
				{
					//rmul = 2;
					if (curStkJt0) {
						stkHead = 2; curStkRb->stkFlag |= 2;// curStkJt->setDrive(1000, 10); curStkRb->setScale(vec3(2.f));
						curStkRbId = 0;
						curStkRb = nullptr;
					}
					else stkHead = 1;

					{
						int cid = svgMan->strokes[pt.strokeIdx].charIdx;
						if (cid < svgMan->chars.size()) {
							auto chr = svgMan->chars[cid];
							std::wstring s; s += chr.ch;
							actVoiceFx(8 + itemIdx, 5, 60, s);
							//Pm.mmd->mdplr.aouStart(itemIdx,  10, 0, false,,true);
						}
					}
				}
				else {
					//if (curStkRbId > 1 && curStkJt0) { curStkJt0->destroy(); std::erase_if(sls[curStkLine].jts[0], [=](const auto& j) { return j == curStkJt0; }); }

				}

				if (vms.usedSize() < FW_VTX_MAT_MAX - 1)	curStkMid = vms.alloc({});
				PhyObjParam pm{ 1 ,sfi.density * pow(rbD,3),vec3(rbD),{0,-0,0},{},{0,0,0},false,false,false };
				pm.operation = 1;  pm.kinematic = 0, pm.ownerAdd = itemIdx; pm.pos = mp;
				pm.pmxrb.m_repulsion = 0.5f;
				pm.pmxrb.m_friction = 10000.f;
				pm.pmxrb.m_collideMask32 = SVG_MMD_RB_HAS_SUBMMD ? 0 : 0xFFFE;
				pm.tag = 'tspt';
				pm.timer = 999;
				pm.vtxMatId = curStkMid;	pm.vtxMatUseSizeScale = 1;
				pm.ownerAdd = 100;
				//pm.rtt = vector3df(mp- lastWriteRbPos).getHorizontalAngle()*DEGTORAD;
				auto obj = Pom->addObj(pm);

				auto rb = obj->rb;
				if (SVG_MMD_RB_HAS_SUBMMD && cbOnAddStkRb) cbOnAddStkRb(obj);

				//saba::PMXFileCreateParam fcp{ "D:/MMD/PMX/JQL/dog/watchN.pmx" }; fcp.massMul = 1.f;
				//auto sbw = loadSabaModelScale(fcp, 1.f, false, 0x101)->sb;

				auto tsf = baseRb->GetTransform() * glm::inverse(baseRb->initTransform) * rb->initTransform;
				rb->svgStrokeIdx = pt.strokeIdx;
				rb->svgRbIdx = lineRbs.size();
				rb->svgChIdx = pt.chIdx;
				rb->SetCoMTransform(tsf);   actualPos = tsf[3];
				//MMDFW(2, "sw1s" , actualPos, vec3(0, 0, 0), SColorf(1, 0, 0, 1));

				PMXJoint jt{}; jt.translate = mp;
				jt.limitMinT = vec3((stkHead ? -rbD : -rbD) * sfi.limitT[0]);
				jt.limitMaxT = vec3((stkHead ? rbD : rbD) * sfi.limitT[0]);

				jt.useInitRbPos = true;
				jt.springT = vec3(sfi.springT[0]);
				jt.springR = vec3(sfi.springR[0]);//free
				jt.dampingT = vec3(sfi.dampingT[0]);
				curStkJt0 = Pmx->connectRb(baseRb, rb, false, false, jt);
				sls[curStkLine].jts[0].push_back(curStkJt0);

				if (curStkRb) {
					int grp = stkHead != 0 ? 2 : 1;
					jt.limitMinT = vec3(-rbD * sfi.limitT[grp]);
					jt.limitMaxT = vec3(rbD * sfi.limitT[grp]);
					jt.springT = vec3(sfi.springT[grp]);
					auto j = Pmx->connectRb(curStkRb, rb, false, true, jt);
					sls[curStkLine].jts[grp].push_back(j);
				}

				jt.limitMinT = vec3(-rbD * sfi.limitT[2]);
				jt.limitMaxT = vec3(rbD * sfi.limitT[2]);
				jt.springT = vec3(sfi.springT[2]);

				{  // cur to closest head
					float minDis2 = 9999999.f; int minI = -1;
					for (int i = 0; i < lineRbs.size(); i++) {
						auto ri = lineRbs[i];
						if (((ri->stkFlag & 0x3) || ri->svgChIdx == rb->svgChIdx) && (ri->svgStrokeIdx != rb->svgStrokeIdx || rb->svgRbIdx - ri->svgRbIdx > 6)) {
							float idis2 = (vector3df(ri->initPos) - mp).getLengthSQ();
							if (minDis2 > idis2)
							{
								minI = i; minDis2 = idis2;
							}
						}
					}
					if (minI >= 0) {
						float dis = (vector3df(lineRbs[minI]->initPos) - mp).getLength();
						auto& ri = lineRbs[minI];
						bool close = dis < rbD;
						if ((dis < linkLimit) && ri->svgChIdx != rb->svgChIdx || close && ri->svgChIdx == rb->svgChIdx || stkHead)
						{
							bool link = true;
							if (ri->svgJt)
								if (ri->svgJtDis > dis)
								{
									ri->svgJt->destroy(); DP(("Link--- %d", ri->svgRbIdx));
									std::erase_if(sls[curStkLine].jts[2], [ri](const auto& j) { return j == ri->svgJt; });
									if (curLinkJt == ri->svgJt) curLinkJt = nullptr;
								}
								else link = false;
							if (link && (!curLinkJt || curLinkJt->getRb(1)->disTo(mp) > rbD * 1.5f)) {
								curLinkJt = ri->svgJt = Pmx->connectRb(ri, rb, close, false, jt);
								sls[curStkLine].jts[2].push_back(curLinkJt);
								ri->svgJtDis = dis;
								DP(("Link+++ %d,%d", ri->svgRbIdx, rb->svgRbIdx));
								//MMDFW(2, close ? "sw25s" : "sw5s", mp, vec3(0, 0, 0), SColorf(0, 1, 0, 1)); MMDFW(2, close ? "sw25s" : "sw5s", lineRbs[minI]->initPos, vec3(0, 0, 0), SColorf(1, 0, 0, 1));
							}
						}

					}
				}
				lastWriteRbPos = mp;
				curStkRb = rb;
				curStkStokeId = pt.strokeIdx;

				curStkRbId++; //
				sls[curStkLine].kdTree->addPoint(mp, lineRbs.size());
				sls[curStkLine].objs.push_back(obj); lineRbs.push_back(rb);
				DP(("STKRB %3d  fwRadius=%8.2f  rbR=%8.2f size.x=%8.2f", lineRbs.size(), fwRadius, rbD, pm.size.x));

			}
			if (curStkRb && curStkMid > 0) {
				tp = glh::matTransformVec(mmdBaseMat * glm::inverse(curStkRb->initTransform), msR.pos);
				mid = curStkMid;
			}

		}
		else {
			DP(("lost stk"));
		}
#if 1

		if (ndPinHandR)
		{
			ndYao->rb0->setAngVelToPos(actualPos, 200);
			ndUpper2->rb0->setAngVelToPos(actualPos, 100);
			ndHead->rb0->setAngVelToPos(actualPos, 100);
			//MMDFW(2, "sw", actualPos, vec3(0), SColorf(0, 1, 0, 1));
			ndPinHandR->SetAnimationTranslate(glm::mix(ndPinHandR->GetAnimationTranslate(), vec3(actualPos), 0.1f));
			int ptStep = 100;
			auto p2 = svgMan->getOfsPt(-1);
			if (p2 && p2->strokeIdx > 0) {
				vector3df tr2; auto& stk = svgMan->strokes[p2->strokeIdx];
				mx = SVG_MUL_BASE / 10;
				auto t = drawPos;
				getMmdPos(tr2, true, mx, stk.movDis, *p2);
				drawPos = t;
				//ndPinHandR->SetAnimationTranslate(glm::mix(ndPinHandR->GetAnimationTranslate(),vec3(tr2),0.1f));
				//ndFootL->rb0->addLinearVelToPos(tr2, drawStroking ? 3 : 0);
			}
			p2 = svgMan->getOfsPt(-200);
			if (p2 && p2->strokeIdx > 0) {
				vector3df tr2; auto& stk = svgMan->strokes[p2->strokeIdx];
				mx = SVG_MUL_BASE / 10;
				auto t = drawPos;
				getMmdPos(tr2, true, mx, stk.movDis, *p2);
				drawPos = t; float a = gPhyTime * core::PI * 2;
				ndHandL->rb0->addLinearVelToPos(tr2, drawStroking ? std::max(0.f, sin(a)) * 2 : 1);
				//ndFootL->rb0->addLinearVelToPos(tr2, drawStroking ? std::max(0.f, -sin(a)) * 2 : 1);
			}
		}

#endif
		if (!drawn && ratCanDraw) {
			svgMan->drawFw(this, pt, fwRadius, tp, useDrawSrc ? &drawSrc : nullptr, mid);
			lastWritePos = tp;
		}
		mmdRhSpeed = tp;

#endif

		//drQue.push(dsLast);
	}
	else {
		if (ndIKArmR && ndIkFingerR && dsLast.drawing) //stop drawing
		{
			Ctx->gd.CamNormal->interpolateToCam(Ctx->gd.CamRtt, 3.f);
			setWriting(false);
			ndIKArmR->exd.ms.pos = ndIkFingerR->exd.ms.pos;
			//ndIKArmR->setCmPosGlobal(ndIkFingerR->exd.ms.pos, 0.02f, 0.02f);
		}
		svgMan->useMat = drawStroking = false; //evtMoving = true;curSTP.pos.set(0, 30, 0);
		drawStrokeRatio = 0;
		dsLast = { Drawing }; drQue.push(dsLast);
	}
#endif
#endif
}

void irr::scene::IrrSaba::stkLineAdd(std::wstring txt)
{
	svgMan->fixLineNum = stkLineNum;
	stkLineDel((curStkLine + 1 + stkLineNum) % stkLineNum);
	curStkLine = (curStkLine + 1) % stkLineNum;
	svgMan->atLine = curStkLine;
	svgMan->GenSvTxtFw(txt);
}

void irr::scene::IrrSaba::stkLineDel(int i)
{
	auto& line = sls[i];

	line.init_reset();
}

void irr::scene::IrrSaba::writeWithLeg()
{
	{
#if SVG_MMD_WRITE_USING_LEG
		vector3df ctrPos = msR.pos;
		if (ndFootL->rb0) {
			vector3df vel = msR.spd;
			vector3df rtt = vel.getHorizontalAngle();
			auto nd = ndFootL, nd1 = ndFootR;
			float3 posL = ndFootL->GetGlobalTransform()[3];
			float3 posR = ndFootR->GetGlobalTransform()[3];
			const WriteDrawData* a{}, * b{}; float ratio = 0;
			if (glm::length(posR - msR.pos) < glm::length(posL - msR.pos)) { nd = ndFootR, nd1 = ndFootL; }
			int id = svgMan->ddr.getDataPtrUs((pt.timeS) * TIMEUSINS, a, b, ratio);
			matrix4 mp; mp.setTranslation(msR.pos); mp.setRotationDegrees(-rtt);
			btTransform transform; transform.setFromOpenGLMatrix(&mp[0]);
			vector3df dir = msR.pos - float3(nd->rb0->GetTransform()[3]);
			if (drawStroking) {
				nd->rb0->addForce(dir.normalize() * std::max(50000.f, 3000 * dir.getLength()));
				useDrawSrc = true;
				drawSrc = nd->getGlobalPos();
				mmdToIrrPos(drawSrc);
			}
			else {
				int id = pt.chPtIdx;
				while (id < svgMan->ddr.getCount())
				{
					auto& d = svgMan->ddr.getDataPtr(id)->d;
					if (d.stroking)
						break;
					id++;
				}
				if (id < svgMan->ddr.getCount()) {
					auto d = svgMan->ddr.getDataPtr(id)->d;
					auto dir = d.pos - float3(nd->rb0->GetTransform()[3]);
					nd->rb0->addForce(dir.normalize() * 20000.f);
				}
			}
			//nd->rb0->GetRigidBody()->setCenterOfMassTransform(transform);

			if (id >= 0) {
				while (id < svgMan->ddr.getCount())
				{
					auto& d = svgMan->ddr.getDataPtr(id)->d;
					if (d.sid > pt.strokeIdx && d.stroking)
						break;
					id++;
				}
				if (id < svgMan->ddr.getCount()) {
					auto d = svgMan->ddr.getDataPtr(id)->d;
					vector3df rtt = d.vel.getHorizontalAngle();
					vector3df tp = d.pos;  tp.Y += 6;  mmdToIrrPos(tp);
					Eqv->LaunchFw3D(tp, Eqv->getFwIdx(2, 1), { 0,0,0 }, SColor(0xFF00FF00));
					matrix4 mp; mp.setTranslation(d.pos); mp.setRotationDegrees(-rtt);
					btTransform transform; transform.setFromOpenGLMatrix(&mp[0]);
					auto dir = d.pos - float3(nd1->rb0->GetTransform()[3]);
					if (drawStroking)		nd1->rb0->addForce(dir.normalize() * 2000.f);
					else if (drawPtId == 0)	nd1->rb0->applyCentralImpulse({ 0,10000,0 });//			nd1->rb0->addForce(tdir.normalize()*10000.f* core::clamp(1- (svgMan->ddr.getDataPtr(id)->ts / 1000000.f - pt.timeS),0.f,1.f));
					lookTgtMmdPos = d.pos; lookTgtMmdPos.Y += 16;
					ctrPos = (ctrPos + d.pos) / 2;
				}
			}
			if (drawStroking && 0)
			{
				ctrPos.Y = 13.5;
				ctrMph.interpolate(ctrPos, ctrMph, 0.02);
				if (ndPinYao) ndPinYao->SetAnimationTranslate(ctrMph);
			}
			else if (ndPinYao)
			{
				id = svgMan->ddr.getDataPtrUs((pt.timeS + 0.35) * TIMEUSINS, a, b, ratio);
				if (id >= 0) {
					float ly = std::min(nd->getGlobalPos().y, nd1->getGlobalPos().y);
					float yh = ndPinYao->pmxBone->m_position.y + 6;
					vector3df vYao(0, std::max(yh - 6, yh - ly), 0), vHandL(-3, -6, 0);
					//auto mr = svgCam->getAbsoluteTransformation();						mr.rotateVect(vYao); mr.rotateVect(vHandL);
					auto pos = b->pos.getInterpolated(a->pos, ratio);
					DP(("pos %f, %f, %f", pos.X, pos.Y, pos.Z));
					auto tgt = vYao + pos;
					ctrMph.interpolate(tgt, ctrMph, 0.006);
					ctrMph.X = glm::mix(ctrMph.X, tgt.X, 0.006);
					ctrMph.Y = glm::mix(ctrMph.Y, tgt.Y, 0.012);
					ctrMph.Z = glm::mix(ctrMph.Z, tgt.Z, 0.006);

					if (ndPinYao) ndPinYao->SetAnimationTranslate(ctrMph);
					//nd1->SetAnimationTranslate(phL+vHandL);
					//nd1->rb0->SetActivation(true);

					auto ndRoot = Pmx->GetNodeManager()->GetMMDNode(Pmx->rootRbBoneIdx);

					dir = (pos - ndRoot->getGlobalPos()).getHorizontalAngle();
					dir.X = -30; dir.Z = 0; dir.Y += 180;
					matrix4 m; m.setRotationDegrees(dir);
					glm::mat4 mCamWrite = m;

					ndRoot->rb0->applyTorqueRotateTo(mCamWrite, 3);
				}
			}
		}
#else
		auto& sb = *this;
		if (sb.ndPinFootL && sb.ndPinFootL->rb0) {
			auto nd = sb.ndPinFootL, nd1 = sb.ndPinFootR;
			if (sb.curNdId == 1) {
				//nd->rb0->SetActivation(false);
				nd = sb.ndPinFootR, nd1 = sb.ndPinFootL;
			}
			if (nd) nd->SetAnimationTranslate(msR.pos);
			vector3df vYao(0, 12, 0), vHandL(-3, -6, 0);
			//mr.rotateVect(vYao); mr.rotateVect(vHandL);
			ctrMph.interpolate(vYao + msR.pos, ctrMph, 0.03);

			if (sb.ndPinYao) sb.ndPinYao->SetAnimationTranslate(ctrMph);
			//nd1->SetAnimationTranslate(phL+vHandL);
			//nd1->rb0->SetActivation(true);

			vector3df vel = dsLast.vel;
			vector3df rtt = vel.getHorizontalAngle();
			matrix4 m; m.setRotationDegrees(-rtt);
			glm::mat4 mCamWrite = m;
			auto ndRoot = sb.Pmx->GetNodeManager()->GetMMDNode(sb.Pmx->rootRbBoneIdx);
			ndRoot->rb0->applyTorqueRotateTo(mCamWrite, 10);
			sb.ndUpper2->rb0->addForce(float3(0, 100000, 0));
		}
#endif
	}
}

void irr::scene::IrrSaba::stkDrawFirstPt()
{
	if (!isAiCharacter()) return;
	drawStartPos = msR.pos;
	vector3df v = msR.pos; v.Y = 0;
	moveFoot = v.getLength() > 15;
#if MMD_CENTER_CTRL
	if (!isDrawStartFootSet)
	{
		isDrawStartFootSet = true;
		drStFootL = ndFootIKL->GetAnimationTranslate();
		drStFootR = ndFootIKR->GetAnimationTranslate();

	}
#endif
}

void setBonePhsActiveIntl(PMXModel* pmx, MMDNode* node, bool active) {
	if (!node) return;
	//old MMDRigidBody* rb = node->rb0;if (rb && rb->dynRbType == 1) rb->SetActivation(active);
	for (auto& rb : *pmx->GetPhysicsManager()->GetRigidBodys())
	{
		if (rb->dynRbType == 1 && rb->node == node) {
			rb->SetActivation(active);
			//if (active)				rb->mmdNode->EnableIK(false);
		}
	}
};


void cycSubNode(PMXModel* pmx, saba::MMDNode* node, std::function<void(saba::MMDNode* nd)> cb) {

	while (node) {
		cb(node);
		cycSubNode(pmx, node->GetChild(), cb);
		node = node->GetNext();
	}
};
void irr::scene::IrrSaba::setBonePhsActive(saba::MMDNode* node, bool active, bool setSubNodes)
{
	if (!node)
		return;
	setBonePhsActiveIntl(Pmx, node, active);
	if (setSubNodes) cycSubNode(Pmx, node->GetChild(), [this, active](saba::MMDNode* nd) {
		setBonePhsActiveIntl(Pmx, nd, active);
		});
}
void irr::scene::IrrSaba::setPhyAnim(saba::MMDNode* node, int phAni, bool setSubNodes, int setForcePhy)
{
	if (!node) node = ndRbRoot;;
	if (setForcePhy >= 0) {
		forcePhyAnim = setForcePhy;
	}
	if (node->rb0 && node->rb0->dynRbType) node->phyAnim = phAni;
	if (setSubNodes) cycSubNode(Pmx, node->GetChild(), [this, phAni](saba::MMDNode* nd) {
		if (nd->rb0 && nd->rb0->dynRbType) nd->phyAnim = phAni;
		});
}
void irr::scene::IrrSaba::setSubNodePhyAnim(saba::MMDNode* node, int phAni)
{
	if (!node) node = ndRbRoot;

	cycSubNode(Pmx, node->GetChild(), [this, phAni](saba::MMDNode* nd) {
		if (nd->rb0 && nd->rb0->dynRbType) nd->phyAnim = phAni;
		});
}


void irr::scene::IrrSaba::setSpeechId(int sid, float dur, float startRatio)
{
	nextSid = sid;
	sidTimerMax = SID_TIMER_S;
	sidTimer = (1.f - startRatio) * sidTimerMax;
	sidTimerDurMax = sidTimerDur = dur;
}

bool irr::scene::IrrSaba::attachToParentNode(const wchar_t* name)
{
	if (name) {
		parentMMD = dynamic_cast<IrrSaba*>(getParent());
		if (!parentMMD) return false;
		setScale(1.f);
		parentAttechNode = parentMMD->mNodeMan->GetMMDNode(ualib::WcstoUtf8(name));
		Pmx->rootHasParent = true;
		return parentAttechNode != nullptr;
	}

	parentMMD = nullptr;
	parentAttechNode = nullptr;
	return false;
}

bool irr::scene::IrrSaba::connectPhyNodeToParent(const saba::MMDNode* node, const saba::MMDNode* ndParent, float3 ofs)
{
	if (ndParent) {
		//parentMMD = dynamic_cast<IrrSaba*>(ndParent->model->saba);
		//if (!parentMMD) return false;
		PMXJoint jt{};
		jt.translate = ofs;
		jt.limitMinT = vec3(0);
		jt.limitMaxT = vec3(0);
		jt.setLocalPos = true;
		jt.springT = vec3(50000.f);
		jt.springR = vec3(10000.f);
		jt.dampingT = vec3(1.f);
		jt.dampingR = vec3(1.f);
		Pmx->connectRb(ndParent->rb0, node->rb0, 0, 1, jt);
	}
	return false;
}

void irr::scene::IrrSaba::copyPoseTo(int pose, IrrSaba* sb2, int pose2)
{
	saba::VMDFile vmdFile, vmdFile2;
	if (!saba::ReadVMDFile(&vmdFile, motionFile.std_string().c_str()));
	uint32_t newFrame = pose2;
	if (pose2 < 0)
		newFrame = sb2->Vmd->GetMaxKeyTimeFrameDeprecated();

	for (auto& vm : vmdFile.m_motions)
	{
		if ((int)vm.m_frame == pose) vmdFile2.m_motions.push_back(vm);
	}

	for (auto& vm : vmdFile2.m_motions)
	{
		vm.m_frame = newFrame;
	}

	for (auto& vm : vmdFile.m_morphs)
	{
		if ((int)vm.m_frame == pose) vmdFile2.m_morphs.push_back(vm);
	}

	for (auto& vm : vmdFile2.m_morphs)
	{
		vm.m_frame = newFrame;
	}

	sb2->Vmd->Add(vmdFile2);
}

void irr::scene::IrrSaba::saveMidi()
{
#if MMD_SAVE_PUNCH_MIDI
	midilib::MidiOutFile mof; mof.newFile();
	for (size_t i = 0; i < midiArr.size(); i++)
	{
		auto& vi = midiArr[i];
		smf::MidiEvent ev; ev.makeNoteOn(vi.track, 60 + vi.track * 12, 100);	mof.addKey(vi.tick, ev);
		smf::MidiEvent me; me.makeNoteOff(vi.track, 60, 0);	mof.addKey(vi.tick + 200, me);

	}
	mof.writeFile("out/punch.mid");
#endif
}

void irr::scene::IrrSaba::setWriting(bool wr)
{
	if (!ndIkFingerR) return;
	ndIkFingerR->EnableIK(1);
	ndIKArmR->EnableIK(0);
	ndIKArmL->EnableIK(!wr);
	//setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"左腕"), !wr);
	//setBonePhsActive(Pmx->GetNodeManager()->FindNode(L"右ひじ"), !wr); //右腕

	zyTimer = wr ? 0.05 + UaRandF() * std::min(2.0f, svgMan->totalTime) * 0.5f : 0.2;
	Pmx->iksMulArm = wr ? 1 : 0.1f;
	if (wr) setAdAnim(0, 0.1f);
	else setAdAnim(-1, 0.1f);
}

void irr::scene::IrrSaba::setHandPointCam(bool b) {
	HandPointCam = b;// evtMoving = b;
	if (b) {
		fist = (fist + 1) % 2;
		auto tgg = SceneManager->getActiveCamera()->getTarget();
		mmdBaseInv.transformVect(tgg); curSTP[0].pos = curSTP[0].poL = lookTgtMmdPos + (tgg - lookTgtMmdPos).normalize() * (HandPointCam ? 0.1f : 3.f);
#if MMD_SAVE_PUNCH_MIDI
		float mt; Ctx->getLib()->CurStage()->StageGetVar(ILibStage::varMediaTime, &mt);
		smf::MidiEvent me; me.tick = mt * 1000; me.track = fist;
		midiArr.push_back(me);
#endif
	}
	//ndIkFingerR->EnableIK(b);
	//ndIKArmR->EnableIK(!b  );
	//if (b) setAdAnim(1, 0.3f);	else setAdAnim(-1, 0.1f);
}

void irr::scene::IrrSaba::mmdToIrrPos(irr::core::vector3df& pos) {
	mmdBaseMat.transformVect(pos);
}
irr::core::vector3df irr::scene::IrrSaba::mmd2irr(irr::core::vector3df pos) {
	mmdBaseMat.transformVect(pos);
	return pos;
}
void irr::scene::IrrSaba::irrToMmdPos(irr::core::vector3df& pos) {
	mmdBaseInv.transformVect(pos);
}
irr::core::vector3df irr::scene::IrrSaba::irr2mmd(irr::core::vector3df pos) {
	mmdBaseInv.transformVect(pos);
	return pos;
}
void  irr::scene::IrrSaba::toggleLnv(int mode, int add)
{
	switch (mode)
	{
	case 1: case 2:
	{
		specId = (specId + add) % specSets.size();
		for (size_t i = 0; i < mModel->GetMaterialCount(); i++)
		{
			const auto& mmdMat = mModel->GetMaterials()[i];
			auto& cb = cbcps[i].cb;
			cb.Specular =
				float4(mmdMat.m_specular.r, mmdMat.m_specular.g, mmdMat.m_specular.b, mmdMat.m_specularPower) + specSets[specId];
			//if (mode == 2) cb.Specular = { 0,0,0,0 };
		}

	}
	break;


	default:
	{
		lnvId = (lnvId + add) % lnvSets.size();
		for (size_t i = 0; i < mModel->GetMaterialCount(); i++)
		{
			auto& cb = cbcps[i].cb;
			cb.lnv = lnvSets[lnvId];
			DP(("LNV %f,%f,%f,%f", cb.lnv.x, cb.lnv.y, cb.lnv.z, cb.lnv.w));
		}
	}
	break;
	}


}

void irr::scene::IrrSaba::setPlaying(bool pl)
{
	mPlaying = pl;
	if (DISABLE_LEAP_ONSTART) enableLeap(false);
}


void irr::scene::IrrSaba::nodeToFwSrc(saba::NodeExtData& ne, irr::core::matrix4& matAbs)
{
	fwtAbsVel = ne.ms.spd;
	fwtMat = matrix4();
#if 0
	fwtMat = matAbs;
#else
	fwtMat.setTranslation(matAbs.getTranslation());
	fwtMat.setRotationDegrees(matAbs.getRotationDegrees());
#endif
}

//bool irr::scene::IrrSaba::isReplaceMat(std::string name)
//{
//	return false
//		||name.find(ualib::WcstoUtf8(L"裙")) != std::string::npos
//		||name.find(ualib::WcstoUtf8(L"服")) != std::string::npos
//		||name.find(ualib::WcstoUtf8(L"新規")) != std::string::npos
//		;
//}

bool irr::scene::IrrSaba::needControl(saba::MMDNode* nd, int flag)
{

	if (flag == 0)
		return false
		|| (nd->flMove && nd->flVisible && nd->GetName()[0] == '@')
		|| nd->GetNameU() == L"全ての親"
		|| nd->GetNameU() == L"センター"
		//|| nd->GetNameU() == L"センター2" || nd->GetNameU() == L"グルーブ" || nd->GetNameU() == L"グルーブ2"

		//|| nd->GetNameU() == L"腰"
		|| nd->GetNameU() == L"首"
		|| nd->GetNameU() == L"両目"
		|| nd->GetNameU() == L"上半身"
		|| nd->GetNameU() == L"上半身2"
		|| nd->GetNameU() == L"上半身3"
		|| nd->GetNameU() == L"左腕"
		|| nd->GetNameU() == L"右腕"
		|| nd->GetNameU() == L"左ひじ"
		|| nd->GetNameU() == L"右ひじ"
		|| nd->GetNameU() == L"左手首"
		|| nd->GetNameU() == L"右手首"
		|| nd->GetNameU() == L"左足"
		|| nd->GetNameU() == L"右足"
		//|| nd->GetNameU() == L"pinHandL"		|| nd->GetNameU() == L"pinHandR"
		//|| nd->GetNameU() == L"右人指１"
		//|| nd->GetNameU() == L"右人指先"//		|| nd->GetNameU() == L"右人差指先"
		;
	if (flag == 1 || flag == 2) {
		return true;
		const std::wstring& s = nd->GetNameU();
		auto ch = s[s.size() - 1];
		return ch == L'１' || ch == L'先';
	}
}

bool irr::scene::IrrSaba::hasVtxData() {
	return Pmx && Pmx->hasBoneInfo();
}

#define HIT_SCREEN_PLANE 0
saba::MMDNode* irr::scene::IrrSaba::getPickNodeByNodeId(int nid)
{
	saba::MMDNode* pickingNode{};
	if (nid < 0)
	{
		pickingNode = nullptr; return nullptr;
	}
	pickingNode = Pmx->GetNodeManager()->GetMMDNode(nid);

	matrix4& mt = *(matrix4*)&pickingNode->GetGlobalTransform();
	pickNodeIrrPos = mt.getTranslation();
	mmdBaseMat.transformVect(pickNodeIrrPos);
	LfwParam lpm; lpm.pmz = 0.1f;	Eqv->LaunchFw3D(pickNodeIrrPos, Eqv->getFwIdx(2, 0), { 0,0,0 }, SColorf(1, 1, 1, 0.1f), &lpm);
	auto camRealPos = SceneManager->getActiveCamera()->getAbsolutePosition();
	pickDisMul = pickNodeIrrPos.getDistanceFrom(camRealPos) / Ctx->getPointerHitRealPos().getDistanceFrom(camRealPos);
	pickingNode->exd.pickStartPos = { Ctx->gd.mouseX, Ctx->gd.mouseY };
	pickingNode->exd.pickStartTransform = pickingNode->GetGlobalTransform();
	pickingNode->exd.pickStartAnimRotate = pickingNode->GetAnimationRotate();
	//pickNodeMove(pickingNode, { Ctx->gd.mouseX, Ctx->gd.mouseY });
	return selectedNode = pickingNode;
}

saba::MMDNode* irr::scene::IrrSaba::getPickNodeByMarker(core::vector2df xy, u32 forcePick)
{
	saba::MMDNode* pickingNode{};

	matrix4 mt; vector3df hitpos, camPos = SceneManager->getActiveCamera()->getAbsolutePosition();
	hitpos = Ctx->getPointerHitRealPos(&xy);
	auto dir1 = (hitpos - camPos).normalize();
	std::vector<PickNodesInfo> pns;
	if (forcePick && snMarks.size()) {
		auto it = snMarks[ndCenter->GetIndex()];
		PickNodesInfo pi; pi.nd = ndCenter;
		auto snpos = it.pos; mmdBaseMat.transformVect(snpos);
		auto scrPos = SceneManager->getActiveCamera()->getScreenSpacePos(snpos);
		pi.dis2d = vector2df(scrPos.X, scrPos.Y).getDistanceFrom(xy);
		pns.push_back(pi);
	}
	else for (auto& it : snMarks)
	{
		core::matrix4 mm = it.second.nd->GetGlobalTransform();

		auto snpos = it.second.pos; mmdBaseMat.transformVect(snpos);
		auto scrPos = SceneManager->getActiveCamera()->getScreenSpacePos(snpos);
		scrPos = vector3df(Ctx->gd.scrWidth / 2 + scrPos.X * Ctx->gd.scrWidth / 2, Ctx->gd.scrHeight / 2 - scrPos.Y * Ctx->gd.scrHeight / 2, scrPos.Z);
		auto vtocam = snpos - camPos;
		PickNodesInfo pi;
		//pi.angle = vtocam.normalize().angleBetweenInDegree(dir1);
		pi.nd = it.second.nd;
		pi.dis2d = vector2df(scrPos.X, scrPos.Y).getDistanceFrom(xy);//  +( pi.nd->IsIK() ? 0 : it.second.sizeR/4);
		if (pi.dis2d < it.second.sizeR / 2)
		{
			//pi.dis =  vtocam.getLengthSQ();// +(pi.angle > 2.5f ? 10000.f : 0.f);
			pns.push_back(pi);
			DPWCS((L"n %f %f %s", scrPos.X, scrPos.Y, pi.nd->GetNameU().c_str()));
		}
	}
	if (pns.size() > 1)
		std::sort(pns.begin(), pns.end(), [&](PickNodesInfo& a, PickNodesInfo& b) {return a.dis2d < b.dis2d; });
	if (pns.size() > 0) {
		pickingNode = pns[0].nd;
		matrix4& mt = *(matrix4*)&pickingNode->GetGlobalTransform();
		glm::vec3 mpos = pickNodeIrrPos = mt.getTranslation();
		mmdBaseMat.transformVect(pickNodeIrrPos);
		auto camRealPos = SceneManager->getActiveCamera()->getAbsolutePosition();
		pickDisMul = pickNodeIrrPos.getDistanceFrom(camRealPos) / Ctx->getPointerHitRealPos().getDistanceFrom(camRealPos);

		DPWCS((L"Pn %s", pns[0].nd->GetNameU().c_str()));
		LfwParam lpm; lpm.pmz = 0.1f;	Eqv->LaunchFw3D(pns[0].pos, Eqv->getFwIdx(2, 0), { 0,0,0 }, SColorf(1, 1, 1, 0.1f), &lpm);
		pickingNode->exd.pickStartPos = { xy.X,xy.Y };
		pickingNode->exd.pickStartTransform = pickingNode->GetGlobalTransform();
		pickingNode->exd.pickStartAnimRotate = pickingNode->GetAnimationRotate();
		//pickNodeMove(pickingNode, xy);

	}
	return selectedNode = pickingNode;
}
void irr::scene::IrrSaba::pickNodeMove(saba::MMDNode* pickingNode, core::vector2df xy, bool swapYZ, bool forceRtt)
{
	if (!pickingNode)return;
	if (pickingNode->flMove && !forceRtt) {

		irr::core::matrix4 mi; SceneManager->getActiveCamera()->getViewMatrix().getInverse(mi);
		vector3df pos;
		float oy = xy.Y;
		if (swapYZ) xy.Y = pickingNode->exd.pickStartPos.y;

		pos = Ctx->getPointerCamSpacePos(&xy);//hit pt in cam space
		//DP(("ss xyz %f %f %f",pos.X,pos.Y,pos.Z));
		pos *= pickDisMul;
		if (swapYZ) {
			pos.Z += (-oy + pickingNode->exd.pickStartPos.y) * 2;
		}

		mi.transformVect(pos);// to real pos
		mmdBaseInv.transformVect(pos);//mmd global Pos

		core::matrix4 mp = pickingNode->GetParent() ? pickingNode->GetParent()->GetGlobalTransform() : glm::mat4(1);
		mp.getInverse(mi);
		mi.transformVect(pos);
		pickingNode->SetAnimationTranslate(pos - pickingNode->GetTranslate());
		pickingNode->exd.pickStartPos = { xy.X,xy.Y };
		if (mmd->syncMovNode) for (auto sb : mmd->sabas) {
			sb->findNode(pickingNode->GetNameU())->SetAnimationTranslate(pickingNode->GetAnimationTranslate());
		}
		if (Ctx->getEvtRcv()->IsKeyDown(irr::KEY_LCONTROL)) {
			auto& n = pickingNode->GetNameU(); bool isL = n[0] == L'左';
			if (isL || n[0] == L'右') {
				auto name = n; name[0] = isL ? L'右' : L'左';
				auto nd = findNode(name);
				nd->SetAnimationTranslate(pickingNode->GetAnimationTranslate() * vec3(-1, 1, 1));
				if (mmd->syncMovNode) for (auto sb : mmd->sabas) {
					sb->findNode(nd->GetNameU())->SetAnimationTranslate(nd->GetAnimationTranslate());
				}
			}
		}

	}
	else if (pickingNode->flRotate) {

		glm::vec3 rtt{};
		if (swapYZ) 			rtt.z -= (xy.Y - pickingNode->exd.pickStartPos.y) * core::DEGTORAD / 10.f;
		else rtt.x -= (xy.Y - pickingNode->exd.pickStartPos.y) * core::DEGTORAD / 10.f;
		rtt.y -= (xy.X - pickingNode->exd.pickStartPos.x) * core::DEGTORAD / 10.f;

#if 1
		auto mCv = SceneManager->getActiveCamera()->getViewMatrix();
		matrix4 mi, m;

		m = mCv * mmdBaseMat * pickingNode->GetGlobalTransform();

		m.getInverse(mi);
		core::quaternion qtr; core::quaternion qtr1 = vector3df(rtt);
		matrix4 m1 = qtr1.getMatrix();
		m = mi * m1 * m;
		qtr = m;
		pickingNode->SetAnimationRotate(pickingNode->GetAnimationRotate() * glm::quat(qtr));
		pickingNode->exd.pickStartPos = { xy.X,xy.Y };
#else
		auto mCv = SceneManager->getActiveCamera()->getViewMatrix();
		matrix4 mi, m;

		m = mCv * mmdBaseMat * pickingNode->exd.pickStartTransform;// GetGlobalTransform();

		m.getInverse(mi);
		core::quaternion qtr; glm::quat qtr1 = rtt;


		pickingNode->SetAnimationRotate(pickingNode->exd.pickStartAnimRotate * qtr1);
#endif
		if (mmd->syncMovNode) for (auto sb : mmd->sabas) {
			sb->findNode(pickingNode->GetNameU())->SetAnimationRotate(pickingNode->GetAnimationRotate());
		}
	}

}

bool irr::scene::IrrSaba::ifPickTmpIK(saba::MMDNode*& pickingNode, core::vector2df xy, saba::MMDNode* tmpIkRoot)
{
	irr::core::matrix4 mi; SceneManager->getActiveCamera()->getViewMatrix().getInverse(mi);
	vector3df pos;
	pos = Ctx->getPointerCamSpacePos(&xy);//hit pt in cam space
	pos *= pickDisMul;
	mi.transformVect(pos);// to real pos
	mmdBaseInv.transformVect(pos);//mmd global Pos

	if (tmpIkRoot && pickingNode && !pickingNode->IsIK()) {
		Pmx->rebuildTmpIK(pickingNode, tmpIkRoot, nullptr);
		Pmx->getTmpIkNode()->SetAnimationTranslate(pos);
		pickingNode = Pmx->getTmpIkNode();
		DP(("tmpik reb"));
		return true;
	}
	return false;
}

bool irr::scene::IrrSaba::setTmpIK(saba::MMDNode*& pickingNode, saba::MMDNode* tmpIkRoot)
{

	vector3df pos = SceneManager->getActiveCamera()->getAbsolutePosition();
	mmdBaseInv.transformVect(pos);//mmd global Pos

	if (tmpIkRoot && pickingNode && !pickingNode->IsIK()) {
		Pmx->rebuildTmpIK(pickingNode, tmpIkRoot, nullptr);
		//Pmx->getTmpIkNode()->SetAnimationTranslate(pos);
		pickingNode = Pmx->getTmpIkNode();
		DP(("tmpik reb"));
		return true;
	}

	return false;
}

void irr::scene::IrrSaba::setNodeRealPos(saba::MMDNode* pickingNode, core::vector3df pos)
{
	mmdBaseInv.transformVect(pos);//mmd global Pos
	core::matrix4 mi, mp = pickingNode->GetParent() ? pickingNode->GetParent()->GetGlobalTransform() : pickingNode->GetGlobalTransform();
	mp.getInverse(mi);
	mi.transformVect(pos);
	pickingNode->SetAnimationTranslate(pos - pickingNode->GetTranslate());
}

void irr::scene::IrrSaba::translateNodeInCamSpace(saba::MMDNode* node, int space, core::vector3df ofs)
{
	auto mCv = SceneManager->getActiveCamera()->getViewMatrix();
	matrix4 mi, m, mp = node->GetParent() ? node->GetParent()->GetGlobalTransform() : glm::mat4(1);

	switch (space)
	{
	case 1:m = mmdBaseMat * mp; break;
	case 2:m = mCv * mmdBaseMat * mp; break;
	default: //local
		m = glm::inverse(node->GetLocalTransform());
		break;
	}

	vector3df pos;
#if 1
	core::matrix4 mt; mt.setTranslation(ofs * m.getScale() / 10.f);
	m.getInverse(mi);
	m = mi * mt * m;
	pos = m.getTranslation();
	node->SetAnimationTranslate(node->GetAnimationTranslate() + glm::vec3(pos));
#else
	m.transformVect(pos);   // mmd to cam space
	pos += ofs;
	irr::core::matrix4 mCvi; mCv.getInverse(mCvi);
	mCvi.transformVect(pos);//to real pos
	matAbsInv.transformVect(pos);//mmd global Pos
	assert(node->GetParent());
	core::matrix4 mp = node->GetParent()->GetGlobalTransform();
	mp.getInverse(mi);
	mi.transformVect(pos);
	core::matrix4 mp = node->GetParent()->GetGlobalTransform();
	mp.getInverse(mi);
	mi.transformVect(pos);
	node->SetAnimationTranslate(pos - node->GetTranslate());
#endif

}

void irr::scene::IrrSaba::rotateNodeInCamSpace(saba::MMDNode* node, int space, core::vector3df ofs)
{
	auto mCv = SceneManager->getActiveCamera()->getViewMatrix();
	matrix4 mi, m;
	switch (space)
	{
	case 1:m = mmdBaseMat * node->GetGlobalTransform(); break;
	case 2:m = mCv * mmdBaseMat * node->GetGlobalTransform(); break;
	default: //local
		m = glm::inverse(node->GetLocalTransform());
		break;
	}
	m.getInverse(mi);
	core::quaternion qtr; core::quaternion qtr1 = ofs;
	matrix4 m1 = qtr1.getMatrix();
	m = mi * m1 * m;
	qtr = m;
	node->SetAnimationRotate(node->GetAnimationRotate() * glm::quat(qtr));
}

void irr::scene::IrrSaba::globlToBaseAnime()
{
	bool active = Pmx->getDynRbActive();
	if (active) {
		auto nodeMan = Pmx->GetNodeManager();

		for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
		{
			PMXNode* node = (PMXNode*)nodeMan->GetMMDNode(i);
			node->EnableIK(false);

			//node->UpdateLocalTransform();
		}

		int count = 1;//  Pmx->rootRbBoneIdx - 1;
		for (int c = 0; c < count; c++) {
			auto node = nodeMan->getRootNode();  nodeMan->GetMMDNode(Pmx->rootRbBoneIdx);


			node->UpdateGlobalTransform();
			for (size_t i = node->GetIndex(); i < nodeMan->GetNodeCount(); i++)
			{
				PMXNode* node = (PMXNode*)nodeMan->GetMMDNode(i);
				//if (node->rb0 && node->rb0->dynRbType)
				{
					auto global = node->GetGlobalTransform();
					glm::mat4 m = global;
					if (auto np = node->GetParent()) {
						m = glm::inverse(np->GetGlobalTransform()) * m;
					}
					// if (auto apn = node->GetAppendNode()) node->SetAppendNode(nullptr);
#if 0 // not affect?
					if (auto apn = node->GetAppendNode()) {
						m = m  //*  glm::inverse(glm::translate(glm::mat4(1),apn->GetAppendTranslate()))
							* glm::inverse(glm::mat4(apn->GetAppendRotate()));
						//node->SetAnimationTranslate({});
						//node->SetAnimationRotate({1,0,0,0});
					}	//else
#endif
					node->setAnimationMatrix(m);
					//glm::vec3 scale;glm::quat rotation;glm::vec3 translation;glm::vec3 skew;glm::vec4 perspective;
					//glm::decompose(m, scale, rotation, translation, skew, perspective);
					//node->SetAnimationTranslate(translation - node->GetTranslate());
					//node->SetAnimationRotate(rotation);

				}
			}
			auto np = node->GetParent();
			int lvl = 0;
			while (np && lvl < 20)
			{
				lvl++;
				auto global = node->GetGlobalTransform();
				auto local = node->GetLocalTransform();
				glm::mat4 m, mpg = global * glm::inverse(glm::translate(glm::mat4(1), node->GetInitialTranslate()) * glm::mat4_cast(node->GetInitialRotate()));
				m = mpg;
				printMatTRS(global, L"global");

				if (auto npp = np->GetParent()) {
					m = glm::inverse(npp->GetGlobalTransform()) * mpg;
					//continue;
					printMatTRS(mpg, L"m1");
				}
				else {
					printMatTRS(mpg, L"m0");
					//node = np;// continue;
				}
				printMatTRS(local, L"l");
				printMatTRS(m, L"m");
				//m = local;
				glm::vec3 tr, skew, scale; glm::vec4 perspective; glm::quat rotation;
				glm::decompose(m, scale, rotation, tr, skew, perspective);
				DPWCS((L"G2b P %s %f,%f,%f", np->GetNameU().c_str(), tr.x, tr.y, tr.z));
				np->SetAnimationTranslate(tr - np->GetTranslate());
				np->SetAnimationRotate(rotation);
				np->UpdateLocalTransform();
				np->UpdateGlobalTransform();
				node = np; node->EnableIK(false);
				np = node->GetParent();
			}
			Pmx->UpdateAllAnimation(nullptr, 0, 0);
			//Pmx->SaveBaseAnimation();
		}

	}
	else //!active
	{
		Pmx->ResetPhysics();
		Pmx->GetPhysicsManager()->syncDynRb();
		auto rb = Rb0();
		printMatTRS(rb->GetTransform(), L"M1");

	}
}

saba::MMDNode* irr::scene::IrrSaba::getPickNodeByVtxId(int vid, int  byNode, glm::vec3* hitPos, bool onlyDynRb)
{
	saba::MMDNode* pickingNode{};
	if (byNode)
	{

		//if (!ndFingerR) ndFingerR = setNodeFlag(L"右人指先", EId::handL, 0);// 左手首
	}
	else if (vid >= 0)
	{
		pickSubMesh = Pmx->getMeshIdByVtxId(vid);
	}

	glm::vec3 mmdHitPos;
	if (hitPos) {
		pickNodeIrrPos = *hitPos;
		mmdHitPos = mmdBaseInv.getTransformedVect(*hitPos);
	}
	MMDRigidBody* rb = Pmx->getRbByVtxIdOrNode(vid,
		hitPos ? &mmdHitPos : nullptr,
		byNode ? (byNode == 5 ? ndYao : byNode == 7 ? ndUpper2 : byNode == 8 ? ndFootL : byNode == 9 ? ndFootR : byNode == 3 ? ndHead : byNode == 1 ? ndHandR : ndHandL) : nullptr, nullptr,
		false, 0x1 | (byNode ? 0x10 : 0) | (onlyDynRb?0x10000:0) );

	if (byNode) {
#if IS_WIN
		auto worldPos = mmd2irr(rb->getPosition());
		auto screenPos = SceneManager->getSceneCollisionManager()->getScreenCoordinatesFrom3DPosition(worldPos, SceneManager->getActiveCamera());
		extern HWND hWndVk;
		POINT pos = { Ctx->gd.mouseX = screenPos.X, Ctx->gd.mouseY = screenPos.Y };
		if (::ClientToScreen(hWndVk, &pos))
			::SetCursorPos(pos.x, pos.y);
#endif
	}
	if (!rb)
	{
		return nullptr;
	}

	if (vid == PICK_NODE_CONTINUE)
	{
		pickNodeIrrPos = mmd2irr(Pmx->pickRbPos = Pmx->touchRB->getPosition());
	}
	pickFilter = byNode;
	rb->ResetMovement(0);
	pickingNode = rb->node;
#if 0
	matrix4& mt = *(matrix4*)&pickingNode->GetGlobalTransform(); pickNodePos = mt.getTranslation(); mmdBaseMat.transformVect(pickNodePos);
#endif
	if (Ctx->gd.ptCount == 1)
	{
		auto camRealPos = SceneManager->getActiveCamera()->getAbsolutePosition();
		pickDisMul = pickNodeIrrPos.getDistanceFrom(camRealPos) / Ctx->getPointerHitRealPos().getDistanceFrom(camRealPos);
		//DP(("pickDisMul %f", pickDisMul));
	}

	//mt.transformVect(pickNodePos);
	pickingNode->exd.pickStartPos = { Ctx->gd.mouseX, Ctx->gd.mouseY };
	pickingNode->exd.pickStartTransform = pickingNode->GetGlobalTransform();
	pickingNode->exd.pickStartAnimRotate = pickingNode->GetAnimationRotate();
	pickLastPtNum = Ctx->gd.ptCount;
	//Pmx->touchRB->Reset(Pmx->GetMMDPhysics());
	pickVtxMove(pickingNode, vector2df(Ctx->gd.mouseX, Ctx->gd.mouseY), false);
	return curPickingNode = pickingNode;
}
void irr::scene::IrrSaba::pickVtxMove(saba::MMDNode* pickingNode, core::vector2df xy, bool swapYZ)
{
	if (!Pmx->touchRB || !pickingNode)return;

	irr::core::matrix4 mi; SceneManager->getActiveCamera()->getViewMatrix().getInverse(mi);
	matrix4 mt; vector3df pos;
	if (Ctx->gd.ptCount == 1)
	{
		float oy = xy.Y;
		if (swapYZ) xy.Y = pickingNode->exd.pickStartPos.y;
		//xy -= vector2df(pickingNode->exd.pickStartPos.x, pickingNode->exd.pickStartPos.y);
		pos = Ctx->getPointerCamSpacePos(&xy);//hit pt in cam space
		//DP(("pickDisMul %f ", pickDisMul));
		pos *= pickDisMul;

		mi.transformVect(pos);// to real pos
		//DP(("pickDisMul %f   %.0f,%.0f", pickDisMul, xy.X, xy.Y));
		sbFw2D("sw2", pos / 100.f, vec3(0), 0x8080FF00);
	}
	else
	{
		auto cam = SceneManager->getActiveCamera();
		auto camRealPos = cam->getAbsolutePosition();
		pos = Ctx->getPointerHitRealPos();
		vector3df pickLineDir = (Ctx->getPointerHitRealPos() - camRealPos).normalize();

		vector3df camDir = cam->getTarget() - camRealPos; camDir.Y = 0;

		core::plane3df plane(core::vector3df(pickNodeIrrPos), camDir.normalize());
		core::vector3df ipt;
		if (!plane.getIntersectionWithLine(cam->getAbsolutePosition(), pickLineDir, ipt))
			return;
		pos = ipt;
	}

	//if (!snPickBall) snPickBall = SceneManager->addSphereSceneNode(30); snPickBall->setPosition(pos);
	Pm.mmd->lookAtPos = pos;
	LfwParam lpm; lpm.pmz = 0.1f;
	//Eqv->LaunchFw3D(pos, Eqv->getFwIdx(2, 0), { 0,0,0 }, SColorf(1, 1, 1, 0.1f), &lpm);

	//DP(("rp %f,%f,%f ", pos.X, pos.Y, pos.Z));
	mmdBaseInv.transformVect(pos);
	//DP(("mp %f,%f,%f ", pos.X, pos.Y, pos.Z));
	if (pos.y < 0) pos.y = 0;
	mt.setTranslation(pos);

	//Pmx->touchRB->setGlmMat(mt);//
	Pmx->touchRB->SetCoMTranslate(pos);
	//Pmx->touchRB->m_kinematicMotionState

	pickLastPtNum = Ctx->gd.ptCount;
}


#if MMD_SAVE_VMD
void irr::scene::IrrSaba::recordCamBegin()
{
	//vmdWriteFile = std::make_shared<vmd::VmdMotion>();
	vmdWriteFile = vmd::VmdMotion::LoadFromFile("d:/mmd/vmd/camstd.vmd");
	vmdWriteFile->camera_frames.clear(); vmdWriteFile->recordingAllNode = true;
}
void irr::scene::IrrSaba::recordCamFrame(int id)
{
	if (Pm.needSaveCamVmd && vmdWriteFile && vmdWriteFile->recordingAllNode) {

		auto cam = Ctx->getSceneManager()->getActiveCamera();
		auto pos = cam->getAbsolutePosition(), tgt = cam->getTarget();
		auto dir = (tgt - pos).normalize();
		tgt = pos + dir * 1000.f;
		mmdBaseInv.transformVect(pos);
		mmdBaseInv.transformVect(tgt);
		vmd::VmdCameraFrame  k = vmdWriteFile->camFrameBase;
		k.frame = id;// (animeTime * 30 + 0.001f);
		if (vmdWriteFile->camera_frames.size() == 0 || vmdWriteFile->camera_frames[vmdWriteFile->camera_frames.size() - 1].frame < k.frame) {

			k.angle = cam->getFOV() * core::RADTODEG;
			k.isPerspective = 0;
			k.distance = -(pos - tgt).getLength();
			auto rtt = cam->getRotation();
			glm::mat4 transformation = mmdBaseInv * cam->getRelativeTransformation(); // your transformation matrix.

			glm::extractEulerAngleYXZ(transformation, rtt.Y, rtt.X, rtt.Z);
			k.orientation[0] = -rtt.X;
			k.orientation[1] = -rtt.Y;
			k.orientation[2] = -rtt.Z;
			memcpy(&k.position, &tgt, sizeof(k.position));
			//memcpy(&k.orientation, &rtt, sizeof(k.orientation));

			DP(("cam fra %d %f %f    %f", k.frame, k.position[0], k.orientation[0], animeTime * 30 + 0.001f));
			vmdWriteFile->camera_frames.push_back(k);
		}
	}
}
void irr::scene::IrrSaba::recordCamEnd()
{
	if (!Pm.needSaveCamVmd) return;
	vmdWriteFile->recordingAllNode = false; vmdWriteFile->SaveToFile(L"d:/mmd/vmd/camout.vmd");
}

void irr::scene::IrrSaba::recordVmdEnd()
{
	if (!vmdWriteFile) return;
#if SVG_MMD_WRITE && SVG_MMD_WRITE_SAVE_VMD
	vmd::VmdIkFrame ikf;	ikf.frame = 0; ikf.display = true;
	vmd::VmdIkEnable ike;	ike.enable = false;
	ike.ik_name = ualib::WcstoUtf8(L"左足ＩＫ");	ikf.ik_enable.push_back(ike);
	ike.ik_name = ualib::WcstoUtf8(L"左つま先ＩＫ");	ikf.ik_enable.push_back(ike);
	ike.ik_name = ualib::WcstoUtf8(L"右足ＩＫ");	ikf.ik_enable.push_back(ike);
	ike.ik_name = ualib::WcstoUtf8(L"右つま先ＩＫ");	ikf.ik_enable.push_back(ike);
	vmdWriteFile->ik_frames.insert(vmdWriteFile->ik_frames.begin(), ikf);
#endif
	vmdWriteFile->recordingAllNode = false;
	vmdWriteFile->SaveToFile(L"r:/recMotion.vmd");

}

#endif
void irr::scene::IrrSaba::saveVmd(irr::io::path fp)
{
	vmd::VmdMotion vmd;// = *vmd::VmdMotion::LoadFromFile("d:/mmd/vmd/camstd.vmd").get();
	vmd.model_name = mModel->modelName;
	vmd::VmdBoneFrame bf;
	char szj[256] = {};
	for (int bi = 0; bi < mNodeMan->GetNodeCount(); bi++)
	{
		auto  bone = mNodeMan->GetMMDNode(bi);
		auto nc = bone->nodeCtrl;

		if (nc)	for (auto& nk : nc->GetKeys()) {
			bf.frame = nk.m_time;
			int len = ualib::utf8ToMbcs(szj, 256, bone->GetName().c_str(), 0);
			bf.name = szj;
			bf.position[0] = nk.m_translate.x;
			bf.position[1] = nk.m_translate.y;
			bf.position[2] = nk.m_translate.z;
			bf.orientation[0] = nk.m_rotate[0];
			bf.orientation[1] = nk.m_rotate[1];
			bf.orientation[2] = nk.m_rotate[2];
			bf.orientation[3] = nk.m_rotate[3];
			saba::GetVMDBezier(bf.interpolation, nk.m_txBezier);
			saba::GetVMDBezier(bf.interpolation + 16, nk.m_tyBezier);
			saba::GetVMDBezier(bf.interpolation + 32, nk.m_tzBezier);
			saba::GetVMDBezier(bf.interpolation + 48, nk.m_rotBezier);
			vmd.bone_frames.push_back(bf);
		}
	}

	vmd.SaveToFile(fp.std_string());
}

void IrrSaba::ensureNodeControl(saba::MMDNode* node)
{

	Vmd->ensureNodeCtrl(node);

}

void IrrSaba::addAnimationKeyFrame(uint32_t frame)
{

	auto nodeMan = Pmx->GetNodeManager();
	for (size_t i = 0; i < nodeMan->GetNodeCount(); i++)
	{
		auto nd = nodeMan->GetMMDNode(i);
		if ((nd->flMove || nd->flRotate) && !nd->IsPhysicsActive)
		{
			auto trs = nd->GetAnimationTranslate();
			auto rtt = nd->GetAnimationRotate();
			//if (trs != glm::vec3(0, 0, 0) || rtt != glm::quat(1, 0, 0, 0))
			{
				if (!nd->nodeCtrl)
					ensureNodeControl(nd);
				nd->nodeCtrl->setKeyAt(frame);
				//DPWCS((L"%9s %f", nd->GetNameU().c_str(), nd->GetAnimationTranslate().y));
			}
		}
	}
	Vmd->checkMaxKeyTime(frame);
}

void irr::scene::IrrSaba::aniSetNodeKeyFrame(saba::MMDNode* node, uint32_t frame) {
	if (!Vmd.get()) {
		loadAnimation("res/empty.vmd");
	}
	if (!node->nodeCtrl) ensureNodeControl(node);
	node->nodeCtrl->setKeyAt(frame);
	Vmd->checkMaxKeyTime(frame);
}

#if MMD_ADD_WING
void irr::scene::irrSaba::initWing()
{
	MMDNode* nd, * nds;
	saba::MMDNodeManager* nodeMan = mdRWing->GetNodeManager();
	nds = nd = nodeMan->GetMMDNode(0);
	while (nds = nds->GetChild())
	{
		DP(("nds %s", nds->GetName().c_str()));
	}

}
#endif

irr::scene::MmdCompute::MmdCompute(video::IVideoDriver* driver) :Driver((VkDriver*)driver)
{
	DP(("MmdCompute Created"));
	Device = Driver->Device;
}

irr::scene::MmdCompute::~MmdCompute()
{

	if (gpuCamTrnasform) {
		mVbPosNorm->drop();

		//mVbMorph->drop();
		//VkFxBase did this vkDestroyPipeline(Device, ppl, nullptr);
		mFxCsMmdVtx->freeDs(&dsMMD);
		vkFreeCommandBuffers(Device, Driver->getComputeCommandPool(), 1, &vkCmb);
		vkDestroyFence(Device, fence, nullptr);
	}

	if (pn) delete[] pn;
	DP(("MmdCompute Destroyed"));
}



void irr::scene::MmdCompute::prepareGPUTransform(MMDModel* model)
{
	PMXModel* pmx = dynamic_cast<PMXModel*>(model);
	if (!pmx) return;
	gpuCamTrnasform = true;
#define offsetofDP(C, M) DP(("%10s %10d",#M,offsetof(C,M)));

	offsetofDP(PMXModel::VertexBoneInfo, m_sdef.m_sdefC);
	offsetofDP(PMXModel::VertexBoneInfo, m_sdef.m_sdefR0);
	offsetofDP(PMXModel::VertexBoneInfo, m_sdef.m_sdefR1);
	vtxCount = pmx->GetVertexCount();

	mVbPosNorm = (VkHardwareBuffer*)Driver->createHardwareBuffer(video::EHBT_STORAGE, video::EHBA_DEFAULT_RW, vtxCount * sizeof(PosNorm));
	mCbMats.createBuffer(Driver->mDevice, VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT, VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT, sizeof(PmxCB), nullptr);
	if (!sbd->mVbVtxBoneInfo) sbd->mVbVtxBoneInfo = (VkHardwareBuffer*)Driver->createHardwareBuffer(video::EHBT_STORAGE, video::EHBA_DEFAULT, vtxCount * sizeof(saba::PMXModel::VertexBoneInfo), EHBF_ONE_TIME_UPLOAD, pmx->GetBoneInfos());
	mVbMorph.createBuffer(Driver->mDevice, VK_BUFFER_USAGE_STORAGE_BUFFER_BIT, VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT, vtxCount * sizeof(glm::vec4), nullptr);

	last_morphVtxMax = vtxCount - 1;
	const glm::vec3* pos = pmx->GetPositions();
	const glm::vec3* norm = pmx->GetNormals();
	const glm::vec2* uvs = pmx->GetUVs();

	if (!sbd->mVbOrigPN) {
		pn = new PosNorm[vtxCount];
		for (int i = 0; i < vtxCount; i++)
		{
			pn[i].pos = glm::vec4(pos[i], 1); pn[i].norm = glm::vec4(norm[i], 1);
			auto& uv = uvs[i]; pn[i].u = uv.x; pn[i].v = 1 - uv.y;//TexInvV
		}
		sbd->mVbOrigPN = (VkHardwareBuffer*)Driver->createHardwareBuffer(video::EHBT_STORAGE, video::EHBA_DEFAULT,
			vtxCount * sizeof(PosNorm), EHBF_ONE_TIME_UPLOAD, pn);
		if (pn) delete[] pn; pn = nullptr;
	}

	mrMMD = (video::VkMaterialRenderer_MMD*)Driver->getMaterialRenderer(EMT_MMD);
	mrMMD->initCS();
	mFxCsMmdVtx = mrMMD->fxCsMmdVtx;
	mFxCsMmdVtx->allocDs(1, &dsMMD);

	// Fence for compute CB sync
	VkFenceCreateInfo fenceCreateInfo = vks::initializers::fenceCreateInfo(VK_FENCE_CREATE_SIGNALED_BIT);
	VK_CHECK_RESULT(vkCreateFence(Device, &fenceCreateInfo, nullptr, &fence));

	//UPDATE CMDBUF ===================================
	using namespace vks::initializers;
	VkWriteDescriptorSet computeWriteDescriptorSets[] = {
		writeDescriptorSet(dsMMD,VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,0,&mCbMats.descriptor),
		writeDescriptorSet(dsMMD,VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,1,&sbd->mVbOrigPN->Descriptor),
		writeDescriptorSet(dsMMD,VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,2,&sbd->mVbVtxBoneInfo->Descriptor),
		writeDescriptorSet(dsMMD,VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,3,&mVbMorph.descriptor),//->Descriptor),
		writeDescriptorSet(dsMMD,VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,5,&mVbPosNorm->Descriptor),
	};
	vkUpdateDescriptorSets(Device, sizeof(computeWriteDescriptorSets) / sizeof(VkWriteDescriptorSet), computeWriteDescriptorSets, 0, NULL);

	fillCmdBUf(vtxCount);
}

void irr::scene::MmdCompute::fillCmdBUf(int vtxCount)
{
	if (!gpuCamTrnasform) return;
	VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(
		Driver->getComputeCommandPool(), VK_COMMAND_BUFFER_LEVEL_PRIMARY, 1);
	VK_CHECK_RESULT(vkAllocateCommandBuffers(Device, &cmdBufAllocateInfo, &vkCmb));
	VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
	VK_CHECK_RESULT(vkBeginCommandBuffer(vkCmb, &cmdBufInfo));
	vkCmdBindDescriptorSets(vkCmb, VK_PIPELINE_BIND_POINT_COMPUTE, mFxCsMmdVtx->getPipelineLayout(), 0, 1, &dsMMD, 0, 0);
	vkCmdBindPipeline(vkCmb, VK_PIPELINE_BIND_POINT_COMPUTE, mrMMD->pplCsVtx);
	vkCmdDispatch(vkCmb, vtxCount / 256 + 1, 1, 1);
	//vks::tools::insertMemoryBarrier(vkCmb, VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT, VK_ACCESS_SHADER_WRITE_BIT, VK_PIPELINE_STAGE_DRAW_INDIRECT_BIT, VK_ACCESS_INDIRECT_COMMAND_READ_BIT);
	vkEndCommandBuffer(vkCmb);
}


// ================= PHYSICS + ===================

void irr::scene::IrrSaba::updatePhysicsStep(int step, int stepCount)
{
	//eKINEMATIC set postion will update rb position on step 1  (not 0)
	phyStep = step; phyStepCount = stepCount;
	float stepTime = phyStepTime = Ctx->gd.deltaTime * MMDPhysics::phyTimeMul / stepCount;


	//FLOAT Y
	float fy = mFloatY + mFloatYadd;
	if (ndUpper2 && fy > 1.f)
	{
		if (ndYao->rb0->pos.y < fy)
		{
			float delta = (fy - ndYao->rb0->getPosition().y);
			//Pmx->addBodyVel(delta * 0.6f * vec3(0, 1, 0),true);
			ndUpper->rb0->addLinearVel(delta * vec3(0, 8, 0));
			//if (delta>-1)			mmdFw( ndYao->rb0->getPosition(),Eqv->getCurPtrFwIdx(1), vec3(0, -delta*20-30, 0) );

			float dyu = (ndUpper2->rb0->initPos.y - ndYao->rb0->initPos.y);
			if (centerForceMul >= 1.f) ndUpper2->rb0->addLinearVelToPos(ndYao->rb0->pos + vec3(0, dyu * ndYao->absScale.y, 0), 10);
		}
		
		//{//Skirt Ring FW
		//	
		//	vec3 rbVel = glh::matRotateVec(Rb0()->GetTransform(),vec3(0,-10,0))  ;
		//	float len = glm::length(rbVel); int c = 16;
		//	if (len > 3.f) for (int i = 0; i < c; i++) {				
		//		auto pos = ndYao->rb0->getPosition();
		//		float r = 2.f, a = piFloat * 2*i / c;
		//		pos = pos + glh::directionToRotation(rbVel) * vec3(sin(a), cos(a), 0.33)*2.f ;
		//		auto vel = -rbVel + glh::directionToRotation(rbVel) * vec3(sin(a), cos(a), 2) * 60.f;
		//		mmdFw(pos + vel * 0.f, Eqv->getCurPtrFwIdx(1), vel);
		//	}
		//}
	}
	if (ndManco ) {
		mmdFw(1, "jetOpi", ndManco->getGlobalPos(), ndLower->rb0->getLinearVel(), 0xFFFFFFFF);
	}

	auto rb0 = Pmx->getRb(0);
	bool isPhy = rb0 && rb0->GetActivation();

	if (rbActCD > 0) {
		rbActCD--;
		if (rbActCD == 0) {
			if (!rb0->GetActivation()) setAllDynRbActive(true);
			Pmx->resetRigidBodies(1);
			Pmx->GetMMDPhysics()->clearForces();
		}
	}

	Pmx->onPhysicsStep(step, stepCount, stepTime);
	actionOnPose();

	// Update walk motion if active
	if (walkingActive) {
		updateWalkMotion(stepTime);
	}

	for (auto& nd : Pmx->ndfwNodes)
		launchNodeFw(*nd);
	if (ctrForce != 0.f)
	{
		rb0->addLinearVelToPos(vec3(0, 1, 0), ctrForce * stepTime);

	}

	if (sbTracker
		) {
		auto nd = sbTracker->findNode(L"下半身");
		auto rb = nd->rb0; //rb->SetActivation(true)
		vec3 dir = ndYao->getIfRbPos() - rb->getPosition();
		float dis = glm::length(dir);
		//rb->rotateFromDirToDir(vec3(0, -1, 0), ndYao->getIfRbPos() - rb->getPosition(), 1000.f);
		quat oqt = nd->GetAnimationRotate(); ;
		auto t = gPhyTime - lastTimeSetVelP2P;
		if (dis > 20.f && t > 0.f && t < 2.f)
		nd->SetAnimationRotate(glm::slerp(oqt,quat(glh::rotationFromTo(nd->pmxBone->m_positionOffset, dir)),0.02f));
		else if (dis <= 20) {
			nd->SetAnimationRotate(glm::slerp(oqt, quat(1, 0, 0, 0), 0.02f));
			if (t < 2.f) {
				Pmx->scaleBodyVel(0.99f, 1);
				Pmx->addBodyVel(-dir * 0.1f, 1);
			}
			if (dis < 10.f) lastTimeSetVelP2P = 0;
		}
	}
	//else if (isAiCharacter() && mmd->sabas.size()> itemIdx+1) {
	//	auto tn = mmd->sabas[itemIdx+1]->findNode(L"root");
	//	//getRb0()->addRotationToMatOnNode(tn->GetGlobalTransform(),200.f);
	//	if (tn) ndUpper2->rb0->addRotationToMatOnNode(tn->GetGlobalTransform(), 500.f);
	//}
	if (!isAiCharacter())
	{
		if (Pmx->isCharacter || forcePhyAnim) phyAnimationUpdate(stepTime, step, stepCount);
		return;
	}
	if (NODE_SPD_FW && isAiCharacter()
		&& itemIdx == 0
		)
	{
		static float hue = 0.0f; hue += 1.f / 6;
		{
			SColorHSL hsl(hue, 99, 50);	SColor sc = hsl.toSColor();
			ndFootL->exd.color = ndHandL->exd.color = sc.color;
		}
		{
			SColorHSL hsl(hue + 60, 99, 50);	SColor sc = hsl.toSColor();
			ndFootR->exd.color = ndHandR->exd.color = sc.color;
		}
		launchCbNodeFw(*ndHandL,1); launchCbNodeFw(*ndHandR,1);
		//launchCbNodeFw(*ndFootL, 1); launchCbNodeFw(*ndFootR, 1);
	}

#if 0
	{
		auto Sb = this; auto rbpnt = rb0->getNodeTransform(); float rat01 = 1;// Sb->ndLegL->GetParent()->GetGlobalTransform();
		float rt0 = 5 + rat01 * 25;
		float rt1 = 5 + rat01 * 60;
		Sb->ndLegL->rb0->addRotationToMatOnNode(glh::rttMatYXZDeg({ rt0,-80, 0 }), 60);
		Sb->ndLegR->rb0->addRotationToMatOnNode(glh::rttMatYXZDeg({ rt0, 80, 0 }), 60);
		Sb->ndLeg1L->rb0->addRotationToMatOnNode(glh::rttMatYXZDeg({ -rt1,-80, 0 }), 60);
		Sb->ndLeg1R->rb0->addRotationToMatOnNode(glh::rttMatYXZDeg({ -rt1, 80, 0 }), 60);
	}
#endif

#if 1  //update force
	int idYao = ndYao->GetIndex();
	static vec3 CharPos[] = { {0,3,0},{3,3,-3},{-3,3,-3}, {3,10,6},{-3,10,6},{0,10,0} ,{-3,12,3},{0,12,3} };
	vector3df charPos = //vec3(0, 12 + itemIdx * 6, +itemIdx * 2);
		CharPos[itemIdx]; charPos.Y = Pmx->yaoPos.y + 1;
	auto cammat = Ctx->gd.usingCamRbMat? core::matrix4(Ctx->gd.camRbMat) : mtCam;// matAbsInv* SceneManager->getActiveCamera()->getAbsoluteTransformation();
	matrix4 cammati;  cammat.getInverse(cammati);
	cammat = cammat.getTransformTR();

	//Pmx->rootTr = charPos;f

	if (isPhy || animeTime > 0.01f)
	{
		int i = itemIdx;
		auto& plr = Pm.mmd->mdplr;
		auto& cd = plr.chd[itemIdx];
		//if (step==0)
		if (rb0->GetActivation()) {
			//float stepCount = 1.f;
			float t = Ctx->gd.time;
			//rb0->setAngularVel(rb0->getAngularVel() * 0.3f);rb0->setLinearVel(rb0->getLinearVel() * 0.3f);
			float rat = std::clamp((1.f - (t - cd.kdTime)) * 2, 0.f, 1.f);

			vector3df camRelPos = charPos + vec3(0, -10, 25);
			cammat.transformVect(camRelPos);// if (camRelPos.Y < 5) camRelPos.Y = 5;
			curCharPos = charPos;// *(1 - rat) + vec3(camRelPos) * rat;
			if (centerForceMul < 1) {
				if (cfMulTimer > 0) cfMulTimer -= stepTime;
				else centerForceMul += stepTime;
				if (ndHead->getGlobalPos().y < 3) centerForceMul = 1;
				centerForceVec = 1.f - centerForceMinusUnit * (1 - centerForceMul);
			}
			else centerForceMul = 1.f;
			if (/*CENTER_FORCE_INIT ||*/ !ndRbRoot->phyAnim || localPhyAnim || rd.stage >= 0)
			{
				if (CenterForce) {
					//if (!ndYao->phyAnim)
					//mmdCamPos = vec3(0, 10, 0);
					//if (itemIdx == 0)
					{
						if (CenterForce & 2) {
							addRb2NdPosForce(ndHead, 0.5f, 3.f, 0.5f, 1);
							addRb2NdPosForce(ndUpper2, 0.5f, 10.f, 0.5f, 2);
						}
						if (CenterForce & 4) {
							addRb2NdPosForce(ndFootL, 8.f, 10.f, 8.f, 1);
							addRb2NdPosForce(ndFootR, 8.f, 10.f, 8.f, 1);
						}
						if (CenterForce & 8) {
							addRb2NdPosForce(ndHandL, 8.f, 10.f, 8.f, 1);
							addRb2NdPosForce(ndHandR, 8.f, 10.f, 8.f, 1);
						}
						//centerForceMul = { 1,0,1 };
					//	if (centerForceMul >= 0.5f && glm::length2(rb0->vel)>900.f) 		rb0->scaleVel(pow(0.91f, centerForceMul),1);
						auto yp = ndYao->rb0->getPosition(); auto dir = (ndYao->getRbAnimGlobalPos() - yp);
						rb0->addLinearVel(dir * (centerForceMul < 1.f ? centerForceVec : vec3(1, 0, 1)) * ((cd.act ? 10.f : 3.0f))); //CENTER FORCE

						ndYao->rb0->setAngVelToRotateOnNode(ndYao->mGlobalAnim, 3);
					}

				}

			}


			//rb0->setLinearVel(rb0->getLinearVel() * 0.6f);
			//rb0->setAngularVel(rb0->getAngularVel() * 0.6f);
			matrix4 mTurn = cammat;
			float fa1 = 1, fa = sin(PI * (t - cd.kdTime));// = (cd.act == 1 ? 10 : cd.keySound ? std::clamp(2 - (t - cd.kdTime) * 2, -1.f, 1.f) * 2 : -2);
			//rb0->setAngVelToRotateOnNode(glm::mat4(mTurn), fa1 *( cd.act ? 100.f :100.f ));

			auto camPos = glm::vec3(glm::mat4(mTurn)[3]);
			if (auto obj = lastLookObjId ? Pom->objRec[lastLookObjId].ifObj : nullptr) {
				camPos = obj->pos;
			}
			//DP(("DIS %f", ndHead->disTo(camPos)));
			float headDis = std::clamp(ndHead->disTo(camPos), 2.f, 100.f);
			if (ndLookAt && ndHead->rb0) {
				ndHead->rb0->setAngVelToPos(ndLookAt->getGlobalPos(), std::min(5.f, fa1 * 2 * disRatioPow(20, headDis, 1)) * ndLookAtMul);
			}
#if LOOKAT_CAM_TORQUE
			else if ((lookAt_mmdLookAt || canLookAtCam) && ndHead->rb0 && phyLookMul > 0.01f)
			{
				if (!lookAtPos && !lookAt_mmdLookAt) if (!ndYao->phyAnim) {
					//ndYao->rb0->setAngVelToPos(camPos, LOOKAT_CAM_TORQUE);
					ndUpper2->rb0->setAngVelToPos(camPos, LOOKAT_CAM_TORQUE);
				}
				ndHead->rb0->scaleVel(0.5f, 2);
				float powHead = std::min(10.f, fa1 * 1.75f * disRatioPow(20, headDis, 0.25f)) * LOOKAT_CAM_TORQUE * phyLookMul * phyLookHeadMul;
				float powEye = LOOKAT_CAM_TORQUE_EYE * phyLookMul; bool phyAnim = ndYao->phyAnim;

				IrrSaba::phyLAParam pm{
					.pos = lookAtPos ? *lookAtPos : camPos,
					.powHead = powHead * (lookAtPos ? 2 : phyAnim ? 3 : 2),
					.powHeadMax = phyAnim ? 1000.f : 100.f,
					.powEye = powEye * (phyAnim ?10.f : 1.f) ,
					.angleMul = (lookAtPos ? 2.f : 1.5f) * phyLookHeadAngMul ,
				};
				phyLookAt(pm);
				//if (lookAt_mmdLookAt) sbFw2Line("pt",ndHead->getGlobalPos(), pm.pos, 0xCC00FF80, 1000/pm.powHead);
			}
			else {
				//DP(("DDD"));
			}
#endif

			if (mic) {
				mic->setScale(1 + rat * 0.1f);
				mic->Pmx->rootTr.z = rat * 0.1f;
				ndHandL->rb0->setAngVelToPos(ndHead->getGlobalPos(), fa1 * 6 * disRatioPow(20, headDis, 2));
			}
			xiaoRat = std::clamp(rat * 0.5f + 5 / headDis, 0.f, 1.f);
		}
		phyAnimationUpdate(stepTime, step, stepCount);
	}
	else
	{
		curCharPos = charPos;
	}
#endif




	if (springMode >= 0)
	{
		spring(1, springMode, springMul);
	}

	if (snInflate)
	{

		auto box = snInflate->calcBound();
		MMDFWD(2, "swLong", box.getCenter(), vec3(0, 0, 0), SColorf(0.5, 1.0, 0.1, 1));
		ndYao->rb0->addLinearVelToPosLimitDis(box.getCenter(), 10, 0, 3);
		//ndYao->rb0->addForce((box.getCenter() - ndYao->rb0->getPosition()) * 10.f);			ndCenter->setAnimGlobalPos(box.getCenter());

	}


#if MMD_ATTACK_OBJ
	if (!Pmx->phyActAnimating && itemIdx >= PLATE_PLAYER_SKIP)
		charAtk.attackObjUpdate(stepTime, step, stepCount);
#endif
#if MMD_COMBINE_CATCHER
	if (!Pmx->phyActAnimating && tsb)
		cat.catcherUpdate(stepTime, step, stepCount);
#endif

	if (step < stepCount - 1)
		return;


}

void irr::scene::IrrSaba::actionOnPose()
{
	if (!ndYao) return;
	if (ndRbRoot && ndRbRoot->rb0
		// && isAiCharacter()
		)
	{
		auto rb = ndRbRoot->rb0;
		vec3 yp = rb->pos;
		bool clear = false;
		for (auto inTrgRb : rb->inTriggerRbs)
		{
			auto& tgp = inTrgRb->trigerParam;

			if (tgp.flag & TriggerParam::eSclVel) {
				Pmx->scaleDynBodyVel(tgp.sclVel, 1);
			}
			if (tgp.flag & TriggerParam::eSclRtt) {
				rb->scaleVel3(tgp.sclRtt, 2);
			}
			if (tgp.flag & TriggerParam::eAddVel) {
				Pmx->addBodyVel(tgp.addVel, 1);

				DP(("addv vel  Len=%f",  glm::length(rb->getLinearVel())));
				/*vec3 vMul = vec3(1.f) - glm::fastNormalize(tgp.addVel);
				Rb0()->addLinearVelToPosScVec(tgp.trb->getPosition(), vMul,3.f);*/
				//MMDFW(1, "flameL", rb->pos, tgp.addVel, SColorf(1, 1, 1, 1));
				if (inTrgRb->nameU == L"tgStarter") {
					ndUpper2->rb0->addLinearVel(tgp.addVel*(-3.f));
					ndLower->rb0->addLinearVel(tgp.addVel*2.f);
				}
			}
			 if ((tgp.flag & TriggerParam::eSetVelP2P)  && (mmd->trgSetVelP2P)) {

				auto tn = inTrgRb->node->model->GetNodeManager()->FindNode(tgp.tgtNodeName);

				if (tn) {
					vec3 vel{};
					vec3 setVelP2PTgt = tn->getGlobalPos();
					vec3 ovel = rb->getLinearVel();
					//ovel = glh::vecLimitFast(ovel,170,270.f);
					glh::calcSpeedDirP2PinTimeGuess_Param pm = {
					.startPosition = yp,
					.targetPosition = setVelP2PTgt,
					.velLen = glm::length(ovel),
					.initVel = ovel ,
					.ignoreVelLenLimit = 10.f,
					.gravity = gGravity,
					.time = 0,
					.linearDamping = ndYao->rb0->Pm.m_translateDimmer,
					.stepsPerSecond = 60 * SABA_PHYSICS_FRAMESTEP,
					.maxErrorMag = 100.f,
					.maxGuess = 20,
					};
					vel = glh::calcP2PAdjustVelocityGuess(pm.startPosition,pm.targetPosition,
						pm.initVel,pm.gravity,pm.linearDamping, 2.f, pm.stepsPerSecond,pm.maxGuess,0.01f);
					DP(("tgt pos %f %f %f", setVelP2PTgt.x, setVelP2PTgt.y, setVelP2PTgt.z));
					DP(("orig vel  %f %f %f  Len=%f", pm.initVel.x, pm.initVel.y, pm.initVel.z, glm::length(pm.initVel)));
					DP(("delta vel %f %f %f  len=%f", vel.x - pm.initVel.x, vel.y - pm.initVel.y, vel.z - pm.initVel.z, glm::length(vel)));

					Pmx->setBodyVel(glm::mix(ovel, vel, 0.5f), 1);
					lastTimeSetVelP2P = gPhyTime;
					// clear = true;
				}
			}
			if (tgp.flag & TriggerParam::eTeleport) {

				auto tn = inTrgRb->node->model->GetNodeManager()->FindNode(tgp.tgtNodeName);
				if (tn) {
					vec3 setVelP2PTgt = tn->getGlobalPos();
					Pmx->moveAllRbTo(setVelP2PTgt,false, false);
					if (tgp.flag & TriggerParam::eResetPhy) {
						FRAMEWAITER_CALL_B(3) {
							setAllDynRbActive(false, 1);

						}).waitNframeAndRun(3, [=](auto& t) {
							setAllDynRbActive(true, 1);
							});
					}
					// clear = true;
				}
			}
			if (tgp.flag & TriggerParam::eSpeedTo) {
				vec3 vel = rb->getLinearVel();
				vec3 velTo = glh::vecLimitFast(vel, tgp.speedToPm.x, tgp.speedToPm.y);
				rb->setLinearVel(glm::mix(vel,velTo, tgp.speedToPm.z));
			}

		}
		if (clear)		rb->inTriggerRbs.clear();
		//ndYao->rb0->addRotationToMatOnNode_MatRttResetXZ(ndYao->rb0->getNodeTransform(), 10000, 0, 0);
		//ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(ndYao->rb0->getNodeTransform(), 10000, 0, 0);
		//ndLeg1L->rb0->addRotationToMatOnNode_MatRttResetXZ(ndYao->rb0->getNodeTransform(), 1000, 0, 0);
		//ndLeg1R->rb0->addRotationToMatOnNode_MatRttResetXZ(ndYao->rb0->getNodeTransform(), 1000, 0, 0);
		//return;
		//if (yp.y<20 && yp.x>-20 && yp.x < 0 && yp.z > 0 && yp.z < 100)
		//{
		//	if (yp.z < 6)
		//	{
		//		ndUpper2->rb0->addLinearVel({ 0, 0,-30 });
		//		ndLower->rb0->addLinearVel({ 0, 0,60 });
		//	}
		//	else {
		//		Pmx->addBodyVel({ 0, yp.z * yp.z / 100.f,100 + yp.z * yp.z / 100.f }, 1);
		//		ndUpper2->rb0->addLinearVel({ 0, 0,-300 });
		//		ndLower->rb0->addLinearVel({ 0, 0,200 });
		//	}
		//}

	}
	//return;
	//if (!(ndRbRoot && ndRbRoot->rb0 && isAiCharacter())) return;

	if (ndYao->phyAnim && Vmd && rd.stage < 0) {
		//if (ndFootL->rb0->vel.y < -0.2f)	rotateRbDir(ndFootL->rb0, vec3(0, -1, 0), vec3(0, -1, 0), 10000.f);
		//if (ndFootR->rb0->vel.y < -0.2f)	rotateRbDir(ndFootR->rb0, vec3(0, -1, 0), vec3(0, -1, 0), 10000.f);
 
		//ndFootL->rb0->addRotationToMatOnNode_MatRttResetXZ(10000);
		//ndFootR->rb0->addRotationToMatOnNode_MatRttResetXZ(10000);
		return;
	}
	{
		if (phyStand) {
			if (!mmdPhyCtr) {
				MmdPhysicsController::Config config;
				config.baseRttMul = 1200.0f;
				config.maxAdjustSpeed = 8.0f;
				mmdPhyCtr = new MmdPhysicsController(config, this, ndYao, ndFootL, ndFootR, ndHandL, ndHandR, Pmx->yaoPos);
				mmdPhyCtr->addBehavior(std::make_unique<MmdPhysicsController::FootAdjustStrategy>());
			}
			mmdPhyCtr->update(0, phyStepTime);
			mmdPhyCtr->update(1, phyStepTime);

		//{
		//	footOnGround(ndFootL);
		//	footOnGround(ndFootR);
		//}
		float headR = (ndHead->mGlobalInit[3].y - ndHead->GetParent()->mGlobalInit[3].y) * ndHead->absScale.y;
		auto headOnGroundd = [=](saba::MMDNode* nd) {
			if (nd->rb0->pos.y < headR * (JUMP_ON_GROUND ? 30 : 6) && nd->rb0->vel.y < -6.0f * ndHead->absScale.y)
			{
				float sc = interpAtoB(nd->rb0->vel.y, -10, 0.75, -2, 0.96);
				//DP(("HVY y=%f  vy=%f * sc=%f", nd->rb0->pos.y,nd->rb0->vel.y,sc));
				nd->scaleVel(sc, 1, 2, 1.f);
				float3 fwp = nd->rb0->pos; fwp.y -= headR; fwp.x += ualib::UaRandm1to1() * headR * 2; fwp.z += ualib::UaRandm1to1() * headR * 2;
				MMDFW(1, "airFw", fwp, vec3(0, core::clamp(nd->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(1, 1, 1, 1));
			}
			//else if (abs(nd->rb0->vel.y)>0.01f) {	DP(("H   y=%f  vy=%f  ", nd->rb0->pos.y, nd->rb0->vel.y ));	}
			};
		headOnGroundd(ndHead);

		float handH = (ndHead->mGlobalInit[3].y) * ndHead->absScale.y;
		auto handOnGround = [=](saba::MMDNode* nd) {
			if (nd->rb0->vel.y * MMDPhysics::phyTimeMul < -3.0f * ndHead->absScale.y && nd->rb0->pos.y / -nd->rb0->vel.y < 0.75f)
			{
				float sc = interpAtoB(nd->rb0->vel.y, -60, 3, -2, .1) * 2;
				auto pos = nd->rb0->pos; pos.y -= headR * 6 * ndHead->absScale.y;
				ndHandL->rb0->addLinearVelToPos(pos, sc * (9 / std::max(0.01f, ndHandL->rb0->pos.y)));// MMDFW(1, "airFw", ndHandL->rb0->pos, vec3(0, core::clamp(ndHandL->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
				rotateRbDir(ndHandL->rb0, vec3(-1, -1, 0), vec3(0, -1, 0), 100000.f);
				ndHandR->rb0->addLinearVelToPos(pos, sc * (9 / std::max(0.01f, ndHandR->rb0->pos.y)));// MMDFW(1, "airFw", ndHandR->rb0->pos, vec3(0, core::clamp(ndHandR->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
				rotateRbDir(ndHandR->rb0, vec3(1, -1, 0), vec3(0, -1, 0), 100000.f);
				if (ndArm1L->rb0) ndArm1L->rb0->addLinearVelToPos(pos, sc / 2 * (9 / std::max(0.01f, ndArm1L->rb0->pos.y)));// MMDFW(1, "airFw", ndHandL->rb0->pos, vec3(0, core::clamp(ndHandL->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
				if (ndArm1R->rb0) ndArm1R->rb0->addLinearVelToPos(pos, sc / 2 * (9 / std::max(0.01f, ndArm1R->rb0->pos.y)));// MMDFW(1, "airFw", ndHandR->rb0->pos, vec3(0, core::clamp(ndHandR->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
			}
			//else if (abs(nd->rb0->vel.y)>0.01f) {	DP(("H   y=%f  vy=%f  ", nd->rb0->pos.y, nd->rb0->vel.y ));	}
			};
		handOnGround(ndHead);
		}

#if 	JUMP_ON_GROUND
		auto headOnGround3 = [=](saba::MMDNode* nd) {
			//DP(("headOnGround3 %d %f",objId, nd->rb0->pos.y / -nd->rb0->getLinearVel().y));
			if (ndFootL->rb0 && nd->rb0->vel.y < -1.0f * ndHead->absScale.y && nd->rb0->pos.y / -nd->rb0->vel.y < 2.75f)
			{

				ndFootL->rb0->addLinearVel({ 0,-3,0 });// MMDFW(1, "airFw", ndHandL->rb0->pos, vec3(0, core::clamp(ndHandL->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
				ndFootR->rb0->addLinearVel({ 0,-3,0 });// MMDFW(1, "airFw", ndHandR->rb0->pos, vec3(0, core::clamp(ndHandR->rb0->vel.y * 2 - 6, -90.f, -10.f), 0), SColorf(.1, .5, 1, 1));
			}
			//else if (abs(nd->rb0->vel.y)>0.01f) {	DP(("H   y=%f  vy=%f  ", nd->rb0->pos.y, nd->rb0->vel.y ));	}
			};
		headOnGround3(ndYao);
#endif
	}
}



void irr::scene::IrrSaba::actVoiceFx(int chId, int fx, int objKey, std::wstring  txt, float wordMul)
{
	if (chId >= 16) return;
	if (chId < Pm.mmd->mdplr.mmdCount) {
		//cd.aFxCD = 0;
		chId = std::min(8 + chId, 15);
	}
	auto& cd = Pm.mmd->mdplr.chd[chId];
	cd.ch = chId;
	cd.aFxId = fx;
	cd.curWordMul = wordMul;
	assert(wordMul > 0.01f);
	cd.act = 1; cd.setLyric = txt; cd.objKey = objKey;
	cd.lyricToLip = MOUTH_FX_ANIM;

	setFxMpCallback(chId);
	DPWCS((L"ActFX ch %d fx %d key=%d txt=%s", chId, fx, objKey, txt.c_str()));

}


void irr::scene::IrrSaba::appendLipMorph(std::string txt, float dur)
{
	saba::MMDMorph* mp{};
	int waitN = 0;
	for (auto c : txt) {
		int n = 0;
		bool zero = false;
		switch (c) {
		case 'a':case 'r':  mp = mpA; break;
		case 'e':  mp = mpE; break;
		case 'i':case 's':case 't': case 'y': mp = mpI; break;
		case 'o':  mp = mpO; break;
		case 'u':
		case 'w':case 'f': case 'v': mp = mpU; break;
			// case 'w':  GET_CONTROLER(_w);
		case 'n':  mp = mpN; break;
		case 'm':case 'b':case 'p': zero = true;
		default:;
		}
		DPWCS((L"FXMP %s", mp ? mp->GetNameW().c_str() : L"<null>"));
		if (zero && lastMp) {
			Pmx->GetMorphManager()->resetAll();
			lastMp->aaUpdate(99.f);
			n = 1;
		}
		else if (mp)
		{
			if (lastMp && lastMp != mp)
				lastMp->setLeft(waitN / 30.f);
			mp->appendMorph(1.1f, waitN / 30.f, dur); lastMp = mp;
			n = 3;

		}
		waitN += n;
	}
}

void irr::scene::IrrSaba::lookOnMousePos(int viewId)
{
	float my = float(Ctx->gd.mouseY);
	if (viewId == 1) {
		my = my - Ctx->gd.scrHeight  ;
		if (my < 0) return;
	}
	auto mdt = ImGui::GetIO().MouseDelta;
	vec3 rtt(((0.5f - my / Ctx->gd.scrHeight)) * piFloat,
		(float(Ctx->gd.mouseX) / Ctx->gd.scrWidth - 0.5f) * piFloat, 0);	//DP(("rtt %f,%f",rtt.x,rtt.y));
	//lockOnSb->ndHead->rb0->setAngVelToRotateOnNode(lockOnSb->ndUpper2->rb0->getNodeTransform()*mat4(quat(rtt)), 1000);
	ndHead->rb0->addTorqueLocal({ -rtt.x * 30,-rtt.y * 15,0.f });
	ndUpper2->rb0->addTorqueLocal({ -rtt.x * 100,-rtt.y * 20,0.f });
}

void irr::scene::IrrSaba::lookOnVelocity()
{
	if (!canLookOnVelocity) return;
	auto vel = Pmx->getRb(0)->getLinearVel();
	float lenvel = glm::length(vel);
	if (lenvel > 3.f &&  gPhyTime- lastTimeSetVelP2P>2.f)
	{
		DP(("rb0vel %f,%f,%f len=%f",vel.x,vel.y,vel.z, lenvel));
		auto dir = glm::normalize(vel);

		ndYao->rb0->scaleVel(0.f, 2);
		ndYao->rb0->addRotationToFaceDir(dir, vec3(-piFloat / 2, 0, 0), Ctx->gd.lookOnVel * 1000.f);
			//addRotationToMatOnNode(mat4(qr), Ctx->gd.lookOnVel*1000.f);
		ndHead->rb0->scaleVel(0.0f, 2); //ndUpper2->rb0->scaleVel(0.f, 2);
		ndHead->rb0->setAngVelToPos(ndHead->rb0->getPosition()+ dir *100.f, Ctx->gd.lookOnVel* std::clamp(lenvel,10.f,50.f)*5.0f);
		ndUpper2->rb0->setAngVelToPos(ndUpper2->rb0->getPosition() + dir * 100.f, Ctx->gd.lookOnVel * std::clamp(lenvel, 10.f, 50.f) * 1.f);

	}
}


void irr::scene::IrrSaba::reloadTex(io::path tex)
{
	if (!texQun) return;
	IImage* img, * t = Driver->createImageFromFile(tex);

	if (t->getDimension() != texQun->getSize()) {
		img = Driver->createImage(ECF_A8R8G8B8, texQun->getSize());
		t->copyToScalingBoxFilter(img);
		t->drop();
		t = img;
	}
	PITex tx = Driver->addTexture("a", t);
	tx->copyTo(texQun);
	Driver->removeTexture(tx);
	t->drop();
}
void irr::scene::IrrSaba::UpdateEqvData(const EqvUpdateParam& pm)
{
	if (!isAiCharacter()) return;
	//if (Eqv->CurWavePeak>0.1 && Eqv->CurWavePeak > Eqv->LastWavePeak * 1.23f)
	//phyAct(3700000 * ((Eqv->CurWavePeak - Eqv->LastWavePeak)));
	float f = Eqv->CurWavePeak / 6;// 6 * std::max(-0.1f, ((Eqv->CurWavePeak) - 0.2f));
	for (int i = 0; i < EQ_MAX_BANDS; i++) bandv[i] = pm.eqv->bvd->val[i];
	eqvBandCount = pm.eqv->bvd->count;;
	if (itemIdx == 0) {
		for (auto& bp : Pom->phyObjs) {
			auto& b = *bp; if (!b.rb->GetActivation()) continue;
			int id = b.pm.bandId % eqvBandCount;
			//id = int(b.rb->getPosition().x + pm.eqv->bvd->count-2) * pm.eqv->bvd->count / 60  %pm.eqv->bvd->count;



#if 0
			float v = pm.eqv->bvd->val[id]; f = core::clamp(v * v * v * 0.001f, 0.f, 700.f);	//DP(("Force %d %9.3f",id,  f));
			if (b.rb->getPosition().y < 10.f) {
				b.rb->setLinearVel(vec3{ 0,f * 10.f,0 } + (ndYao->rbPos() - vec3(b.pos)) * f * 0.3f);
				if (f > 300)	b.resetStatus();
			}
#else
	//DP(("Force %d %9.3f", id, f));
			float val = pm.eqv->bvd->val[id];
			float dv = pm.eqv->bvd->val[id] - pm.eqv->lastVals[id];
			float upRatio = dv / (pm.eqv->lastVals[id] + 0.00001f);
			auto sb = b.sb;
			//if (!sb) {
			//	float y = val ; y = upRatio>0.3f && y>1?pow(y,0.5)*1 :0 ;
			//	if (b.hitGroundCC && y > 0) {
			//		b.rb->addLinearVel((ndYao->rb0->getPosition() + vec3(0, val / 1000, 0) - b.rb->getPosition()) * vec3(1, 10, 1) * y *0.5f);
			//		charAtk.reset(); b.resetStatus();
			//	}
			//} else
			if (sb && sb->ndUpper2)
			{
				//sb->Pmx->smaSetAll(std::max(0.f,1-val *1.5f/ 100.f));

				if (upRatio > 1.0f && val > 10.f) {
					sb->Pmx->smaSetAllDec(1);
					Eqv->setCurPtrFwId(Eqv->findPtrFwIdx(1, "soundFwLo"), 1);
					sb->lauchAllVtxFw = 1;
				}
				float handF = 1.f;
				//sb->ndHandL->rb0->addLinearVel( (ndHandL->rbPos() - vec3(b.pos)) * f * handF);
				//sb->ndHandR->rb0->addLinearVel( (ndHandR->rbPos() - vec3(b.pos)) * f * handF);

				auto p = sb->ndUpper2->rb0->getPosition(); p.y = val * 0.17f;
				bool up = p.y > sb->ndUpper2->rb0->getPosition().y - 6;
#if 1
				//auto bandpos = vec3((id - 8.f + 0.5f) * 6.f, 0, 0);
				//p = bandpos; p.y = std::min(16.f, 2 + val * 0.6f);
				//float dis = sb->ndUpper2->disTo(p);
				if (MMD_BAND_EQV) {
					addRb2NdPosForce(sb->ndHead, 0.1f, 0.3f, 0.1f, 1);
					addRb2NdPosForce(sb->ndUpper2, 0.5f, 0.5f, 0.5f, 1);

					auto yp = sb->ndYao->rb0->getPosition(); auto dir = (sb->ndYao->getRbAnimGlobalPos() - yp); dir.y = 0;
					sb->ndYao->rb0->addLinearVel(dir * (centerForceMul * 100.f)); //CENTER FORCE
				}
				//if (dis>2)				sb->ndUpper2->rb0->setLinearVelToPos(p, (up ? 20 : 6)*dis);//sb->ndYao->rb0->setLinearVelToPos(p-vec3(0,3,0), up ? 100 : 60);
				sb->ndHandL->rb0->addLinearVel((p + vec3(6, 20, 10) - vec3(b.pos)) * f * 100.6f);
				sb->ndHandR->rb0->addLinearVel((p + vec3(-6, 20, 10) - vec3(b.pos)) * f * 100.6f);
				matrix4 m; m.setRotationDegrees({ 10, 0, 0 });
				//sb->ndUpper2->rb0->setRotateTo(mat4(m));
				if (up) if (MMD_BAND_EQV) sb->Pmx->addBodyVel(vec3(0, 1 + f, -(f + 0.3f)) * f * 20.1f);
				else  sb->Pmx->addBodyVel(vec3(0, 1 + f, 0.1) * f * 100.1f);
				if (MMD_BAND_EQV) {
					sb->ndFootL->GetParent()->rb0->addLinearVel(vec3(0, 1 + f, -(f + 1.0f)) * f * 960.1f);
					sb->ndFootR->rb0->addLinearVel(vec3(0, 0.6, -1) * f * 360.1f);
				}
				//else  sb->ndFootL->rb0->addLinearVel(vec3(0, 1 + f, -(f + 0.3f)) * f * 60.1f);
				//sb->ndFootR->rb0->addLinearVel(vec3(0, 2, -1) * f * 10.1f);


				//MMDFWID(Eqv->getCurPtrFwId(1), sb->ndYao->getGlobalPos(), vec3(0, f * 10, 0), SColorf(0.5, 1.0, 0.1, 1));
				//if (up) MMDFW(1,"hitFw", sb->ndUpper->rbPos(), vec3(0, -f * 100, 0), SColorf(1, 1, 1, 1));
				//if (up) MMDFW(1, "hitFw", sb->ndFootL->rbPos(), mat3(sb->ndFootL->GetGlobalTransform())* vec3(0, -f * 200, 0), SColorf(1, 1, 1, 1));
#endif

				//if (f > 3)	b.resetStatus();
			}
#endif
		}
	}
	if (Eqv->CurWavePeak - Eqv->LastWavePeak > -0.5f) {
		float d = Eqv->CurWavePeak - Eqv->LastWavePeak;
		//ndFootL->rb0->addTorqueLocal(vec3(-10000 * d, 0, 0));
		//ndFootR->rb0->addTorqueLocal(vec3(-10000 * d, 0, 0));

		if (ctrForce > 0) {
			Pmx->addBodyVel(vec3(0, 1000 * glm::clamp(d, -0.02f, .02f), 0));
		}
		//sb->ndUpper->rb0->addLinearVel(  ( vec3((id - 8) * 3.f, 0, 0)- vec3(b.pos)) * f * 0.1f);

		//sb->ndUpper2->rb0->setLinearVel(vec3{ 0,f *30.f,0 } - (ndYao->rbPos() - vec3(b.pos)) * f * 0.0003f);
	}
	if (rcvEqvData == 10)
	{
		for (int i = 0; i < Pmx->GetPhysicsManager()->sortedRBs.size(); i++)
		{
			auto& rb = *Pmx->GetPhysicsManager()->sortedRBs[i];
			int id = i % Eqv->eqvBandCount;
			float val = pm.eqv->bvd->val[id];
			if (rb.Pm.masLvl < 3)
				rb.addLinearVel(vec3(0, val * 3, 0));
		}
	}




	//phyFoot((Eqv->CurWavePeak-0.03)*10000, 20);

}

void irr::scene::IrrSaba::phyForceOnNode(const phyForceParam& pm)
{
	auto node = pm.node;
	bool isCenter = false;
	if (!pm.node->rb0) {
		node = Pmx->GetNodeManager()->GetMMDNode(Pmx->rootRbBoneIdx);
		isCenter = true;
	}
#if 1

	glm::mat4 m;
	//bt.getOpenGLMatrix(&m[0][0]);
	m = node->GetGlobalTransform();
	glm::vec3 scale;
	glm::quat rotation;
	glm::vec3 translation;
	glm::vec3 skew;
	glm::vec4 perspective;
	glm::decompose(m, scale, rotation, translation, skew, perspective);
	float fmuly = 5.f  , fmulxz = 1.f  ;
#if HAS_AR_EDITOR
	float cy = 19.f;//Pmx->rbRootY
	if (translation.y < cy && (node == ndYao || isCenter)) {
		float r = std::min((cy - translation.y) / 8 + 0.1f, 1.f);
		rotation = glm::slerp(rotation, glm::quat(1, 0, 0, 0), r);
	}
	else
		rotation = glm::slerp(rotation, glm::quat(1, 0, 0, 0), 0.1f);
#else
	bursting = 1;
#endif
	m = glm::translate(glm::mat4(1), translation)
		* glm::mat4_cast(rotation);

	glm::vec3 dir{ 0,pm.fY,0 };  dir = rotation * dir;
	//node->rb0->SetCoMTransform(m);

	auto nd = node;
	for (int i = 0; i < pm.fwSubNode; i++) {
		if (nd->GetChild()) nd = nd->GetChild();
	}
	glm::mat4 mps = glm::translate(glm::mat4(1), { 0,1,0 });
	mps = m * mps;
	vector3df pos = glm::vec3(mps[3]), dirI = -dir;
	mmdBaseMat.transformVect(pos);
	mmdBaseMat.transformVect(dirI);
	dirI = dirI.normalize() * core::clamp(pm.fY / 1000, 500.f, 2500.f);
	if (bursting) {
		node->rb0->addForce({ dir.x * fmulxz, dir.y * fmuly, dir.z * fmulxz });
		phyFwHue += 0.6;
		SColorHSL hsl(phyFwHue + pm.hueAdd, 100, 50);
		SColor sc = hsl.toSColor(); //sc.setAlpha(80);
		if (JET_FW) Eqv->LaunchFw3D(pos, Eqv->getFwIdxByFwIdStr(1, pm.fw), dirI, sc);
	}
#endif
}

void irr::scene::IrrSaba::togglePhyDebugVisual()
{
	Pmx->GetMMDPhysics()->senPhyDbgVisual(!MMDPhysics::phyDbgView);
#if SABA_USE_PHYSX

#else
	if (!Dg) {
		Dg = new UaBtDebugDraw();
		mmdPhysics->GetDynamicsWorld()->setDebugDrawer(Dg);
	}
	Dg->setDebugMode((phyDbgView) ? 1 : 0);
#endif

}


// ================= PHYSICS - ===================



void memcpy_64bit(void* dest, const void* src, size_t len)
{
	const uint64_t* s = (uint64_t*)src;	uint64_t* d = (uint64_t*)dest;
	while (len--) *d++ = *s++;
}
void memcpy_128bit_u(void* dest, const void* src, size_t len)
{
	const __m128i* s = (__m128i*)src;	__m128i* d = (__m128i*)dest;
	while (len--) _mm_storeu_si128(d++, _mm_lddqu_si128(s++));
}
void irr::scene::MmdCompute::runCS(saba::MMDModel* model)
{
	if (!gpuCamTrnasform) return;

	PMXModel* pmx = static_cast<PMXModel*>(model);

	//CPU_COUNT_B(GPU);
	//memcpy_64bit(mCbMats.mapped, pmx->get_transforms().data(), pmx->get_transforms().size() * sizeof(glm::mat4)/sizeof(uint64_t));
	//memcpy_128bit_u(mCbMats.mapped, pmx->get_transforms().data(), pmx->get_transforms().size() * sizeof(__m128i));

	VkDeviceSize mapsize = 64 + pmx->get_transforms().size() * sizeof(glm::mat4);
	VK_CHECK_RESULT(mCbMats.map(mapsize));
	PmxCB* pcb = (PmxCB*)mCbMats.mapped;
	pcb->vtxCount = vtxCount;
	pcb->nodeCount = pmx->get_transforms().size();
	memcpy(&pcb->transforms[0], pmx->get_transforms().data(), pmx->get_transforms().size() * sizeof(glm::mat4));

	mCbMats.unmap();

	int mpMin, mpMax;
	mpMin = (last_morphVtxMin < pmx->morphVtxMin) ? last_morphVtxMin : pmx->morphVtxMin;
	mpMax = (last_morphVtxMax > pmx->morphVtxMax) ? last_morphVtxMax : pmx->morphVtxMax;
	if (mpMin <= mpMax)
	{
		//CPU_COUNT_B(mup);
#if 1 //release vers about 5x faster even updates
		int size = (mpMax - mpMin + 1) * sizeof(glm::vec4);
		mVbMorph.map(size, mpMin * sizeof(glm::vec4)); //		mVbMorph.map();
		glm::vec4* pmp = (glm::vec4*)mVbMorph.mapped;
		glm::vec4* src = (glm::vec4*)pmx->get_morphPositions().data();
		memcpy(mVbMorph.mapped, src + mpMin, size);//memcpy(pmp + pmx->morphVtxMin, src + pmx->morphVtxMin, (pmx->morphVtxMax - pmx->morphVtxMin + 1) * sizeof(glm::vec4));  //pmx->get_morphPositions().size() );

#else
		mVbMorph.map(); glm::vec4* pmp = (glm::vec4*)mVbMorph.mapped; glm::vec4* src = (glm::vec4*)pmx->get_morphPositions().data();
		memcpy(pmp, src, pmx->get_morphPositions().size() * sizeof(glm::vec4));
#endif
		mVbMorph.unmap();
		//DP(("MORPH %d %d = %d ", mpMin, mpMax, mpMax - mpMin + 1));
		//CPU_COUNT_E(mup);
		last_morphVtxMin = pmx->morphVtxMin;
		last_morphVtxMax = pmx->morphVtxMax;
	}

	vkWaitForFences(Device, 1, &fence, VK_TRUE, UINT64_MAX);
	vkResetFences(Device, 1, &fence);
	VkSubmitInfo computeSubmitInfo = vks::initializers::submitInfo();
	computeSubmitInfo.commandBufferCount = 1;
	computeSubmitInfo.pCommandBuffers = &vkCmb;
	VK_CHECK_RESULT(vkQueueSubmit(Driver->getComputeQueue(), 1, &computeSubmitInfo, fence));
	//CPU_COUNT_E(GPU);
#if 0//def _DEBUG
	if (pmx->gpuVtxPtr) {
		auto pb = mVbPosNorm->lock(true);
		memcpy(pn, pb, pmx->GetVertexCount() * sizeof(PosNorm));
		mVbPosNorm->unlock();
		const float3& pos = *pmx->GetUpdatePositions();
		DP(("gpu %f,%f,%f\ncpu %f,%f,%f", pn[0].pos.x, pn[0].pos.y, pn[0].pos.z, pos.x, pos.y, pos.z));
		{
			S3DVertex* vtx = (S3DVertex*)pmx->gpuVtxPtr;
			int vtxCount = pmx->GetVertexCount();
			auto vi = pmx->get_vertexBoneInfos();
			for (int i = 0; i < vtxCount; i++)
			{
				vtx->Pos.set(pn[i].pos.x, pn[i].pos.y, pn[i].pos.z);
				vtx->Normal.set(pn[i].norm.x, pn[i].norm.y, pn[i].norm.z);
				vtx->TCoords.set(pn[i].u, pn[i].v);
				vtx++;
				//pn[i].norm = glm::vec4(norm[i], 1);
			}
		}
	}
#endif
}
#if !USE_PHYSX
void irr::scene::UaBtDebugDraw::drawLine(const btVector3& from, const btVector3& to, const btVector3& color)
{
	//DP(("UaBtDebugDraw::drawLine1111111111111"));
	irr::video::S3DVertex s; s.Color = irr::video::SColor(255, 255 * color.m_floats[0], 255 * color.m_floats[1], 255 * color.m_floats[2]);
	s.Pos = *(irr::core::vector3df*)&from;
	//tr.transformVect(s.Pos);
	lineVec.emplace_back(s);
	s.Pos = *(irr::core::vector3df*)&to;
	//tr.transformVect(s.Pos);
	lineVec.emplace_back(s);
}
#endif
#if MMD_OPEN_POSE
void irr::scene::IrrSaba::openPoseDraw(PITex rt)
{
	MmdNodeHandler_OpenPose* oph = (MmdNodeHandler_OpenPose*)ophdl.get();
	oph->renderOpenPose(rt);
}
#endif
#if 0
void irr::scene::IrrSaba::copyNodeStateFrom(irr::scene::IrrSaba* sb)
{
	auto rbs = sb->Pmx->GetPhysicsManager()->GetRigidBodys();
	auto rbt = Pmx->GetPhysicsManager()->GetRigidBodys();
	if (rbs->size() != rbt->size()) return;
	for (int i = 0; i < rbs->size(); i++) {
		auto& s = rbs->at(i), & t = rbt->at(i);
		//t->setGlmMat(s->GetTransform());
		auto trb = t->GetRigidBody(), srb = s->GetRigidBody();
		trb->setCenterOfMassTransform(srb->getCenterOfMassTransform());
		t->ResetRigidbody(nullptr);
	}
}
#endif

void irr::scene::sbFw2D(const char* name, glm::vec3 pos, glm::vec3 vec, irr::video::SColor col)
{
#ifdef _DEBUG
	Sb0->mmdFw(2, name, pos, vec, col);
#endif
}
void irr::scene::sbFw2(const char* name, glm::vec3 pos, glm::vec3 vec, irr::video::SColor col)
{

	Sb0->mmdFw(2, name, pos, vec, col);

}
void irr::scene::sbFw2LineD(std::string_view idStr, glm::vec3 pos, glm::vec3 pos2, video::SColor color, float step, float startStepRate)
{
#ifdef _DEBUG
	Sb0->mmdFwLine(2, idStr, pos, pos2, color, step, startStepRate);
#endif
}
void irr::scene::sbFw2Line(std::string_view idStr, glm::vec3 pos, glm::vec3 pos2, video::SColor color, float step, float startStepRate)
{

	Sb0->mmdFwLine(2, idStr, pos, pos2, color, step, startStepRate);

}


void irr::scene::IrrSaba::startSbMPA(MpaLoadJsonParam lf)
{
	if (!sbMpa) {
		sbMpa = new  MpaManager(mmd);

	}
	std::vector<int> sbs = { 0,1,2,3,4,5,6,7 };
	int id = getItemIdx();
	if (id>=0 && id<8)
	std::swap(sbs[id], sbs[0]);
	else 
		sbs[0] = -1, sbs[1]=0;
	sbMpa->playMPA(lf, sbs);

}

void  irr::scene::rotateRbDir(saba::MMDRigidBody* rb, const glm::vec3 front, glm::vec3 dirTgtNorm, float mul)
{
#ifdef _DEBUG
	//rb->scaleVel(0.f, 2);
	dirTgtNorm = fastNormalize(dirTgtNorm);
	glm::vec3 dirSrc = glm::fastNormalize(glh::matRotateVec(rb->GetTransform(), front));
	sbFw2Line("pt", rb->getPosition(), rb->getPosition() + dirTgtNorm * 10.5f, 0xFCFF8000, 60);
	sbFw2Line("pt", rb->getPosition(), rb->getPosition() + dirSrc * 10.5f, 0xFC00FF80, 60);
	rb->rotateFromDirToDir(dirSrc, dirTgtNorm, mul);
#else
	rb->rotateLocalDirTo(front, dirTgtNorm, mul);
#endif
}





