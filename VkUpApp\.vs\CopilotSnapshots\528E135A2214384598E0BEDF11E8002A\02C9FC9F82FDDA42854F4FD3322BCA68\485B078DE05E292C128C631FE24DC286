﻿#include "appGlobal.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkDriver.h"
#include "../../../UaIrrlicht/include/IFileSystem.h"
#include "VulkanRenderer/VkVertexDeclaration.h"
#include "VulkanRenderer/vulkanRenderPass.h"
#include "VulkanRenderer/VkShaderMan/VkFxBase.h"
#include "VulkanRenderer/VkShaderMan/VkFxDescriptorSetManager.h"

// Compiled shader headers
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidEllipsoid_vert_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidEllipsoid_frag_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidDensity_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidCovariance_comp_SpirV.h"

namespace irr
{
namespace video
{

using namespace VkFxUtil;

// Fluid shader bytecode declarations
static const ShaderBytecode Sbc_VkFluid__VkFluidEllipsoid_vert = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidEllipsoid_vert);
static const ShaderBytecode Sbc_VkFluid__VkFluidEllipsoid_frag = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidEllipsoid_frag);
static const ShaderBytecode Sbc_VkFluid__VkFluidDensity_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidDensity_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidCovariance_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidCovariance_comp);

E_MATERIAL_TYPE MrVkFluid::s_materialType = EMT_TRANSPARENT_ALPHA_CHANNEL;

MrVkFluid::MrVkFluid(IVideoDriver* driver, io::IFileSystem* fileSystem)
    : VkMaterialRenderer(driver)
    , m_fileSystem(fileSystem)
    , m_particleDescriptorSet(VK_NULL_HANDLE)
    , m_ellipsoidDescriptorSet(VK_NULL_HANDLE)
    , m_volumetricDescriptorSet(VK_NULL_HANDLE)
    , m_raymarchingDescriptorSet(VK_NULL_HANDLE)
    , m_particleUpdateDescriptorSet(VK_NULL_HANDLE)
    , m_densityComputeDescriptorSet(VK_NULL_HANDLE)
    , m_covarianceDescriptorSet(VK_NULL_HANDLE)
    , m_graphicsDescriptorSetLayout(VK_NULL_HANDLE)
    , m_computeDescriptorSetLayout(VK_NULL_HANDLE)
    , m_densityDescriptorSetLayout(VK_NULL_HANDLE)
    , m_descriptorPool(VK_NULL_HANDLE)
    , m_linearSampler(VK_NULL_HANDLE)
    , m_nearestSampler(VK_NULL_HANDLE)
    , m_shadowSampler(VK_NULL_HANDLE)
    , m_graphicsPipelineLayout(VK_NULL_HANDLE)
    , m_computePipelineLayout(VK_NULL_HANDLE)
    , m_particlePipeline(VK_NULL_HANDLE)
    , m_ellipsoidPipeline(VK_NULL_HANDLE)
    , m_volumetricPipeline(VK_NULL_HANDLE)
    , m_raymarchingPipeline(VK_NULL_HANDLE)
    , m_particleUpdatePipeline(VK_NULL_HANDLE)
    , m_densityComputePipeline(VK_NULL_HANDLE)
    , m_covariancePipeline(VK_NULL_HANDLE)
    , m_particleUpdateCmdBuffer(VK_NULL_HANDLE)
    , m_densityComputeCmdBuffer(VK_NULL_HANDLE)
    , m_covarianceCmdBuffer(VK_NULL_HANDLE)
    , m_computeFence(VK_NULL_HANDLE)
    , m_currentPipeline(FRP_ELLIPSOIDS)
    , m_needsUpdate(true)
    , m_particleBuffer(nullptr)
    , m_densityBuffer(nullptr)
    , m_covarianceBuffer(nullptr)
    , m_gridBuffer(nullptr)
{
    if (m_fileSystem)
        m_fileSystem->grab();

    // Initialize uniform data
    memset(&m_uniforms, 0, sizeof(m_uniforms));
    memset(&m_computeParams, 0, sizeof(m_computeParams));
    memset(&m_densityParams, 0, sizeof(m_densityParams));

    // Set default values
    m_uniforms.mWorld.makeIdentity();
    m_uniforms.mView.makeIdentity();
    m_uniforms.mProjection.makeIdentity();
    m_uniforms.mViewInverse.makeIdentity();
    m_uniforms.cameraPos = float4(0.0f, 0.0f, 0.0f, 1.0f);
    m_uniforms.lightDirection = float4(0.0f, -1.0f, 0.0f, 0.0f);
    m_uniforms.lightColor = float4(1.0f, 1.0f, 1.0f, 1.0f);
    m_uniforms.fluidColor = float4(0.3f, 0.6f, 1.0f, 0.8f);
    m_uniforms.absorptionColor = float4(0.584f, 0.843f, 0.953f, 1.0f);
    m_uniforms.refractionIndex = 1.333f;
    m_uniforms.roughness = 0.1f;
    m_uniforms.metallic = 0.0f;
    m_uniforms.particleRadius = 0.08f;
    m_uniforms.normalSmoothness = 0.72f;
    m_uniforms.isoValue = 0.5f;

    initializeShaders();
    createUniformBuffers();
    createSamplers();
    createComputeCommandBuffers();
    setupDescriptorSets();
    recreatePipeline();
}

MrVkFluid::~MrVkFluid()
{
    VkDevice device = Driver->Device;

    // Destroy pipelines
    if (m_particlePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_particlePipeline, nullptr);
    }
    if (m_ellipsoidPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_ellipsoidPipeline, nullptr);
    }
    if (m_volumetricPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_volumetricPipeline, nullptr);
    }
    if (m_raymarchingPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_raymarchingPipeline, nullptr);
    }
    if (m_particleUpdatePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_particleUpdatePipeline, nullptr);
    }
    if (m_densityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_densityComputePipeline, nullptr);
    }
    if (m_covariancePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_covariancePipeline, nullptr);
    }

    // Destroy pipeline layouts
    if (m_graphicsPipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_graphicsPipelineLayout, nullptr);
    }
    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
    }

    // Destroy descriptor set layouts
    if (m_graphicsDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_graphicsDescriptorSetLayout, nullptr);
    }
    if (m_computeDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_computeDescriptorSetLayout, nullptr);
    }
    if (m_densityDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_densityDescriptorSetLayout, nullptr);
    }

    // Destroy descriptor pool
    if (m_descriptorPool != VK_NULL_HANDLE) {
        vkDestroyDescriptorPool(device, m_descriptorPool, nullptr);
    }

    // Destroy samplers
    if (m_linearSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_linearSampler, nullptr);
    }
    if (m_nearestSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_nearestSampler, nullptr);
    }
    if (m_shadowSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_shadowSampler, nullptr);
    }

    // Destroy compute fence
    if (m_computeFence != VK_NULL_HANDLE) {
        vkDestroyFence(device, m_computeFence, nullptr);
    }

    // Free command buffers
    if (m_particleUpdateCmdBuffer != VK_NULL_HANDLE ||
        m_densityComputeCmdBuffer != VK_NULL_HANDLE ||
        m_covarianceCmdBuffer != VK_NULL_HANDLE) {
        VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
        VkCommandBuffer cmdBuffers[] = { m_particleUpdateCmdBuffer, m_densityComputeCmdBuffer, m_covarianceCmdBuffer };
        vkFreeCommandBuffers(device, vkDriver->getComputeCommandPool(), 3, cmdBuffers);
    }

    // vks::Buffer objects are automatically cleaned up by their destructors

    if (m_fileSystem)
        m_fileSystem->drop();
}

bool MrVkFluid::OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId)
{
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    // Bind descriptor sets and pipeline based on current render mode
    VkCommandBuffer cmdBuffer = Driver->currentCmdBuffer();
    VkPipeline currentGraphicsPipeline = VK_NULL_HANDLE;
    VkDescriptorSet currentDescriptorSet = VK_NULL_HANDLE;

    switch (m_currentPipeline) {
        case FRP_PARTICLES:
            currentGraphicsPipeline = m_particlePipeline;
            currentDescriptorSet = m_particleDescriptorSet;
            break;
        case FRP_ELLIPSOIDS:
            currentGraphicsPipeline = m_ellipsoidPipeline;
            currentDescriptorSet = m_ellipsoidDescriptorSet;
            break;
        case FRP_VOLUMETRIC:
            currentGraphicsPipeline = m_volumetricPipeline;
            currentDescriptorSet = m_volumetricDescriptorSet;
            break;
        case FRP_RAYMARCHING:
            currentGraphicsPipeline = m_raymarchingPipeline;
            currentDescriptorSet = m_raymarchingDescriptorSet;
            break;
    }

    if (currentGraphicsPipeline != VK_NULL_HANDLE && currentDescriptorSet != VK_NULL_HANDLE) {
        vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, currentGraphicsPipeline);
        vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                               m_graphicsPipelineLayout, 0, 1, &currentDescriptorSet, 0, nullptr);
    }

    return true;
}

void MrVkFluid::OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                             bool resetAllRenderstates, IMaterialRendererServices* services)
{
    CurrentMaterial = material;
    services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
    m_needsUpdate = true;
}

void MrVkFluid::OnUnsetMaterial()
{
    // Nothing to do here
}

bool MrVkFluid::setVariable(const c8* name, const f32* floats, int count)
{
    // Handle custom shader variables
    if (strcmp(name, "time") == 0 && count >= 1) {
        setTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "deltaTime") == 0 && count >= 1) {
        setDeltaTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "particleRadius") == 0 && count >= 1) {
        setParticleRadius(floats[0]);
        return true;
    }
    else if (strcmp(name, "refractionIndex") == 0 && count >= 1) {
        setRefractionIndex(floats[0]);
        return true;
    }
    else if (strcmp(name, "isoValue") == 0 && count >= 1) {
        setIsoValue(floats[0]);
        return true;
    }

    return false;
}

const void* MrVkFluid::getShaderByteCode() const
{
    // Return ellipsoid vertex shader bytecode for pipeline creation
    return Sbc_VkFluid__VkFluidEllipsoid_vert.code;
}

u32 MrVkFluid::getShaderByteCodeSize() const
{
    // Return ellipsoid vertex shader bytecode size
    return Sbc_VkFluid__VkFluidEllipsoid_vert.s;
}

void MrVkFluid::cleanFrameCache()
{
    // Clean up per-frame resources if needed
}

void MrVkFluid::preSubmit()
{
    // Pre-submission tasks
}

void MrVkFluid::ReloadShaders()
{
    // Reload and recompile shaders
    initializeShaders();
    recreatePipeline();
}

// Material property setters
void MrVkFluid::setFluidColor(const SColor& color)
{
    m_uniforms.fluidColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        color.getAlpha() / 255.0f
    );
    m_needsUpdate = true;
}

void MrVkFluid::setAbsorptionColor(const core::vector3df& absorption)
{
    m_uniforms.absorptionColor = float4(absorption.X, absorption.Y, absorption.Z, 1.0f);
    m_needsUpdate = true;
}

void MrVkFluid::setContainerBounds(const core::aabbox3df& bounds)
{
    m_uniforms.containerMin = float4(bounds.MinEdge.X, bounds.MinEdge.Y, bounds.MinEdge.Z, 1.0f);
    m_uniforms.containerMax = float4(bounds.MaxEdge.X, bounds.MaxEdge.Y, bounds.MaxEdge.Z, 1.0f);
    m_needsUpdate = true;
}

void MrVkFluid::setCameraPosition(const core::vector3df& pos)
{
    m_uniforms.cameraPos = float4(pos.X, pos.Y, pos.Z, 1.0f);
    m_needsUpdate = true;
}

void MrVkFluid::setLightDirection(const core::vector3df& direction)
{
    core::vector3df normalizedDir = direction;
    normalizedDir.normalize();
    m_uniforms.lightDirection = float4(normalizedDir.X, normalizedDir.Y, normalizedDir.Z, 0.0f);
    m_needsUpdate = true;
}

void MrVkFluid::setLightColor(const SColor& color)
{
    m_uniforms.lightColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        color.getAlpha() / 255.0f
    );
    m_needsUpdate = true;
}

// Matrix updates
void MrVkFluid::setWorldMatrix(const core::matrix4& world)
{
    m_uniforms.mWorld = world;
    m_needsUpdate = true;
}

void MrVkFluid::setViewMatrix(const core::matrix4& view)
{
    m_uniforms.mView = view;
    // Compute view inverse for camera position calculations
    view.getInverse(m_uniforms.mViewInverse);
    m_needsUpdate = true;
}

void MrVkFluid::setProjectionMatrix(const core::matrix4& projection)
{
    m_uniforms.mProjection = projection;
    m_needsUpdate = true;
}

// Compute shader dispatch methods
void MrVkFluid::dispatchParticleUpdate(u32 particleCount)
{
    // This method would be used if we had SPH physics compute
    // Currently not needed since we use PhysX
}

void MrVkFluid::dispatchDensityComputation(u32 gridSizeX, u32 gridSizeY, u32 gridSizeZ)
{
    if (m_densityComputePipeline == VK_NULL_HANDLE || m_densityComputeCmdBuffer == VK_NULL_HANDLE)
        return;

    VkDevice device = Driver->Device;

    // Wait for previous compute operations
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    vkResetFences(device, 1, &m_computeFence);

    // Record compute commands
    VkCommandBufferBeginInfo beginInfo = {};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    beginInfo.flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT;

    vkBeginCommandBuffer(m_densityComputeCmdBuffer, &beginInfo);

    vkCmdBindPipeline(m_densityComputeCmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_densityComputePipeline);
    vkCmdBindDescriptorSets(m_densityComputeCmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_densityComputeDescriptorSet, 0, nullptr);

    // Dispatch with workgroups of 8x8x8
    u32 workGroupsX = (gridSizeX + 7) / 8;
    u32 workGroupsY = (gridSizeY + 7) / 8;
    u32 workGroupsZ = (gridSizeZ + 7) / 8;
    vkCmdDispatch(m_densityComputeCmdBuffer, workGroupsX, workGroupsY, workGroupsZ);

    vkEndCommandBuffer(m_densityComputeCmdBuffer);

    // Submit compute work
    VkSubmitInfo submitInfo = {};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_densityComputeCmdBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
}

void MrVkFluid::dispatchCovarianceComputation(u32 particleCount)
{
    if (m_covariancePipeline == VK_NULL_HANDLE || m_covarianceCmdBuffer == VK_NULL_HANDLE)
        return;

    VkDevice device = Driver->Device;

    // Wait for previous compute operations
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    vkResetFences(device, 1, &m_computeFence);

    // Record compute commands
    VkCommandBufferBeginInfo beginInfo = {};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    beginInfo.flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT;

    vkBeginCommandBuffer(m_covarianceCmdBuffer, &beginInfo);

    vkCmdBindPipeline(m_covarianceCmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_covariancePipeline);
    vkCmdBindDescriptorSets(m_covarianceCmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_covarianceDescriptorSet, 0, nullptr);

    // Dispatch with workgroups of 128 threads
    u32 workGroups = (particleCount + 127) / 128;
    vkCmdDispatch(m_covarianceCmdBuffer, workGroups, 1, 1);

    vkEndCommandBuffer(m_covarianceCmdBuffer);

    // Submit compute work
    VkSubmitInfo submitInfo = {};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_covarianceCmdBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
}

void MrVkFluid::waitForComputeCompletion()
{
    VkDevice device = Driver->Device;
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
}

// Buffer management
void MrVkFluid::setParticleBuffer(IHardwareBuffer* buffer)
{
    m_particleBuffer = buffer;
    
    if (buffer && m_ellipsoidDescriptorSet != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        
        // Update particle buffer binding in graphics descriptor set
        VkDescriptorBufferInfo bufferInfo = {};
        bufferInfo.buffer = static_cast<irr::video::VkHardwareBuffer*>(buffer)->getBufferResource();
        bufferInfo.offset = 0;
        bufferInfo.range = VK_WHOLE_SIZE;
        
        VkWriteDescriptorSet writeDescriptorSet = {};
        writeDescriptorSet.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writeDescriptorSet.dstSet = m_ellipsoidDescriptorSet;
        writeDescriptorSet.dstBinding = 1;
        writeDescriptorSet.dstArrayElement = 0;
        writeDescriptorSet.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        writeDescriptorSet.descriptorCount = 1;
        writeDescriptorSet.pBufferInfo = &bufferInfo;
        
        vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);
        
        // Also update compute descriptor sets if they use particle data
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            writeDescriptorSet.dstSet = m_densityComputeDescriptorSet;
            vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);
        }
        
        DP(("MrVkFluid particle buffer updated in descriptor sets"));
    }
}

void MrVkFluid::setDensityBuffer(IHardwareBuffer* buffer)
{
    m_densityBuffer = buffer;
    
    if (buffer && m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        
        // Update density field buffer binding in compute descriptor set
        VkDescriptorBufferInfo bufferInfo = {};
        bufferInfo.buffer = static_cast<irr::video::VkHardwareBuffer*>(buffer)->getBufferResource();
        bufferInfo.offset = 0;
        bufferInfo.range = VK_WHOLE_SIZE;
        
        VkWriteDescriptorSet writeDescriptorSet = {};
        writeDescriptorSet.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writeDescriptorSet.dstSet = m_densityComputeDescriptorSet;
        writeDescriptorSet.dstBinding = 2;
        writeDescriptorSet.dstArrayElement = 0;
        writeDescriptorSet.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        writeDescriptorSet.descriptorCount = 1;
        writeDescriptorSet.pBufferInfo = &bufferInfo;
        
        vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);
        
        DP(("MrVkFluid density buffer updated in descriptor sets"));
    }
}

void MrVkFluid::setCovarianceBuffer(IHardwareBuffer* buffer)
{
    m_covarianceBuffer = buffer;
    
    if (buffer && m_ellipsoidDescriptorSet != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        
        // Update covariance buffer binding in graphics descriptor set
        VkDescriptorBufferInfo bufferInfo = {};
        bufferInfo.buffer = static_cast<irr::video::VkHardwareBuffer*>(buffer)->getBufferResource();
        bufferInfo.offset = 0;
        bufferInfo.range = VK_WHOLE_SIZE;
        
        VkWriteDescriptorSet writeDescriptorSet = {};
        writeDescriptorSet.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writeDescriptorSet.dstSet = m_ellipsoidDescriptorSet;
        writeDescriptorSet.dstBinding = 2;
        writeDescriptorSet.dstArrayElement = 0;
        writeDescriptorSet.descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        writeDescriptorSet.descriptorCount = 1;
        writeDescriptorSet.pBufferInfo = &bufferInfo;
        
        vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);
        
        DP(("MrVkFluid covariance buffer updated in descriptor sets"));
    }
}

void MrVkFluid::setGridBuffer(IHardwareBuffer* buffer)
{
    m_gridBuffer = buffer;
    
    if (buffer) {
        // Grid buffer can be used for various purposes like spatial hashing
        // Update relevant descriptor sets as needed
        DP(("MrVkFluid grid buffer set"));
    }
}

void MrVkFluid::updateSimulationParams(const VkFluidComputeParams& params)
{
    m_computeParams = params;
    m_needsUpdate = true;
}

void MrVkFluid::updateDensityParams(const VkFluidDensityParams& params)
{
    m_densityParams = params;
    m_needsUpdate = true;
}

void MrVkFluid::setRenderPipeline(FluidRenderPipeline pipeline)
{
    m_currentPipeline = pipeline;
}

E_MATERIAL_TYPE MrVkFluid::getMaterialType()
{
    return s_materialType;
}

// Protected methods implementation

void MrVkFluid::initializeShaders()
{
    DP(("MrVkFluid::initializeShaders() - Loading fluid shaders from compiled bytecode"));
    
    try {
        // Load ellipsoid rendering shaders (primary mode) from compiled bytecode
        m_ellipsoidShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        
        // Load vertex and fragment shaders for ellipsoid rendering
        m_ellipsoidShader->createVS(Sbc_VkFluid__VkFluidEllipsoid_vert, "main");
        m_ellipsoidShader->createPS(Sbc_VkFluid__VkFluidEllipsoid_frag, "main");
        
        // Load density field compute shader
        m_densityComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_densityComputeShader->createCS(Sbc_VkFluid__VkFluidDensity_comp, "main");
        
        // Load covariance compute shader
        m_covarianceShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_covarianceShader->createCS(Sbc_VkFluid__VkFluidCovariance_comp, "main");
        
        // Optional shaders for other render modes (to be implemented later)
        // TODO: Implement particle, volumetric, and raymarching shaders
        
        DP(("MrVkFluid compiled shaders loaded successfully"));
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::initializeShaders(): %s", e.what()));
    }
}

        
        DP(("MrVkFluid shaders loaded successfully"));
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::initializeShaders(): %s", e.what()));
    }
}

void MrVkFluid::createUniformBuffers()
{
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    // Create uniform buffer for graphics shaders using vks::Buffer
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_uniformBuffer,
        sizeof(VkFluidUniforms)));

    // Create compute parameters buffer using vks::Buffer
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_computeParamsBuffer,
        sizeof(VkFluidComputeParams)));

    // Create density parameters buffer using vks::Buffer
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_densityParamsBuffer,
        sizeof(VkFluidDensityParams)));

    // Map buffers persistently for fast updates
    VK_CHECK_RESULT(m_uniformBuffer.map());
    VK_CHECK_RESULT(m_computeParamsBuffer.map());
    VK_CHECK_RESULT(m_densityParamsBuffer.map());
}

void MrVkFluid::updateUniformBuffers()
{
    // Update graphics uniform buffer using vks::Buffer
    // Since buffers are persistently mapped, we can directly copy data
    if (m_uniformBuffer.mapped) {
        memcpy(m_uniformBuffer.mapped, &m_uniforms, sizeof(VkFluidUniforms));
    }

    // Update compute parameters buffer
    if (m_computeParamsBuffer.mapped) {
        memcpy(m_computeParamsBuffer.mapped, &m_computeParams, sizeof(VkFluidComputeParams));
    }

    // Update density parameters buffer
    if (m_densityParamsBuffer.mapped) {
        memcpy(m_densityParamsBuffer.mapped, &m_densityParams, sizeof(VkFluidDensityParams));
    }
}

void MrVkFluid::setupDescriptorSets()
{
    DP(("MrVkFluid::setupDescriptorSets() - Creating descriptor layouts and sets"));
    
    VkDevice device = Driver->Device;
    
    try {
        // ===============================================================
        // Graphics Descriptor Set Layout (for ellipsoid rendering)
        // ===============================================================
        
        VkDescriptorSetLayoutBinding graphicsBindings[3] = {};
        
        // Binding 0: Uniform buffer (FluidUniforms)
        graphicsBindings[0].binding = 0;
        graphicsBindings[0].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
        graphicsBindings[0].descriptorCount = 1;
        graphicsBindings[0].stageFlags = VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT;
        
        // Binding 1: Particle buffer (storage buffer)
        graphicsBindings[1].binding = 1;
        graphicsBindings[1].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        graphicsBindings[1].descriptorCount = 1;
        graphicsBindings[1].stageFlags = VK_SHADER_STAGE_VERTEX_BIT;
        
        // Binding 2: Covariance buffer (storage buffer)
        graphicsBindings[2].binding = 2;
        graphicsBindings[2].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        graphicsBindings[2].descriptorCount = 1;
        graphicsBindings[2].stageFlags = VK_SHADER_STAGE_VERTEX_BIT;
        
        VkDescriptorSetLayoutCreateInfo graphicsLayoutInfo = {};
        graphicsLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
        graphicsLayoutInfo.bindingCount = 3;
        graphicsLayoutInfo.pBindings = graphicsBindings;
        
        VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &graphicsLayoutInfo, nullptr, &m_graphicsDescriptorSetLayout));
        
        // ===============================================================
        // Compute Descriptor Set Layout (for density computation)
        // ===============================================================
        
        VkDescriptorSetLayoutBinding computeBindings[3] = {};
        
        // Binding 0: Density computation parameters
        computeBindings[0].binding = 0;
        computeBindings[0].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
        computeBindings[0].descriptorCount = 1;
        computeBindings[0].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
        
        // Binding 1: Particle buffer (input)
        computeBindings[1].binding = 1;
        computeBindings[1].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        computeBindings[1].descriptorCount = 1;
        computeBindings[1].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
        
        // Binding 2: Density field buffer (output)
        computeBindings[2].binding = 2;
        computeBindings[2].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        computeBindings[2].descriptorCount = 1;
        computeBindings[2].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
        
        VkDescriptorSetLayoutCreateInfo computeLayoutInfo = {};
        computeLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
        computeLayoutInfo.bindingCount = 3;
        computeLayoutInfo.pBindings = computeBindings;
        
        VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &computeLayoutInfo, nullptr, &m_computeDescriptorSetLayout));
        
        // ===============================================================
        // Density Descriptor Set Layout (same as compute for now)
        // ===============================================================
        m_densityDescriptorSetLayout = m_computeDescriptorSetLayout;
        
        // ===============================================================
        // Create Descriptor Pool
        // ===============================================================
        
        VkDescriptorPoolSize poolSizes[] = {
            { VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 8 },    // Multiple uniform buffers
            { VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 12 },   // Multiple storage buffers
        };
        
        VkDescriptorPoolCreateInfo poolInfo = {};
        poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
        poolInfo.flags = VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT;
        poolInfo.poolSizeCount = 2;
        poolInfo.pPoolSizes = poolSizes;
        poolInfo.maxSets = 8;  // Graphics + compute sets
        
        VK_CHECK_RESULT(vkCreateDescriptorPool(device, &poolInfo, nullptr, &m_descriptorPool));
        
        // ===============================================================
        // Allocate Descriptor Sets
        // ===============================================================
        
        VkDescriptorSetLayout layouts[] = { 
            m_graphicsDescriptorSetLayout,    // Ellipsoid graphics
            m_computeDescriptorSetLayout,     // Density compute
            m_computeDescriptorSetLayout      // Covariance compute
        };
        
        VkDescriptorSetAllocateInfo allocInfo = {};
        allocInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
        allocInfo.descriptorPool = m_descriptorPool;
        allocInfo.descriptorSetCount = 3;
        allocInfo.pSetLayouts = layouts;
        
        VkDescriptorSet descriptorSets[3];
        VK_CHECK_RESULT(vkAllocateDescriptorSets(device, &allocInfo, descriptorSets));
        
        m_ellipsoidDescriptorSet = descriptorSets[0];
        m_densityComputeDescriptorSet = descriptorSets[1];
        m_covarianceDescriptorSet = descriptorSets[2];
        
        // ===============================================================
        // Update Descriptor Sets with Buffer Bindings
        // ===============================================================
        
        // Graphics descriptor set (ellipsoid rendering)
        VkDescriptorBufferInfo uniformBufferInfo = {};
        uniformBufferInfo.buffer = m_uniformBuffer.buffer;
        uniformBufferInfo.offset = 0;
        uniformBufferInfo.range = sizeof(VkFluidUniforms);
        
        VkWriteDescriptorSet uniformWrite = {};
        uniformWrite.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        uniformWrite.dstSet = m_ellipsoidDescriptorSet;
        uniformWrite.dstBinding = 0;
        uniformWrite.dstArrayElement = 0;
        uniformWrite.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
        uniformWrite.descriptorCount = 1;
        uniformWrite.pBufferInfo = &uniformBufferInfo;
        
        vkUpdateDescriptorSets(device, 1, &uniformWrite, 0, nullptr);
        
        // Compute descriptor set (density computation)
        VkDescriptorBufferInfo densityParamsInfo = {};
        densityParamsInfo.buffer = m_densityParamsBuffer.buffer;
        densityParamsInfo.offset = 0;
        densityParamsInfo.range = sizeof(VkFluidDensityParams);
        
        VkWriteDescriptorSet densityParamsWrite = {};
        densityParamsWrite.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        densityParamsWrite.dstSet = m_densityComputeDescriptorSet;
        densityParamsWrite.dstBinding = 0;
        densityParamsWrite.dstArrayElement = 0;
        densityParamsWrite.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
        densityParamsWrite.descriptorCount = 1;
        densityParamsWrite.pBufferInfo = &densityParamsInfo;
        
        vkUpdateDescriptorSets(device, 1, &densityParamsWrite, 0, nullptr);
        
        DP(("MrVkFluid descriptor sets created successfully"));
        
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::setupDescriptorSets(): %s", e.what()));
    }
}

void MrVkFluid::createComputeCommandBuffers()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Allocate compute command buffers
    VkCommandBufferAllocateInfo allocInfo = {};
    allocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
    allocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
    allocInfo.commandPool = vkDriver->getComputeCommandPool();
    allocInfo.commandBufferCount = 3;

    VkCommandBuffer cmdBuffers[3];
    vkAllocateCommandBuffers(device, &allocInfo, cmdBuffers);

    m_particleUpdateCmdBuffer = cmdBuffers[0];
    m_densityComputeCmdBuffer = cmdBuffers[1];
    m_covarianceCmdBuffer = cmdBuffers[2];

    // Create compute fence
    VkFenceCreateInfo fenceInfo = {};
    fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;
    vkCreateFence(device, &fenceInfo, nullptr, &m_computeFence);
}

void MrVkFluid::recreatePipeline()
{
    // TODO: Create graphics and compute pipelines
    createPipelineLayouts();
    createGraphicsPipelines();
    createComputePipelines();
}

void MrVkFluid::createPipelineLayouts()
{
    DP(("MrVkFluid::createPipelineLayouts() - Creating pipeline layouts"));
    
    VkDevice device = Driver->Device;
    
    try {
        // ===============================================================
        // Graphics Pipeline Layout
        // ===============================================================
        
        VkPipelineLayoutCreateInfo graphicsLayoutInfo = {};
        graphicsLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
        graphicsLayoutInfo.setLayoutCount = 1;
        graphicsLayoutInfo.pSetLayouts = &m_graphicsDescriptorSetLayout;
        graphicsLayoutInfo.pushConstantRangeCount = 0;
        graphicsLayoutInfo.pPushConstantRanges = nullptr;
        
        VK_CHECK_RESULT(vkCreatePipelineLayout(device, &graphicsLayoutInfo, nullptr, &m_graphicsPipelineLayout));
        
        // ===============================================================
        // Compute Pipeline Layout
        // ===============================================================
        
        VkPipelineLayoutCreateInfo computeLayoutInfo = {};
        computeLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
        computeLayoutInfo.setLayoutCount = 1;
        computeLayoutInfo.pSetLayouts = &m_computeDescriptorSetLayout;
        computeLayoutInfo.pushConstantRangeCount = 0;
        computeLayoutInfo.pPushConstantRanges = nullptr;
        
        VK_CHECK_RESULT(vkCreatePipelineLayout(device, &computeLayoutInfo, nullptr, &m_computePipelineLayout));
        
        DP(("MrVkFluid pipeline layouts created successfully"));
        
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::createPipelineLayouts(): %s", e.what()));
    }
}

void MrVkFluid::createGraphicsPipelines()
{
    DP(("MrVkFluid::createGraphicsPipelines() - Creating graphics pipelines"));
    
    if (!m_ellipsoidShader || m_graphicsPipelineLayout == VK_NULL_HANDLE) {
        DP(("Cannot create graphics pipeline: shader or layout not available"));
        return;
    }
    
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    try {
        // ===============================================================
        // Ellipsoid Graphics Pipeline (primary rendering mode)
        // ===============================================================
        
        VkGraphicsPipelineCreateInfo pipelineInfo = {};
        pipelineInfo.sType = VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO;
        
        // Shader stages
        VkPipelineShaderStageCreateInfo shaderStages[2] = {};
        
        // Use m_ellipsoidShader directly in pipelineInfo
        m_ellipsoidShader->applyShaders(&pipelineInfo);
        
        pipelineInfo.stageCount = 2;
        pipelineInfo.pStages = shaderStages;
        
        // Vertex input (instanced rendering from particle data)
        VkVertexInputBindingDescription bindingDescription = {};
        bindingDescription.binding = 0;
        bindingDescription.stride = sizeof(float) * 5; // position(3) + texcoord(2)
        bindingDescription.inputRate = VK_VERTEX_INPUT_RATE_VERTEX;
        
        VkVertexInputAttributeDescription attributeDescriptions[2] = {};
        
        // Position attribute
        attributeDescriptions[0].binding = 0;
        attributeDescriptions[0].location = 0;
        attributeDescriptions[0].format = VK_FORMAT_R32G32B32_SFLOAT;
        attributeDescriptions[0].offset = 0;
        
        // Texture coordinate attribute
        attributeDescriptions[1].binding = 0;
        attributeDescriptions[1].location = 1;
        attributeDescriptions[1].format = VK_FORMAT_R32G32_SFLOAT;
        attributeDescriptions[1].offset = sizeof(float) * 3;
        
        VkPipelineVertexInputStateCreateInfo vertexInputInfo = {};
        vertexInputInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO;
        vertexInputInfo.vertexBindingDescriptionCount = 1;
        vertexInputInfo.pVertexBindingDescriptions = &bindingDescription;
        vertexInputInfo.vertexAttributeDescriptionCount = 2;
        vertexInputInfo.pVertexAttributeDescriptions = attributeDescriptions;
        
        pipelineInfo.pVertexInputState = &vertexInputInfo;
        
        // Input assembly
        VkPipelineInputAssemblyStateCreateInfo inputAssembly = {};
        inputAssembly.sType = VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO;
        inputAssembly.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
        inputAssembly.primitiveRestartEnable = VK_FALSE;
        
        pipelineInfo.pInputAssemblyState = &inputAssembly;
        
        // Viewport state (dynamic)
        VkPipelineViewportStateCreateInfo viewportState = {};
        viewportState.sType = VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO;
        viewportState.viewportCount = 1;
        viewportState.scissorCount = 1;
        
        pipelineInfo.pViewportState = &viewportState;
        
        // Rasterization
        VkPipelineRasterizationStateCreateInfo rasterizer = {};
        rasterizer.sType = VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO;
        rasterizer.depthClampEnable = VK_FALSE;
        rasterizer.rasterizerDiscardEnable = VK_FALSE;
        rasterizer.polygonMode = VK_POLYGON_MODE_FILL;
        rasterizer.lineWidth = 1.0f;
        rasterizer.cullMode = VK_CULL_MODE_BACK_BIT;
        rasterizer.frontFace = VK_FRONT_FACE_COUNTER_CLOCKWISE;
        rasterizer.depthBiasEnable = VK_FALSE;
        
        pipelineInfo.pRasterizationState = &rasterizer;
        
        // Multisampling (disabled for now)
        VkPipelineMultisampleStateCreateInfo multisampling = {};
        multisampling.sType = VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO;
        multisampling.sampleShadingEnable = VK_FALSE;
        multisampling.rasterizationSamples = VK_SAMPLE_COUNT_1_BIT;
        
        pipelineInfo.pMultisampleState = &multisampling;
        
        // Depth testing
        VkPipelineDepthStencilStateCreateInfo depthStencil = {};
        depthStencil.sType = VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO;
        depthStencil.depthTestEnable = VK_TRUE;
        depthStencil.depthWriteEnable = VK_TRUE;
        depthStencil.depthCompareOp = VK_COMPARE_OP_LESS;
        depthStencil.depthBoundsTestEnable = VK_FALSE;
        depthStencil.stencilTestEnable = VK_FALSE;
        
        pipelineInfo.pDepthStencilState = &depthStencil;
        
        // Color blending (for transparency)
        VkPipelineColorBlendAttachmentState colorBlendAttachment = {};
        colorBlendAttachment.colorWriteMask = VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT | 
                                            VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT;
        colorBlendAttachment.blendEnable = VK_TRUE;
        colorBlendAttachment.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
        colorBlendAttachment.dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
        colorBlendAttachment.colorBlendOp = VK_BLEND_OP_ADD;
        colorBlendAttachment.srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
        colorBlendAttachment.dstAlphaBlendFactor = VK_BLEND_FACTOR_ZERO;
        colorBlendAttachment.alphaBlendOp = VK_BLEND_OP_ADD;
        
        VkPipelineColorBlendStateCreateInfo colorBlending = {};
        colorBlending.sType = VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO;
        colorBlending.logicOpEnable = VK_FALSE;
        colorBlending.logicOp = VK_LOGIC_OP_COPY;
        colorBlending.attachmentCount = 1;
        colorBlending.pAttachments = &colorBlendAttachment;
        colorBlending.blendConstants[0] = 0.0f;
        colorBlending.blendConstants[1] = 0.0f;
        colorBlending.blendConstants[2] = 0.0f;
        colorBlending.blendConstants[3] = 0.0f;
        
        pipelineInfo.pColorBlendState = &colorBlending;
        
        // Dynamic state
        VkDynamicState dynamicStates[] = {
            VK_DYNAMIC_STATE_VIEWPORT,
            VK_DYNAMIC_STATE_SCISSOR
        };
        
        VkPipelineDynamicStateCreateInfo dynamicState = {};
        dynamicState.sType = VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO;
        dynamicState.dynamicStateCount = 2;
        dynamicState.pDynamicStates = dynamicStates;
        
        pipelineInfo.pDynamicState = &dynamicState;
        
        // Pipeline layout and render pass
        pipelineInfo.layout = m_graphicsPipelineLayout;
        pipelineInfo.renderPass = vkDriver->getRenderPass(); 
        pipelineInfo.subpass = 0;
        
        pipelineInfo.basePipelineHandle = VK_NULL_HANDLE;
        pipelineInfo.basePipelineIndex = -1;
        
        // Create the pipeline
        VK_CHECK_RESULT(vkCreateGraphicsPipelines(device, VK_NULL_HANDLE, 1, &pipelineInfo, nullptr, &m_ellipsoidPipeline));
        
        DP(("MrVkFluid ellipsoid graphics pipeline created successfully"));
        
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::createGraphicsPipelines(): %s", e.what()));
    }
}

void MrVkFluid::createComputePipelines()
{
    DP(("MrVkFluid::createComputePipelines() - Creating compute pipelines"));
    
    VkDevice device = Driver->Device;
    
    try {
        // ===============================================================
        // Density Computation Pipeline
        // ===============================================================
        
        if (m_densityComputeShader && m_computePipelineLayout != VK_NULL_HANDLE) {
            VkComputePipelineCreateInfo densityPipelineInfo = {};
            densityPipelineInfo.sType = VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO;
            densityPipelineInfo.stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
            densityPipelineInfo.stage.stage = VK_SHADER_STAGE_COMPUTE_BIT;
            densityPipelineInfo.stage.module = m_densityComputeShader->getComputeShaderModule();
            densityPipelineInfo.stage.pName = "main";
            densityPipelineInfo.layout = m_computePipelineLayout;
            densityPipelineInfo.basePipelineHandle = VK_NULL_HANDLE;
            densityPipelineInfo.basePipelineIndex = -1;
            
            VK_CHECK_RESULT(vkCreateComputePipelines(device, VK_NULL_HANDLE, 1, &densityPipelineInfo, nullptr, &m_densityComputePipeline));
            DP(("Density compute pipeline created successfully"));
        }
        
        // ===============================================================
        // Covariance Computation Pipeline  
        // ===============================================================
        
        if (m_covarianceShader && m_computePipelineLayout != VK_NULL_HANDLE) {
            VkComputePipelineCreateInfo covariancePipelineInfo = {};
            covariancePipelineInfo.sType = VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO;
            covariancePipelineInfo.stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
            covariancePipelineInfo.stage.stage = VK_SHADER_STAGE_COMPUTE_BIT;
            covariancePipelineInfo.stage.module = m_covarianceShader->getComputeShaderModule();
            covariancePipelineInfo.stage.pName = "main";
            covariancePipelineInfo.layout = m_computePipelineLayout;
            covariancePipelineInfo.basePipelineHandle = VK_NULL_HANDLE;
            covariancePipelineInfo.basePipelineIndex = -1;
            
            VK_CHECK_RESULT(vkCreateComputePipelines(device, VK_NULL_HANDLE, 1, &covariancePipelineInfo, nullptr, &m_covariancePipeline));
            DP(("Covariance compute pipeline created successfully"));
        }
        
        DP(("MrVkFluid compute pipelines created successfully"));
        
    } catch (const std::exception& e) {
        DP(("Exception in MrVkFluid::createComputePipelines(): %s", e.what()));
    }
}

void MrVkFluid::createSamplers()
{
    VkDevice device = Driver->Device;

    // Linear sampler
    VkSamplerCreateInfo samplerInfo = {};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.anisotropyEnable = VK_FALSE;
    samplerInfo.maxAnisotropy = 1.0f;
    samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_OPAQUE_BLACK;
    samplerInfo.unnormalizedCoordinates = VK_FALSE;
    samplerInfo.compareEnable = VK_FALSE;
    samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
    samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
    samplerInfo.mipLodBias = 0.0f;
    samplerInfo.minLod = 0.0f;
    samplerInfo.maxLod = 0.0f;

    vkCreateSampler(device, &samplerInfo, nullptr, &m_linearSampler);

    // Nearest sampler
    samplerInfo.magFilter = VK_FILTER_NEAREST;
    samplerInfo.minFilter = VK_FILTER_NEAREST;
    vkCreateSampler(device, &samplerInfo, nullptr, &m_nearestSampler);

    // Shadow sampler
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.compareEnable = VK_TRUE;
    samplerInfo.compareOp = VK_COMPARE_OP_LESS;
    vkCreateSampler(device, &samplerInfo, nullptr, &m_shadowSampler);
}

} // namespace video
} // namespace irr
 