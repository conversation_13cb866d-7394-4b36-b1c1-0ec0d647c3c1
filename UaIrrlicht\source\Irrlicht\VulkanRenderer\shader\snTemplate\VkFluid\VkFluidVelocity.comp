#version 460

/**
 * =================================================================
 * VkFluidVelocity.comp - 3D Velocity Field Generation Compute Shader
 * =================================================================
 * 
 * Generates 3D velocity field from SPH particle data
 * Creates smooth velocity interpolation for volume rendering
 * 
 * Algorithm:
 * 1. For each voxel in 3D volume texture
 * 2. Accumulate velocity contributions from nearby particles
 * 3. Use SPH kernel functions for smooth interpolation
 * 4. Store result in 3D texture for volume rendering
 * =================================================================
 */

layout(local_size_x = 8, local_size_y = 8, local_size_z = 8) in;

// Uniform buffer
layout(set = 0, binding = 0) uniform FluidUniforms {
    mat4 mViewProjectionInverse;
    vec3 volumeMin;
    float time;
    vec3 volumeMax; 
    float rayStepSize;
    vec3 volumeSize;
    float densityThreshold;
    vec3 cameraPosition;
    int maxRaymarchSteps;
    float noiseScale;
    float refractionIndex;
    float absorptionScale;
    float scatteringScale;
    uint numParticles;
    float padding0;
    float padding1;
    float padding2;
} uniforms;

// Particle data buffers
layout(set = 0, binding = 1, std430) readonly buffer ParticleBuffer {
    vec4 positionMass[100000];    // xyz = position, w = 1/mass
    vec4 velocity[100000];        // xyz = velocity, w = density
    vec4 force[100000];           // xyz = force, w = pressure
    vec4 extra[100000];           // Additional data
} particles;

// Output 3D velocity texture
layout(set = 0, binding = 4, rg16f) uniform image3D velocityTexture;

// Constants
#define PI 3.1415926535
#define KERNEL_RADIUS 1.0

// SPH Kernel Functions
float kernelPoly6(float r, float h) {
    if (r > h) return 0.0;
    float ratio = h * h - r * r;
    return (315.0 / (64.0 * PI)) * ratio * ratio * ratio / (h * h * h * h * h * h * h * h * h);
}

// Gaussian kernel for smooth velocity field
float gaussianKernel(float r, float h) {
    float norm = 1.0 / (h * h * h * sqrt(PI * PI * PI * 8.0));
    return norm * exp(-0.5 * (r * r) / (h * h));
}

// Add velocity contribution from a particle
vec3 addVelocityContribution(vec3 voxelPos, vec4 particlePosMass, vec4 particleVel) {
    if (particlePosMass.w == 0.0) return vec3(0.0); // No mass
    
    vec3 particlePos = particlePosMass.xyz;
    float mass = 1.0 / max(particlePosMass.w, 1e-6); // Convert from 1/mass to mass
    
    float distance = length(voxelPos - particlePos);
    
    // Use Gaussian kernel for smooth velocity field
    float weight = mass * gaussianKernel(distance, KERNEL_RADIUS);
    return particleVel.xyz * weight;
}

void main() {
    ivec3 voxelCoord = ivec3(gl_GlobalInvocationID.xyz);
    ivec3 volumeSize = imageSize(velocityTexture);
    
    // Check bounds
    if (any(greaterThanEqual(voxelCoord, volumeSize))) {
        return;
    }
    
    // Convert voxel coordinate to world position
    vec3 voxelPos = uniforms.volumeMin + 
                   (vec3(voxelCoord) + 0.5) * uniforms.volumeSize / vec3(volumeSize);
    
    vec3 totalVelocity = vec3(0.0);
    float totalWeight = 0.0;
    
    // Accumulate velocity from all particles
    uint numParticles = uniforms.numParticles;
    
    for (uint i = 0; i < numParticles; i++) {
        vec4 particlePosMass = particles.positionMass[i];
        vec4 particleVel = particles.velocity[i];
        
        if (particlePosMass.w == 0.0) continue; // Skip invalid particles
        
        vec3 particlePos = particlePosMass.xyz;
        float mass = 1.0 / max(particlePosMass.w, 1e-6);
        float distance = length(voxelPos - particlePos);
        
        float weight = mass * gaussianKernel(distance, KERNEL_RADIUS);
        if (weight > 0.0) {
            totalVelocity += particleVel.xyz * weight;
            totalWeight += weight;
        }
    }
    
    // Normalize velocity by total weight
    if (totalWeight > 0.0) {
        totalVelocity /= totalWeight;
    }
    
    // Store velocity in 3D texture (RG16F format - only XY components)
    imageStore(velocityTexture, voxelCoord, vec4(totalVelocity.xy, 0.0, 0.0));
}
