#pragma once
const uint32_t SpirV_FFCode2DVertexColorDuDv_frag[] = {
	0x07230203,0x00010300,0x0008000b,0x00000023,0x00000000,0x00020011,0x00000001,0x0006000b,
	0x00000003,0x4c534c47,0x6474732e,0x3035342e,0x00000000,0x0003000e,0x00000000,0x00000001,
	0x0008000f,0x00000004,0x00000006,0x6e69616d,0x00000000,0x00000012,0x00000018,0x00000020,
	0x00030010,0x00000006,0x00000007,0x000a0007,0x00000001,0x6f434646,0x44326564,0x74726556,
	0x6f437865,0x44726f6c,0x2e764475,0x67617266,0x00000000,0x000a0007,0x00000002,0x2e2e2f2e,
	0x6665442f,0x53656e69,0x65726168,0x68746957,0x64616853,0x682e7265,0x00000000,0x00bf0003,
	0x00000002,0x000001cc,0x00000001,0x72657623,0x6e6f6973,0x30363420,0x7865230a,0x736e6574,
	0x206e6f69,0x475f4c47,0x4c474f4f,0x6e695f45,0x64756c63,0x69645f65,0x74636572,0x20657669,
	0x6e65203a,0x656c6261,0x6e69230a,0x64756c63,0x2e222065,0x65442f2e,0x656e6966,0x72616853,
	0x74695765,0x61685368,0x2e726564,0x700a2268,0x69636572,0x6e6f6973,0x67696820,0x66207068,
	0x74616f6c,0x0a0a0a3b,0x6f79616c,0x28207475,0x646e6962,0x20676e69,0x2932203d,0x6e752020,
	0x726f6669,0x6173206d,0x656c706d,0x20443272,0x65745f67,0x735f3078,0x6c706d61,0x0a3b7265,
	0x6f79616c,0x28207475,0x646e6962,0x20676e69,0x2933203d,0x6e752020,0x726f6669,0x6173206d,
	0x656c706d,0x20443272,0x65745f67,0x735f3178,0x6c706d61,0x0a3b7265,0x090a0a0a,0x6f79616c,
	0x6c287475,0x7461636f,0x206e6f69,0x2930203d,0x206e6920,0x34636576,0x635f6920,0x726f6c6f,
	0x090a3b44,0x6f79616c,0x6c287475,0x7461636f,0x206e6f69,0x2931203d,0x206e6920,0x32636576,
	0x745f6920,0x3b307865,0x0a0a0a0a,0x3d2f2f0a,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,
	0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x20535020,0x3d3d3d3d,
	0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,0x3d3d3d3d,
	0x3d3d3d3d,0x2f0a0a0a,0x6d61532f,0x72656c70,0x74617453,0x5f672065,0x31786574,0x6d61735f,
	0x72656c70,0x72203a20,0x73696765,0x28726574,0x3b293173,0x616c0a0a,0x74756f79,0x6f6c2820,
	0x69746163,0x3d206e6f,0x20293020,0x2074756f,0x34636576,0x74756f20,0x67617246,0x6f6c6f43,
	0x0a0a3b72,0x61202f2f,0x6e696464,0x69702067,0x206c6578,0x64616873,0x760a7265,0x2064696f,
	0x6e69616d,0x7b0a2928,0x6669230a,0x090a3120,0x32636576,0x64756420,0x74203d76,0x75747865,
	0x67286572,0x7865745f,0x61735f31,0x656c706d,0x5f692c72,0x30786574,0x67722e29,0x6f090a3b,
	0x72467475,0x6f436761,0x20726f6c,0x7420203d,0x75747865,0x67286572,0x7865745f,0x61735f30,
	0x656c706d,0x5f692c72,0x30786574,0x6475642b,0x2a202976,0x635f6920,0x726f6c6f,0x0a203b44,
	0x65230a20,0x0a65736c,0x4674756f,0x43676172,0x726f6c6f,0x76203d20,0x28346365,0x74786574,
	0x28657275,0x65745f67,0x735f3078,0x6c706d61,0x692c7265,0x7865745f,0x722e2930,0x202c6267,
	0x6f635f69,0x44726f6c,0x3b29612e,0x65230a20,0x6669646e,0x00007d0a,0x002f0003,0x00000002,
	0x000001cc,0x00000002,0x6e666923,0x20666564,0x5f525249,0x44414853,0x435f5245,0x535f5050,
	0x45524148,0x45485f44,0x52454441,0x4c49465f,0x230a0d45,0x69666564,0x4920656e,0x535f5252,
	0x45444148,0x50435f52,0x48535f50,0x44455241,0x4145485f,0x5f524544,0x454c4946,0x64230a0d,
	0x6e696665,0x53552065,0x50535f45,0x4c554345,0x31205241,0x64230a0d,0x6e696665,0x53552065,
	0x50535f45,0x4c554345,0x20315241,0x0a0d3120,0x64230a0d,0x6e696665,0x52492065,0x46445f52,
	0x52494d5f,0x20524f52,0x230a0d31,0x69646e65,0x00000066,0x000a0004,0x475f4c47,0x4c474f4f,
	0x70635f45,0x74735f70,0x5f656c79,0x656e696c,0x7269645f,0x69746365,0x00006576,0x00080004,
	0x475f4c47,0x4c474f4f,0x6e695f45,0x64756c63,0x69645f65,0x74636572,0x00657669,0x00040005,
	0x00000006,0x6e69616d,0x00000000,0x00060005,0x0000000f,0x65745f67,0x735f3178,0x6c706d61,
	0x00007265,0x00040005,0x00000012,0x65745f69,0x00003078,0x00060005,0x00000018,0x4674756f,
	0x43676172,0x726f6c6f,0x00000000,0x00060005,0x00000019,0x65745f67,0x735f3078,0x6c706d61,
	0x00007265,0x00050005,0x00000020,0x6f635f69,0x44726f6c,0x00000000,0x0006014a,0x72746e65,
	0x6f702d79,0x20746e69,0x6e69616d,0x00000000,0x0006014a,0x65696c63,0x7620746e,0x616b6c75,
	0x3030316e,0x00000000,0x0006014a,0x67726174,0x652d7465,0x7320766e,0x76726970,0x00332e31,
	0x0007014a,0x67726174,0x652d7465,0x7620766e,0x616b6c75,0x312e316e,0x00000000,0x0006014a,
	0x72746e65,0x6f702d79,0x20746e69,0x6e69616d,0x00000000,0x00040047,0x0000000f,0x00000021,
	0x00000003,0x00040047,0x0000000f,0x00000022,0x00000000,0x00040047,0x00000012,0x0000001e,
	0x00000001,0x00040047,0x00000018,0x0000001e,0x00000000,0x00040047,0x00000019,0x00000021,
	0x00000002,0x00040047,0x00000019,0x00000022,0x00000000,0x00040047,0x00000020,0x0000001e,
	0x00000000,0x00020013,0x00000004,0x00030021,0x00000005,0x00000004,0x00030016,0x00000008,
	0x00000020,0x00040017,0x00000009,0x00000008,0x00000002,0x00090019,0x0000000c,0x00000008,
	0x00000001,0x00000000,0x00000000,0x00000000,0x00000001,0x00000000,0x0003001b,0x0000000d,
	0x0000000c,0x00040020,0x0000000e,0x00000000,0x0000000d,0x0004003b,0x0000000e,0x0000000f,
	0x00000000,0x00040020,0x00000011,0x00000001,0x00000009,0x0004003b,0x00000011,0x00000012,
	0x00000001,0x00040017,0x00000014,0x00000008,0x00000004,0x00040020,0x00000017,0x00000003,
	0x00000014,0x0004003b,0x00000017,0x00000018,0x00000003,0x0004003b,0x0000000e,0x00000019,
	0x00000000,0x00040020,0x0000001f,0x00000001,0x00000014,0x0004003b,0x0000001f,0x00000020,
	0x00000001,0x00040008,0x00000001,0x0000001a,0x0000000b,0x00050036,0x00000004,0x00000006,
	0x00000000,0x00000005,0x000200f8,0x00000007,0x00040008,0x00000001,0x0000001d,0x00000000,
	0x0004003d,0x0000000d,0x00000010,0x0000000f,0x0004003d,0x00000009,0x00000013,0x00000012,
	0x00050057,0x00000014,0x00000015,0x00000010,0x00000013,0x0007004f,0x00000009,0x00000016,
	0x00000015,0x00000015,0x00000000,0x00000001,0x00040008,0x00000001,0x0000001e,0x00000000,
	0x0004003d,0x0000000d,0x0000001a,0x00000019,0x00050081,0x00000009,0x0000001d,0x00000013,
	0x00000016,0x00050057,0x00000014,0x0000001e,0x0000001a,0x0000001d,0x0004003d,0x00000014,
	0x00000021,0x00000020,0x00050085,0x00000014,0x00000022,0x0000001e,0x00000021,0x0003003e,
	0x00000018,0x00000022,0x00040008,0x00000001,0x00000023,0x00000000,0x000100fd,0x00010038
};
