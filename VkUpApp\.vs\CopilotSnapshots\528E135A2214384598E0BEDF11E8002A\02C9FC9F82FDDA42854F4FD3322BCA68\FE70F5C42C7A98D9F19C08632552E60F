﻿#pragma once

#include "../VkHeader.h"

#include <functional>
#define USE_PARSER 0
#define CE_DECL_SHADERCODE(_sname)  { _sname ,sizeof(_sname) }
#define VKR_MAX_STAGES 3
namespace VkFxUtil {

	struct ShaderBytecode
	{
		void const* code;
		size_t length;
	};

	class VkFxBase
	{
	public:
		VkFxBase(VkDevice device);
		virtual ~VkFxBase();

		//enum StageEnum {			eVS = 0, eGS = 1, ePS = 2		};

		void createVS(const ShaderBytecode &code, const char* name);
		void createGS(const ShaderBytecode &code, const char* name);
		void createPS(const ShaderBytecode &code, const char* name);

		void recreatePS(const char* fName);
		void recreateVS(const char* fName);
		void recreateCS(const char* fName);
		void createPS(const char* fName, const char* entryName);

		void createCS(const ShaderBytecode& code, const char* name);
		//void CreateGS_SO(const ShaderBytecode & code,  const D3D11_SO_DECLARATION_ENTRY * pSODeclaration, UINT NumEntries, const UINT * pBufferStrides, UINT NumStrides);
		void applyShaders(VkGraphicsPipelineCreateInfo* pd);

		void applyPipelineLayout(VkGraphicsPipelineCreateInfo* pd);

		void applyComputeShader(VkComputePipelineCreateInfo* pd);

		const ShaderBytecode* CompiledCodeWithSignature() {
			return mCompiledCodeWithSignature;
		}

		void createPipelineLayoutAndDescriptorSet(
			const VkDescriptorSetLayoutCreateInfo& layoutCI,
			const VkDescriptorPoolCreateInfo& poolCI, 
			uint32_t descSetCount
		);
		void allocDs(uint32_t c, VkDescriptorSet* ds);
		void freeDs(VkDescriptorSet *ds){ vkFreeDescriptorSets(Device, mDescPool, 1, ds); }
		void copyLayoutFrom(VkFxBase* other);
		VkDescriptorSet getDescriptorSet(size_t id) { if (id< mDescSets.size()) return mDescSets[id]; else throw;	}
		VkPipelineLayout getPipelineLayout() { return mPplLayout; }

		VkPipeline createComputePipeline(VkPipelineCache cache);
		void destoryComputePipeLine();
		VkPipeline getComputePipeline() { return mPipeline; }
	private:
		VkShaderModule createSPIRVShader(const ShaderBytecode & code);

		VkDevice Device;
		VkShaderModule		mVertexShader = VK_NULL_HANDLE, mGeometryShader = VK_NULL_HANDLE, mPixelShader = VK_NULL_HANDLE,
			mComputeShader = VK_NULL_HANDLE;


		const ShaderBytecode * mCompiledCodeWithSignature = nullptr;
		uint32_t mStageCount = 0;
		VkPipelineShaderStageCreateInfo mStages[VKR_MAX_STAGES] = {};
		int mIdVS = -1, mIdGS = -1, mIdPS = -1, mIdCS = -1;

		VkPipeline mPipeline = VK_NULL_HANDLE;
		
		bool mOwnLayoutObject = false;
		VkDescriptorSetLayout mDescSetLayout = VK_NULL_HANDLE;
		VkDescriptorPool mDescPool = VK_NULL_HANDLE;
		VkPipelineLayout mPplLayout = VK_NULL_HANDLE;
		std::vector<VkDescriptorSet>  mDescSets;

		//VkPipelineShaderStageCreateInfo mVsStages[2];
		//VkShaderModule mVsShaderModules[2];
	};

	VkPipelineShaderStageCreateInfo loadVkShader(VkDevice device, std::string fileName, VkShaderStageFlagBits stage);
}

