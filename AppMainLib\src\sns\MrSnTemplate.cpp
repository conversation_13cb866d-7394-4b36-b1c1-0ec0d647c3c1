#include "appGlobal.h" // Include the precompiled header file
#include "MrSnTemplate.h"
#include "../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkDriver.h"
#include "../../UaIrrlicht/include/IFileSystem.h"
#include "VulkanRenderer/VkVertexDeclaration.h"
#include "VulkanRenderer/vulkanRenderPass.h"
#include "VulkanRenderer/VkShaderMan/VkFxBase.h"
#include "VulkanRenderer/VkShaderMan/VkFxDescriptorSetManager.h"
//compiled shader headers
#include "VulkanRenderer/Shader/Compiled/SnTemplate/sntemplate_vert_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/sntemplate_frag_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/sntemplate_comp_SpirV.h"


namespace irr
{
namespace video
{

    using namespace VkFxUtil;
    static const ShaderBytecode Sbc_snTemplate__SnTemplate_vert = CE_DECL_SHADERCODE(SpirV_snTemplate__SnTemplate_vert);
	static const ShaderBytecode Sbc_snTemplate__SnTemplate_frag = CE_DECL_SHADERCODE(SpirV_snTemplate__SnTemplate_frag);
	static const ShaderBytecode Sbc_snTemplate__SnTemplate_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__SnTemplate_comp);


E_MATERIAL_TYPE MrSnTemplate::s_materialType = EMT_SOLID;

MrSnTemplate::MrSnTemplate(IVideoDriver* driver, io::IFileSystem* fileSystem)
    : VkMaterialRenderer(driver)
    , m_fileSystem(fileSystem)
    , m_descriptorSet(VK_NULL_HANDLE)
    , m_computeDescriptorSet(VK_NULL_HANDLE)
    , m_descriptorSetLayout(VK_NULL_HANDLE)
    , m_computeDescriptorSetLayout(VK_NULL_HANDLE)
    , m_descriptorPool(VK_NULL_HANDLE)
    , m_defaultSampler(VK_NULL_HANDLE)
    , m_pipelineLayout(VK_NULL_HANDLE)
    , m_computePipelineLayout(VK_NULL_HANDLE)
    , m_graphicsPipeline(VK_NULL_HANDLE)
    , m_computePipeline(VK_NULL_HANDLE)
    , m_computeCommandBuffer(VK_NULL_HANDLE)
    , m_computeFence(VK_NULL_HANDLE)
    , m_time(0.0f)
    , m_deltaTime(0.0f)
    , m_needsUpdate(true)
    , m_computeInputBuffer(nullptr)
    , m_computeOutputBuffer(nullptr)
{
    if (m_fileSystem)
        m_fileSystem->grab();

    // Initialize uniform data
    memset(&m_uniforms, 0, sizeof(m_uniforms));
    memset(&m_computeParams, 0, sizeof(m_computeParams));

    // Set default values
    m_uniforms.mWorld.makeIdentity();
    m_uniforms.mView.makeIdentity();
    m_uniforms.mProjection.makeIdentity();
    m_uniforms.materialColor = float4(1.0f, 1.0f, 1.0f, 1.0f);
    m_uniforms.lightDirection = float4(0.0f, -1.0f, 0.0f, 0.0f);
    m_uniforms.lightColor = float4(1.0f, 1.0f, 1.0f, 1.0f);

    initializeShaders();
    createUniformBuffers();
    createDefaultSampler();
    createComputeCommandBuffer();
    setupDescriptorSets();
    recreatePipeline();
}

MrSnTemplate::~MrSnTemplate()
{
    // Clean up Vulkan resources
    VkDevice device = Driver->Device;

    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
    }

    if (m_computePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_computePipeline, nullptr);
    }

    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
    }

    if (m_descriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_descriptorSetLayout, nullptr);
    }

    if (m_computeDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_computeDescriptorSetLayout, nullptr);
    }

    if (m_descriptorPool != VK_NULL_HANDLE) {
        vkDestroyDescriptorPool(device, m_descriptorPool, nullptr);
    }

    if (m_defaultSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_defaultSampler, nullptr);
    }

    if (m_computeFence != VK_NULL_HANDLE) {
        vkDestroyFence(device, m_computeFence, nullptr);
    }

    if (m_computeCommandBuffer != VK_NULL_HANDLE) {
        VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
        vkFreeCommandBuffers(device, vkDriver->getComputeCommandPool(), 1, &m_computeCommandBuffer);
    }

    // vks::Buffer objects are automatically cleaned up by their destructors

    if (m_fileSystem)
        m_fileSystem->drop();
}

bool MrSnTemplate::OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId)
{
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    // Bind descriptor sets and pipeline
    VkCommandBuffer cmdBuffer = Driver->currentCmdBuffer();

    vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_graphicsPipeline);
    vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                           m_pipelineLayout, 0, 1, &m_descriptorSet, 0, nullptr);

    return true;
}

void MrSnTemplate::OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                                bool resetAllRenderstates, IMaterialRendererServices* services)
{
    CurrentMaterial = material;
    services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
    m_needsUpdate = true;
}

void MrSnTemplate::OnUnsetMaterial()
{
    // Nothing to do here for this template
}

bool MrSnTemplate::setVariable(const c8* name, const f32* floats, int count)
{
    // Handle custom shader variables
    if (strcmp(name, "time") == 0 && count >= 1) {
        setTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "deltaTime") == 0 && count >= 1) {
        setDeltaTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "materialColor") == 0 && count >= 4) {
        m_uniforms.materialColor = float4(floats[0], floats[1], floats[2], floats[3]);
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "lightDirection") == 0 && count >= 3) {
        m_uniforms.lightDirection = float4(floats[0], floats[1], floats[2], 0.0f);
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "lightColor") == 0 && count >= 3) {
        m_uniforms.lightColor = float4(floats[0], floats[1], floats[2], 1.0f);
        m_needsUpdate = true;
        return true;
    }

    return false;
}

const void* MrSnTemplate::getShaderByteCode() const
{
    // Return vertex shader bytecode for pipeline creation
    if (m_vertexFragmentShader) {
        // This would need to be implemented based on your shader loading system
        return nullptr; // Placeholder
    }
    return nullptr;
}

u32 MrSnTemplate::getShaderByteCodeSize() const
{
    // Return vertex shader bytecode size
    return 0; // Placeholder
}

void MrSnTemplate::cleanFrameCache()
{
    // Clean up per-frame resources if any
}

void MrSnTemplate::preSubmit()
{
    // Pre-submission setup if needed
}

void MrSnTemplate::ReloadShaders()
{
    initializeShaders();
    recreatePipeline();
}

void MrSnTemplate::setMaterialColor(const SColor& color)
{
    m_uniforms.materialColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        color.getAlpha() / 255.0f
    );
    m_needsUpdate = true;
}

void MrSnTemplate::setLightDirection(const core::vector3df& direction)
{
    core::vector3df normalized = direction;
    normalized.normalize();
    m_uniforms.lightDirection = float4(normalized.X, normalized.Y, normalized.Z, 0.0f);
    m_needsUpdate = true;
}

void MrSnTemplate::setLightColor(const SColor& color)
{
    m_uniforms.lightColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        1.0f
    );
    m_needsUpdate = true;
}

void MrSnTemplate::setWorldMatrix(const core::matrix4& world)
{
    m_uniforms.mWorld = world;
    m_needsUpdate = true;
}

void MrSnTemplate::setViewMatrix(const core::matrix4& view)
{
    m_uniforms.mView = view;
    m_needsUpdate = true;
}

void MrSnTemplate::setProjectionMatrix(const core::matrix4& projection)
{
    m_uniforms.mProjection = projection;
    m_needsUpdate = true;
}

E_MATERIAL_TYPE MrSnTemplate::getMaterialType()
{
    return s_materialType;
}

void MrSnTemplate::initializeShaders()
{
    // Load and create vertex/fragment shader
    m_vertexFragmentShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);

    // Load shaders from compiled bytecode (note: VS = vertex shader, PS = pixel/fragment shader)
    m_vertexFragmentShader->createVS(Sbc_snTemplate__SnTemplate_vert, "main");
    m_vertexFragmentShader->createPS(Sbc_snTemplate__SnTemplate_frag, "main");

    // Load and create compute shader
    m_computeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
    m_computeShader->createCS(Sbc_snTemplate__SnTemplate_comp, "main");
}

void MrSnTemplate::createUniformBuffers()
{
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    // Create uniform buffer for graphics pipeline using vks::Buffer
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_uniformBuffer,
        sizeof(SnTemplateUniforms)));

    // Create uniform buffer for compute pipeline using vks::Buffer
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_computeUniformBuffer,
        sizeof(SnTemplateComputeParams)));

    // Create a small dummy storage buffer for descriptor set validation
    // This will be replaced when actual storage buffers are set
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
        VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT,
        &m_dummyStorageBuffer,
        64)); // Small 64-byte dummy buffer

    // Map buffers persistently for fast updates
    VK_CHECK_RESULT(m_uniformBuffer.map());
    VK_CHECK_RESULT(m_computeUniformBuffer.map());
}

void MrSnTemplate::createDefaultSampler()
{
    VkDevice device = Driver->Device;
    
    VkSamplerCreateInfo samplerInfo{};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
    samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_REPEAT;
    samplerInfo.mipLodBias = 0.0f;
    samplerInfo.anisotropyEnable = VK_FALSE;
    samplerInfo.maxAnisotropy = 1.0f;
    samplerInfo.compareEnable = VK_FALSE;
    samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
    samplerInfo.minLod = 0.0f;
    samplerInfo.maxLod = 1.0f;
    samplerInfo.borderColor = VK_BORDER_COLOR_INT_OPAQUE_BLACK;
    samplerInfo.unnormalizedCoordinates = VK_FALSE;

    VK_CHECK_RESULT(vkCreateSampler(device, &samplerInfo, nullptr, &m_defaultSampler));
}

void MrSnTemplate::createComputeCommandBuffer()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create command buffer for compute operations
    VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(
        vkDriver->getComputeCommandPool(),
        VK_COMMAND_BUFFER_LEVEL_PRIMARY,
        1);

    VK_CHECK_RESULT(vkAllocateCommandBuffers(device, &cmdBufAllocateInfo, &m_computeCommandBuffer));

    // Create fence for compute synchronization
    VkFenceCreateInfo fenceCreateInfo = vks::initializers::fenceCreateInfo(VK_FENCE_CREATE_SIGNALED_BIT);
    VK_CHECK_RESULT(vkCreateFence(device, &fenceCreateInfo, nullptr, &m_computeFence));
}

void MrSnTemplate::updateUniformBuffers()
{
    // Update graphics uniform buffer using vks::Buffer
    // Since buffers are persistently mapped, we can directly copy data
    if (m_uniformBuffer.mapped) {
        memcpy(m_uniformBuffer.mapped, &m_uniforms, sizeof(SnTemplateUniforms));
    }

    // Update compute uniform buffer
    m_computeParams.time = m_time;
    m_computeParams.deltaTime = m_deltaTime;

    if (m_computeUniformBuffer.mapped) {
        memcpy(m_computeUniformBuffer.mapped, &m_computeParams, sizeof(SnTemplateComputeParams));
    }
}

void MrSnTemplate::setupDescriptorSets()
{
    // Create descriptor set layouts and descriptor sets
    VkDevice device = Driver->Device;

    // Graphics descriptor set layout
    std::vector<VkDescriptorSetLayoutBinding> graphicsBindings = {
        // Binding 0: Uniform buffer for vertex and fragment shaders
        {
            0,                                      // binding
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,      // descriptorType
            1,                                      // descriptorCount
            VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT, // stageFlags
            nullptr                                 // pImmutableSamplers
        },
        // Binding 1: Texture sampler for fragment shader (texDiffuse)
        {
            1,                                      // binding
            VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, // descriptorType
            1,                                      // descriptorCount
            VK_SHADER_STAGE_FRAGMENT_BIT,          // stageFlags
            nullptr                                 // pImmutableSamplers
        }
    };

    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = static_cast<uint32_t>(graphicsBindings.size());
    layoutInfo.pBindings = graphicsBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &layoutInfo, nullptr, &m_descriptorSetLayout));

    // Compute descriptor set layout
    std::vector<VkDescriptorSetLayoutBinding> computeBindings = {
        // Binding 0: Compute uniform buffer (parameters like time, deltaTime, elementCount)
        {
            0,                                      // binding
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,      // descriptorType
            1,                                      // descriptorCount
            VK_SHADER_STAGE_COMPUTE_BIT,           // stageFlags
            nullptr                                 // pImmutableSamplers
        },
        // Binding 1: Input storage buffer
        {
            1,                                      // binding
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,      // descriptorType
            1,                                      // descriptorCount
            VK_SHADER_STAGE_COMPUTE_BIT,           // stageFlags
            nullptr                                 // pImmutableSamplers
        },
        // Binding 2: Output storage buffer
        {
            2,                                      // binding
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,      // descriptorType
            1,                                      // descriptorCount
            VK_SHADER_STAGE_COMPUTE_BIT,           // stageFlags
            nullptr                                 // pImmutableSamplers
        }
    };

    VkDescriptorSetLayoutCreateInfo computeLayoutInfo{};
    computeLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    computeLayoutInfo.bindingCount = static_cast<uint32_t>(computeBindings.size());
    computeLayoutInfo.pBindings = computeBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &computeLayoutInfo, nullptr, &m_computeDescriptorSetLayout));

    // Create descriptor pool
    std::vector<VkDescriptorPoolSize> poolSizes = {
        { VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 2 },           // Graphics + Compute uniform buffers
        { VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1 },   // Graphics texture sampler
        { VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 2 }            // Input + Output storage buffers
    };

    VkDescriptorPoolCreateInfo poolInfo = vks::initializers::descriptorPoolCreateInfo(
        static_cast<uint32_t>(poolSizes.size()),
        poolSizes.data(),
        2  // maxSets: graphics + compute descriptor sets
    );

    VK_CHECK_RESULT(vkCreateDescriptorPool(device, &poolInfo, nullptr, &m_descriptorPool));

    // Allocate graphics descriptor set
    VkDescriptorSetAllocateInfo allocInfo = vks::initializers::descriptorSetAllocateInfo(
        m_descriptorPool,
        &m_descriptorSetLayout,
        1
    );
    VK_CHECK_RESULT(vkAllocateDescriptorSets(device, &allocInfo, &m_descriptorSet));

    // Allocate compute descriptor set
    allocInfo.pSetLayouts = &m_computeDescriptorSetLayout;
    VK_CHECK_RESULT(vkAllocateDescriptorSets(device, &allocInfo, &m_computeDescriptorSet));

    // Update graphics descriptor set
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    // Create descriptor image info for the default texture
    VkDescriptorImageInfo defaultTextureDescriptor{};
    defaultTextureDescriptor.imageView = VKTEX(vkDriver->NullTexture)->getShaderResourceView();
    defaultTextureDescriptor.sampler = m_defaultSampler;
    defaultTextureDescriptor.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
    
    std::vector<VkWriteDescriptorSet> writeDescriptorSets = {
        // Binding 0: Uniform buffer
        vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,
            0,
            &m_uniformBuffer.descriptor
        ),
        // Binding 1: Default texture sampler (using NullTexture)
        vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
            1,
            &defaultTextureDescriptor
        )
    };
    vkUpdateDescriptorSets(device, static_cast<uint32_t>(writeDescriptorSets.size()), writeDescriptorSets.data(), 0, nullptr);

    // Update compute descriptor set
    // We need to initialize ALL bindings, even if with dummy buffers initially
    
    std::vector<VkWriteDescriptorSet> computeWriteDescriptorSets = {
        // Binding 0: Compute uniform buffer
        vks::initializers::writeDescriptorSet(
            m_computeDescriptorSet,
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,
            0,
            &m_computeUniformBuffer.descriptor
        ),
        // Binding 1: Input storage buffer (dummy for now)
        vks::initializers::writeDescriptorSet(
            m_computeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1,
            &m_dummyStorageBuffer.descriptor
        ),
        // Binding 2: Output storage buffer (dummy for now)
        vks::initializers::writeDescriptorSet(
            m_computeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            2,
            &m_dummyStorageBuffer.descriptor
        )
    };
    
    vkUpdateDescriptorSets(device, static_cast<uint32_t>(computeWriteDescriptorSets.size()), 
                          computeWriteDescriptorSets.data(), 0, nullptr);
    
    // Note: Storage buffers will be properly updated when they are set via
    // setComputeInputBuffer() and setComputeOutputBuffer() methods
}

void MrSnTemplate::dispatchCompute(uint32_t elementCount)
{
    if (m_computePipeline == VK_NULL_HANDLE || m_computeCommandBuffer == VK_NULL_HANDLE) {
        return;
    }

    // Ensure storage buffers are set before dispatching
    if (m_computeInputBuffer == nullptr || m_computeOutputBuffer == nullptr) {
        // Storage buffers must be set before dispatching compute
        return;
    }

    m_computeParams.elementCount = elementCount;
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Wait for previous compute operation to complete
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    vkResetFences(device, 1, &m_computeFence);

    // Begin recording compute commands
    VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
    VK_CHECK_RESULT(vkBeginCommandBuffer(m_computeCommandBuffer, &cmdBufInfo));

    // Bind compute pipeline and descriptor sets
    vkCmdBindPipeline(m_computeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_computePipeline);
    vkCmdBindDescriptorSets(m_computeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_computeDescriptorSet, 0, nullptr);

    // Add memory barrier to ensure uniform buffer writes are visible
    VkMemoryBarrier memoryBarrier = vks::initializers::memoryBarrier();
    memoryBarrier.srcAccessMask = VK_ACCESS_HOST_WRITE_BIT;
    memoryBarrier.dstAccessMask = VK_ACCESS_UNIFORM_READ_BIT;
    vkCmdPipelineBarrier(m_computeCommandBuffer, 
                        VK_PIPELINE_STAGE_HOST_BIT, 
                        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT, 
                        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr);

    // Dispatch compute shader
    uint32_t workGroupCount = (elementCount + 63) / 64; // 64 is the local workgroup size
    vkCmdDispatch(m_computeCommandBuffer, workGroupCount, 1, 1);

    // Add memory barrier to ensure compute writes are visible to subsequent operations
    memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
    memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT | VK_ACCESS_VERTEX_ATTRIBUTE_READ_BIT;
    vkCmdPipelineBarrier(m_computeCommandBuffer, 
                        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT, 
                        VK_PIPELINE_STAGE_VERTEX_SHADER_BIT | VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT, 
                        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr);

    // End recording
    VK_CHECK_RESULT(vkEndCommandBuffer(m_computeCommandBuffer));

    // Submit compute commands
    VkSubmitInfo computeSubmitInfo = vks::initializers::submitInfo();
    computeSubmitInfo.commandBufferCount = 1;
    computeSubmitInfo.pCommandBuffers = &m_computeCommandBuffer;

    VK_CHECK_RESULT(vkQueueSubmit(vkDriver->getComputeQueue(), 1, &computeSubmitInfo, m_computeFence));
}

void MrSnTemplate::waitForComputeCompletion()
{
    if (m_computeFence != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    }
}

void MrSnTemplate::setComputeInputBuffer(VkHardwareBuffer* buffer)
{
    m_computeInputBuffer = buffer;
    
    // Update descriptor set to point to new buffer
    if (buffer && m_computeDescriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_computeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1,  // binding 1 for input buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }
}

void MrSnTemplate::setComputeOutputBuffer(VkHardwareBuffer* buffer)
{
    m_computeOutputBuffer = buffer;
    
    // Update descriptor set to point to new buffer
    if (buffer && m_computeDescriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_computeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            2,  // binding 2 for output buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }
}

void MrSnTemplate::recreatePipeline()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Clean up existing pipelines
    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
        m_graphicsPipeline = VK_NULL_HANDLE;
    }

    if (m_computePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_computePipeline, nullptr);
        m_computePipeline = VK_NULL_HANDLE;
    }

    // Clean up existing pipeline layouts
    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
        m_pipelineLayout = VK_NULL_HANDLE;
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
        m_computePipelineLayout = VK_NULL_HANDLE;
    }

    // Create pipeline layouts
    createPipelineLayouts();

    // Create graphics pipeline
    createGraphicsPipeline();

    // Create compute pipeline
    createComputePipeline();
}

void MrSnTemplate::createPipelineLayouts()
{
    VkDevice device = Driver->Device;

    // Create graphics pipeline layout
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &m_descriptorSetLayout;
    pipelineLayoutInfo.pushConstantRangeCount = 0;
    pipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &pipelineLayoutInfo, nullptr, &m_pipelineLayout));

    // Create compute pipeline layout
    VkPipelineLayoutCreateInfo computePipelineLayoutInfo{};
    computePipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    computePipelineLayoutInfo.setLayoutCount = 1;
    computePipelineLayoutInfo.pSetLayouts = &m_computeDescriptorSetLayout;
    computePipelineLayoutInfo.pushConstantRangeCount = 0;
    computePipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &computePipelineLayoutInfo, nullptr, &m_computePipelineLayout));
}

void MrSnTemplate::createGraphicsPipeline()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Set up pipeline state using base class helper
    SetPipelineCommonInfo();

    // Create graphics pipeline
    VkGraphicsPipelineCreateInfo pipelineInfo = vks::initializers::pipelineCreateInfo(
        m_pipelineLayout,
        vkDriver->curRenderPass(),
        0);

    // Set up vertex input state (assuming standard vertex format)
    auto declNode = vkDriver->declarationMap.find(E_VERTEX_TYPE::EVT_STANDARD);
    if (declNode ) {
        pipelineInfo.pVertexInputState = declNode->getValue()->getInputLayout(this, 
            (u64)m_vertexFragmentShader->CompiledCodeWithSignature());
    } else {
        // Fallback to empty vertex input state
        VkPipelineVertexInputStateCreateInfo emptyVertexInputState = vks::initializers::pipelineVertexInputStateCreateInfo();
        pipelineInfo.pVertexInputState = &emptyVertexInputState;
    }

    // Set up color blending
    VkPipelineColorBlendAttachmentState blendAttachmentState = 
        vks::initializers::pipelineColorBlendAttachmentState(0xf, VK_FALSE);
    VkPipelineColorBlendStateCreateInfo colorBlendState = 
        vks::initializers::pipelineColorBlendStateCreateInfo(1, &blendAttachmentState);

    // Apply common pipeline state
    pipelineInfo.pInputAssemblyState = &inputAssemblyState;
    pipelineInfo.pRasterizationState = &rasterizationState;
    pipelineInfo.pColorBlendState = &colorBlendState;
    pipelineInfo.pMultisampleState = &multisampleState;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pDepthStencilState = &depthStencilState;
    pipelineInfo.pDynamicState = &dynamicState;

    // Apply shaders
    if (m_vertexFragmentShader) {
        m_vertexFragmentShader->applyShaders(&pipelineInfo);
    }

    // Create the graphics pipeline
    VK_CHECK_RESULT(vkCreateGraphicsPipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_graphicsPipeline));
}

void MrSnTemplate::createComputePipeline()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    if (!m_computeShader) {
        return; // No compute shader available
    }

    // Create compute pipeline
    VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
    
    // Apply compute shader
    m_computeShader->applyComputeShader(&pipelineInfo);

    // Create the compute pipeline
    VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_computePipeline));
}



} // namespace video
} // namespace irr
