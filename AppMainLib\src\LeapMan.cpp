#include "AppGlobal.h"
#include "LeapMan.h" 
#include "irrmmd/irrSaba.h"
#include "irrmmd/irrMMD.h"
#define SCALE_POS  0.1;//0.07
#define ADD_POS   0, 0,0
#if USE_LEAP
#pragma comment(lib,"D:/SDK/LeapSDK/lib/x64/LeapC.lib")
#include "LeapC.h"
extern "C" {
#include "D:/SDK/LeapSDK/samples/ExampleConnection.h"
}

#define INTERPOLATE_FRAME 0
using namespace irr;
using namespace irr::core;
using namespace irr::scene;
using namespace ualib;
using namespace saba;

static clock_t cpuTime;
static int64_t targetFrameTime = 0;
static uint64_t targetFrameSize = 0;
static eLeapRS result;
static LEAP_CONNECTION* connHandle{};
static LEAP_CLOCK_REBASER clockSynchronizer;
static     LEAP_DEVICE device;
 
LeapMan::LeapMan(ualib::UaLibContext* ctx)
    :Ctx(ctx)
{
    connHandle=OpenConnection();


    auto stp = 3;
    et[0] = new ExponentialMovingAverageXYZ(stp);
    er[0] = new ExponentialMovingAverageXYZ(stp);
    et[1] = new ExponentialMovingAverageXYZ(stp);
    er[1] = new ExponentialMovingAverageXYZ(stp);
    auto sm = Ctx->getSceneManager();
    root = sm->addEmptySceneNode();
    //root->getMaterial(0).DiffuseColor = 0xFF00FF00;
    
    //snL = sm->addMeshSceneNode(sm->getMesh("data/leaphandl.dae"));    snL->setScale(1000);
   // snR = sm->addMeshSceneNode(sm->getMesh("data/leaphandr.dae"));    snR->setScale(1000);
    auto ms = sm->getMesh("res/axisbox.obj");
#if MMD_PMX_HAND
    root->setVisible(false);
#endif
    //root->setScale(10);
    for (int i = 0; i < 2; i++) {
        snH[i] = sm->addMeshSceneNode(ms, root);
        snH[i]->getMaterial(0).DiffuseColor = 0xFFFF8080;
    }
    scene::ISceneNode* lsn{};
    for (int hi = 0; hi < 2; hi++)  for (int fi = 0; fi < 5; fi++) for (int ni = 0; ni < 5; ni++)
    {
        auto sn = snF[hi][fi][ni] = sm->addMeshSceneNode(ms, ni == 0 ? snH[hi] : lsn);
        video::SColor sc(0xFF008080); sc.setRed(ni * 60 + 15); //sc.setRed(255 - ni * 60);// sc.setBlue(fi * 60 + 5);
        sn->getMaterial(0).DiffuseColor = sc;
        sn->setMaterialFlag(irr::video::EMF_LIGHTING, true);
        lsn = sn;
    }
}

LeapMan::~LeapMan() 
{
    //LEAP_DEVICE_REF dvs[16]; uint32_t size;
    //LeapGetDeviceList(*connHandle, dvs, &size);
    //LeapOpenDevice(dvs[0], &device);
    //LeapCloseDevice(device);
    //DestroyConnection();
}

inline irr::core::vector3df vl2i(LEAP_VECTOR& v) {
    vector3df pos{ v.x,v.y,-v.z };
    pos += vector3df(ADD_POS);
    pos *= SCALE_POS;
    // if (pos.Y < 0) pos.Y = 0;
    return pos;
}
inline irr::core::quaternion ql2i(LEAP_QUATERNION& v) {
    return { -v.x,-v.y,v.z,v.w };
}
void LeapMan::updates(float frameTime)
{
    if (!IsConnected) return;
    if (firstConnect)
    {
        firstConnect = false;
        if (connHandle) LeapSetTrackingMode(*connHandle, eLeapTrackingMode_Desktop);// eLeapTrackingMode_HMD);// eLeapTrackingMode_ScreenTop);//
        LEAP_DEVICE_INFO* deviceProps = GetDeviceProperties();
        if (deviceProps)
            DP(("Using device %s.\n", deviceProps->serial));
#if INTERPOLATE_FRAME
        //Create the clock synchronizer
        LeapCreateClockRebaser(&clockSynchronizer);
        //Calculate the application time
        cpuTime = (clock_t).000001 * clock() / CLOCKS_PER_SEC;//microseconds
        //Synchronize the clocks
        LeapUpdateRebase(clockSynchronizer, cpuTime, LeapGetNow());
#endif
    }
#if INTERPOLATE_FRAME
    //Simulate delay (i.e. processing load, v-sync, etc)
    //millisleep(10);

    //Now get the updated application time
    cpuTime = (clock_t).000001 * clock() / CLOCKS_PER_SEC;

    //Translate application time to Leap time
    LeapRebaseClock(clockSynchronizer, cpuTime, &targetFrameTime);

    //Get the buffer size needed to hold the tracking data
    result = LeapGetFrameSize(*connHandle, targetFrameTime, &targetFrameSize);
    if (result == eLeapRS_Success) {
        //Allocate enough memory
        LEAP_TRACKING_EVENT* interpolatedFrame = (LEAP_TRACKING_EVENT*)malloc((size_t)targetFrameSize);
        //Get the frame
        result = LeapInterpolateFrame(*connHandle, targetFrameTime, interpolatedFrame, targetFrameSize);
        if (result == eLeapRS_Success) {
            onFrame(interpolatedFrame);
            //Free the allocated buffer when done.
            free(interpolatedFrame);
        }
        else {
            printf("LeapInterpolateFrame() result was %s.\n", ResultString(result));
        }
    }
    else {
        printf("LeapGetFrameSize() result was %s.\n", ResultString(result));
    }
    //Calculate the application time
    cpuTime = (clock_t).000001 * clock() / CLOCKS_PER_SEC;//microseconds
    //Synchronize the clocks
    LeapUpdateRebase(clockSynchronizer, cpuTime, LeapGetNow());
#else
    LEAP_TRACKING_EVENT* frame = GetFrame();
    onFrame(frame);
#endif

    for (int hi=0;hi<2;hi++) for (int fi = 0; fi < 5; fi++) for (int ni = 0; ni < 5; ni++)
    {
        auto& dat = lphnd[hi][fi][ni];
        if (dat.mjt && dat.jtScCd > 0) {
            dat.jtScCd -= frameTime;
            if (dat.jtScCd <= 0.f)
            {
                dat.mjt->scaleBreakThreshold(0.003);
            }
        }

    }
}

void LeapMan::onFrame(_LEAP_TRACKING_EVENT* frame)
{
    if (sbL && root->getParent()!=sbL) {
        root->setParent(sbL);

        glm::quat lastInvQt(1, 0, 0, 0);
        for (int fi = 0; fi < 5; fi++) for (int ni = 0; ni <= 3; ni++) {
            auto np = sbL->Pmx->GetNodeManager()->GetMMDNode(1 + fi * 4 + ni - 1);
            auto ns = sbL->Pmx->GetNodeManager()->GetMMDNode(1 + fi * 4 + ni);
           // assert(ns == sbL->Pmx->GetNodeManager()->GetMMDNode(1 + fi * 4 + ni));
            DP(("np %f,%f,%f", np->pmxBone->m_position.x, np->pmxBone->m_position.y, np->pmxBone->m_position.z));
            DP(("ns %f,%f,%f", ns->pmxBone->m_position.x, ns->pmxBone->m_position.y, ns->pmxBone->m_position.z));
            glm::vec3 A = np->pmxBone->m_position;
            glm::vec3 B = ns->pmxBone->m_position;
             
            vector3df dir(B - A);
            dir=dir.normalize();
            glm::vec3 ha = dir.getSphericalCoordinateAngles() * DEGTORAD;  ha.z =   - ha.z;
            glm::quat rotationQuat = ha;

            if (ni >0)
            rotationQuat = lastInvQt*rotationQuat ;
            //glm::quat invRttQuat = glm::inverse(rotationQuat);
            lastInvQt = glm::inverse(glm::quat(ha));
            np->baseRt = rotationQuat;
            np->baseRtI = glm::inverse(rotationQuat); 
            glm::vec3 rtt = glm::eulerAngles(rotationQuat)*360.f;
            DP(("%d-%d rt %f,%f,%f",fi,ni, rtt.x, rtt.y, rtt.z));
        }
        DP(("leap INITED"));
    }

    auto camMat = Ctx->mmd?  Ctx->mmd->camMat:glm::mat4(1);

    if (frame && (frame->tracking_frame_id > lastFrameID)) {
        lastFrameID = frame->tracking_frame_id;
        //DP(("Frame %lli with %i hands.\n", (long long int)frame->tracking_frame_id, frame->nHands));
        for (uint32_t h = 0; h < frame->nHands; h++) {
            LEAP_HAND* hand = &frame->pHands[h];
            int hid = hand->type == eLeapHandType_Left ? 0 : 1;
            bool left = hid == 0;
            auto& hd = *snH[hid]; auto& hp = hand->palm.position; auto& hq = hand->palm.orientation;
            core::vector3df pos = vl2i(hp);

            core::quaternion ort(-hq.x, -hq.y, hq.z, hq.w);

            core::vector3df rtt; ort.toEuler(rtt); 
            //et[hid]->addAndFloatValues(pos.X, pos.Y, pos.Z);
            //er[hid]->addAndFloatValues(rtt.X, rtt.Y, rtt.Z);

            hd.setPosition(pos);
            hd.setRotation(rtt * RADTODEG);

            hd.updateAbsolutePosition();
            LeapHandData& dat=Lhd[hid];
            dat.mt = hd.getRelativeTransformation();
            dat.mtabs = hd.getAbsoluteTransformation();
            matrix4 mti; dat.mt.getInverse(mti);
            matrix4 mli;
            dat.hid = hid;
            dat.pinch = hand->pinch_strength;
            dat.lastGrab = dat.grab;
            dat.grab = hand->grab_strength;
            
            dat.mtArm.setTranslation(vl2i(hand->arm.prev_joint));
            vector3df armrtt; ql2i(hand->arm.rotation).toEuler(armrtt);
            dat.mtArm.setRotationRadians(armrtt);

            auto sb = hid == 0 ? sbL : sbR;
            if (sb) {
                auto Pmx = sb->Pmx;
                auto nodeMan = Pmx->GetNodeManager();
                auto ndRoot = nodeMan->GetMMDNode(0);
                //auto mi = sb->matAbsInv;
                glm::mat4 m = ( dat.
                    mt).getTransformTR();
                ndRoot->setAnimationMatrix(glm::translate(camMat, {0,-20,30}) * m);
            }
            //DP(("HD %d d %6.2f,%6.2f a %6.2f, %6.2f", hid, hand->pinch_strength, hand->pinch_distance,hand->grab_strength, hand->grab_angle*RADTODEG));
            for (int fi = 0; fi < 5; fi++) for (int ni = 0; ni < 5; ni++)
            {
                auto pos = vl2i(ni < 4 ? hand->digits[fi].bones[ni].prev_joint : hand->digits[fi].bones[3].next_joint);

                auto& sn = *snF[hid][fi][ni];
                auto np = pos;
                auto nq = ql2i(hand->digits[fi].bones[ni > 3 ? 3 : ni].rotation);
                matrix4 m; m.setTranslation(np);
                sn.setPosition(np);
               
                vector3df rtt; nq.toEuler(rtt);  //if (fi == 0) rtt.z += core::PI / 2.3 * (hid == 0?1:-1);
                m.setRotationRadians(rtt);
                //m = m * nq.getMatrix();
                auto mi = (ni == 0  ? mti : mli);
                m.getInverse(mli);
                matrix4 mr = (mi * m); //relative  

                sn.setTransformTR(mr);
                sn.updateAbsolutePosition();
                dat.mtf[fi][ni] = mti * m;// sn.getAbsoluteTransformation();
                dat.sn[fi][ni] = &sn;
                dat.sb = hid == 0 ? sbL : sbR;
                {
                    rtt = dat.mtf[fi][ni].getTranslation();
                    //if (fi == 1 && ni >= 1) {
                    //    DP(("HD %d-%d %6.f, %6.f, %6.f", fi, ni, rtt.X, rtt.Y, rtt.Z));
                    //}
                }
                
                if (sb && ni >=1 && ni<=3  ) 
                {
                    auto Pmx = sb->Pmx;
                    auto nodeMan = Pmx->GetNodeManager();
                    auto nd = nodeMan->GetMMDNode(1+fi*4+ni);
                    
                    auto q = glm::quat(glm::mat3(glm::mat4(mr)));// *nd->baseRtI; //q.x = - q.x; // q.z = -q.z;
                    if (fi == 0) {
                        if (ni > 1)q = glm::angleAxis((ni == 1 ? PI / 6.f : PI / 6.f) * (hid == 0 ? -1 : 1), glm::vec3(0, 0, 1)) * q;
                        auto rtt = glm::eulerAngles(q); rtt *= 1.5f;
                       // if (ni>1) q = rtt;
                    }
                    nd->SetAnimationRotate(q);
                    //DP(("set %s",nd->GetName().c_str()));
                }
            }


            if (cbOnHandTracking) {

                cbOnHandTracking(dat);
            }
        }

    }
}

void LeapMan::toggleVisible()
{
#if MMD_PMX_HAND
    root->setVisible(false);// !root->isVisible());
    if (sbL) sbL->setVisible(!sbL->isVisible());
    if (sbR) sbR->setVisible(!sbR->isVisible());
#else
	root->setVisible(!root->isVisible());
#endif
}

saba::MMDNode* LeapMan::getMmdNode(int hid, int fid, int nid)
{
    
    return  (hid==0?sbL:sbR)->Pmx->GetNodeManager()->GetMMDNode(1 + fid * 4 + nid);
}

saba::MMDJoint* LeapMan::connectNodeWithRb(int hid, int fid, int nid, saba::MMDRigidBody* rb)
{
    auto& dat = lphnd[hid][fid][nid];
    auto jt = dat.mjt;
    
    auto ndA=getMmdNode(hid, fid, nid);
    auto Pmx = (hid == 0 ? sbL : sbR)->Pmx;

    if (jt)
    {
        auto c=jt->isConnected();
        DP(("brk %d",c));

        Pmx->GetMMDPhysics()->RemoveJoint(jt);
        dat.mjt = jt = nullptr;
    }
    if (rb) {
        jt = dat.mjt = Pmx->connectRb(ndA->rb0, rb);
        dat.jtScCd = 1.f;
    }
    return jt;
}

bool LeapMan::isNodeConnected(int hid, int fid, int nid)
{
    auto& dat = lphnd[hid][fid][nid];
    if (!dat.mjt) return false;

    return dat.mjt->isConnected();
}


#endif
