#pragma once


#include <functional>

#define _IRR_STATIC_LIB_


 

#define APP_HAS_EQV 1
#define FORCE_CTRCAM  1

extern float gGameTime, gSceneTime,gEditorTime, gFrameTime, gTimeMul;
extern glm::vec3 gGravity;


#include "AppTypes.h"
#include "irrlicht.h"
#include "UaLibEvtRcv.h"
#include "stlUtils.h"
#if HAS_MIDI
#include "MidiMan.h"
#endif
#include "PythonMan.h"
#include "../../CommonStaticLib/oxr/BasicXrApp/OxmTypes.h"
#if APP_HAS_EQV
namespace EQVisual
{
	class EQV;
	class SnCsParticle;
}
#endif

namespace irr {
	namespace video{
		class VkDriver;
	}
	namespace scene {
		class CSceneNodeAnimatorCameraTouchControl;
		class IrrMMD;
	}
}
namespace uu {
	class ThreadPool;
}
namespace saba {
	class MMDRigidBody;
	class MMDJoint;
	struct PhysicsEngineObjectUserData;
}
namespace irr::scene
{
 
}
namespace ualib
{
	class CXAudioManager;
	class IUaLibBase;
	enum viewIds { ev_main = 0,  ev_fpv, ev_post, ev_ui,ev_hdr,ev_bloom, ev_bloom1};
	struct LibCreateionParam
	{
		void* WindowId;
		uint32_t app_code;
		irr::io::path dataPath,tempPath,curUaLibPath,appPath,picturePath;  //app's dir
		irr::SIrrlichtCreationParameters ipm;
	};
	struct addMmdObjParam {
		int flag = 0;
		bool autoShot = 0;
		int pm1 = 0; irr::scene::IrrSaba* tgtSb{}; bool nextSb = false;
		int countRt = 1;
		float scale = 1.f, angle = 15.f, spdMul = 2.f; 
		float density = 1.f;
		float friction = 0.5f;
		irr::core::vector3df camOfsCtr = irr::core::vector3df(0, 0, 0), dirOfs{};
		glm::vec3 camOfs = glm::vec3(0);
		bool toMMD = false, p2p = false, noRtt = false, fromEmitter = false;
		bool checkMode = false;
		int addFluid = false;
		bool RCombine = 0;
		int dupCount = 1, combineMode = 0 ;
		int dupToSave = 0;
		glm::vec3 dupTrsInc = glm::vec3(0);
		glm::vec3 dupRttInc = glm::vec3(0);
		float dupScaleInc = 0.f;
		int lsXC = 8, lsYC = 3;
		int dupModelId = 0;

		//tmp
		int toUpdate = 0;
		int setMmdIdx = -1;
	};
	struct VoxelSceneParam {
		bool autoReset = 1, speedDelay = 1, oit = 0;
		float resetTime=1.f;
		float alpha = 1.f, scale = 1.f;
		float btShell=1000, btInner=600;
		int fwFrame=0;
	};
	struct LibGlobalData
	{
		int frameCount = 0;
		int fps = 0;
		float fpsReal = 0.f;
		double time=0.0,prevTime=0.0, lastEvtTime;
		double deltaTime;

		int mediaSrcFrameId = -2;  // -1 started,  -2 stopped
		int mediaSrcFrameIdDrawn = -1;
		int mediaSrcTimeUs = 0;//
		
		float mouseX=0, mouseY=0;
		float xrX=0, xrY=0, xrZ=0; //OpenXR rHandd 0
		int xrUpdated=0;
		int ptCount = 1;
		int supportFW=1;
		int  scrWidth = 0, scrHeight = 0; //main view size
		int screenX = 0, screenY = 0;
		uint32_t oxrControl[2] = {}; //0:cam 1:sn  0x10:saba  
		void* oxrObj[2] = {};
		bool fpsMode = false;
		int toneForHdr = 1;
		uint32_t bgColor = 0;
		irr::core::position2di pointerPos, pointerClick;
		irr::core::vector3df baseLightPos; bool lightPosChanged = true;
		bool EqvUseFFAudio = false;
		bool libCaptureScreen = true;
		irr::scene::ISceneNode* RootSn;
		irr::scene::ICameraSceneNode* CamNormal{}, * CamRtt{}, * CamFpv{};// , * CamMovie{}, * CamMovieBeg{};
		irr::scene::ILightSceneNode* Light[8]{};
		irr::video::ITexture* texCube{}, * texBg{};
		irr::core::matrix4 CMCloudMat;
		glm::mat4 camRbMat=glm::mat4(1);
		bool usingCamRbMat = false;
		//sb
		float lookOnVel = 1.f;
		bool camRttOnSbVel = false;

		//tmp
		int swsid = 0;
		float sceneTime = 0;
        int frameMoved;  //touch ptr move in frame
		bool updateStartPos = false;
		double timeMul = 1.0;
		bool ignoreNextMouseUp[8] = {};
		bool oneShotForce = false;
		bool debugScene = true;
		float tmpPm=1;
		addMmdObjParam apm;
		VoxelSceneParam vspm;
    };
	struct LibGlobalSetting
	{
		
	};
	struct StrMsgStruct {
		std::string msg,pm1;		
	};

#if APP_HAS_EQV
	struct EQV_Data
	{
		int disableEQV = 0;
		//int particleOn = 1;
		//int eqvHintMsg = 1;
		//int engineId = 1;
		int baseColorSatPct = 50;
		int rainbowStepMul = 50;
		int rainbowSpdMul = 20;
		//int picFwOn = 1;
		//int pfwPicSrc = 0;
		//int pfwFreq = 100;

		//int txtFwOn = 1;
		//int txtFwColorType = 0;  //particle FW text color
		//int txtFwMode = 0;

		//int wavCld = 2;
		//int wavY = 0;

		unsigned int channelDataMask = 1;//  channel data for wave..
		//int touchFwMode = 1;

		int nextBandCount = 32;
		int useLocalStyle = 0;
		//runtime
		int inited = 0;
		int overrideBandCount = 0;
 
		bool wavCloudDrawing;

		EQVisual::EQV *eqv = nullptr;
		EQVisual::SnCsParticle *ifPPt = nullptr;
		//int engineIdThisRun = -1;

		void setUseStyle(int us) { useStyle = us; if (useStyle == 0) overrideBandCount = 0; }
		int getUseStyle() { return useStyle; }


	private:
		int useStyle = 0;
		
	};
#define EQV_IF_PPT  if (Ctx->eqv.ifPPt) Ctx->eqv.ifPPt
#endif
	typedef std::function<bool(irr::video::IImage *)>  CbScreenCapture;

	template<typename T>
	class SPHolder {
	public:
		SPHolder(T x) :x(x) {	}	//~SPHolder() {		DP(("free"));		}
		T x;
	};
	template<typename T>
	std::shared_ptr<SPHolder<T>> make_SPHolder(T value) {
		return std::make_shared<SPHolder<T>>(value);
	}

#define FRAMEWAITER_CALL_B(_x_) FrameWaiter fwcv##_x_; fwcv##_x_.waitNframeAndRun((_x_), [=](FWTask& task) 
#define FRAMEWAITER_CALL_BI(id, _x_) FrameWaiter fwcv##id; fwcv##id.waitNframeAndRun((_x_), [=](FWTask& task) 
#define FRAMEWAITER_CALL_BN(_x_,name) FrameWaiter fwcv##name##_x_; fwcv##name##_x_.waitNframeAndRun((_x_), [=](FWTask& task) 

	struct FWTask {
		int targetFrame, repeatCountDown,count, frameN;		
		void* pm;
		std::function<void(FWTask& t)> callback, onOtherFrame;
		float rat01;
	};
	class FrameWaiter {
	public:
		FrameWaiter() : currentFrame(0) {}


		// Adds a callback that should be executed after 'frames' number of frames.
		FrameWaiter& waitNframeAndRun(int frameN, std::function<void(FWTask& t)> callback,int count = 1, int repeatWaitFrameN=1) {
			//mtx.lock();
			int targetFrame = globalFrameCounter+currentFrame + frameN;
			FWTask tsk({ targetFrame,count,count, repeatWaitFrameN,0, callback });
			
			if (frameN == 0) {
				callback(tsk); tsk.repeatCountDown--;
			}
			if (tsk.repeatCountDown>0)
			pendingTasks.push_back(tsk);
			currentFrame += frameN+ repeatWaitFrameN*count;  // Update the current frame counter to reflect this task's target frame
			//mtx.unlock();
			return *this;  // Return reference to this object for chaining
		}

		// This function should be called every frame.
		static void updateFrame(int addFrame) {
			{
				tasks.insert(tasks.end(), pendingTasks.begin(), pendingTasks.end());
				pendingTasks.clear();
			}
			globalFrameCounter+=addFrame;
			//mtx.lock();
			for (auto it = tasks.begin(); it != tasks.end(); ) {
				if (globalFrameCounter >= it->targetFrame) {
					it->rat01 = 1.f - float(it->repeatCountDown) / it->count;
					if (it->repeatCountDown>0)
						it->callback(*it );
					it->repeatCountDown--;
					if (it->repeatCountDown <= 0) {
						it = tasks.erase(it);
					}
					else {		
						it->targetFrame += it->frameN;
						++it;
					}
				}
				else {
					if (it->onOtherFrame) it->onOtherFrame(*it);
					++it;  // Move to the next task					
				}
			}
			//mtx.unlock();

		}
		static int currentFrameCount() { return globalFrameCounter; }
		static FWTask& getLastTask() { return pendingTasks.back(); }
	private:
		
		inline static std::mutex mtx;
		inline static std::vector<FWTask> tasks; // Static vector to hold all tasks
		inline static int globalFrameCounter=0;  // Static counter to track the total number of frames
		inline static std::vector<FWTask> pendingTasks; // Tasks to be added
		int currentFrame, repeatCount;  // Tracks when the next task should execute
	};

	class UaLibContext
	{
		friend class UaLibMain;
	public:
		UaLibContext(IUaLibBase* lib);
		virtual ~UaLibContext();
		bool isApp(uint32_t appCode) { return appCode == app_code; }
		LibGlobalData gd;
		LibGlobalSetting gs;
		LibCreateionParam CP;
		UaJsonSetting jss{ "appSettings" };

		IUaLibBase* getLib() { return mLib; }
		void						setDevice(irr::IrrlichtDevice* device, irr::IrrlichtDevice* device1 = nullptr);
		irr::IrrlichtDevice* getDevice() { return mIrrDevice; };
		irr::video::IVideoDriver* getDriver(int id = 0) { return id ? DriverOfflline : Driver; }
		irr::video::IVideoDriver* getDriverOnThread()
		{
			if (std::this_thread::get_id() == Driver->dsd.threadId) return Driver;
			else return DriverOfflline;
		}
		irr::scene::ISceneManager* getSceneManager() { return SceneManager; }
		irr::io::IFileSystem* getFileSystem() { return FileSystem; }
		UaLibEvtRcv* getEvtRcv() {return mEvtRcv; }
		irr::video::IImage* getImage(const irr::io::path& filename);

		std::string readStringFromFile(irr::io::path filepath);
		void writeStringToFile(std::string s,irr::io::path filepath);
		bool UaCopyFile(irr::io::path srcFile, irr::io::path tgtFileName);
		irr::io::path getDataFilePath(irr::io::path fn) { irr::io::path tp=CP.dataPath; return tp.append(fn); }
		irr::io::path getTempFilePath(irr::io::path fn) { irr::io::path tp=CP.tempPath; return tp.append(fn); }
		irr::io::path getAppFilePath(irr::io::path fn) { irr::io::path tp=CP.appPath; return tp.append(fn); }	
		irr::io::path getPictureFilePath(irr::io::path fn) { irr::io::path tp = CP.picturePath; return tp.append(fn); }
		irr::io::path getOrigCurUaLibFilePath(irr::io::path fn) { irr::io::path tp = CP.curUaLibPath; return tp.append(fn); }
		int		getCameraId() { return mCameraId; }
		irr::scene::ICameraSceneNode* getActiveCam() { return mCameraId == 1 ? gd.CamRtt : gd.CamNormal; }
		void	setCameraId(int id);
		irr::scene::ICameraSceneNode* getViewCamera(int viewId=0) { return curCamera[viewId]; }
		void setViewCamera(irr::scene::ICameraSceneNode* cam, int viewId = 0,bool activate=true);
		void UpdateCamRtt();
		bool	isNormalCamera() { return mCameraId == 0; }
		bool getScrPtIntersectionOnPlane(int x, int y, irr::core::plane3df plane, irr::core::vector3df &outPt, irr::scene::ICameraSceneNode *cam=nullptr);
		irr::video::VkDriver* getVkDrv(int id) { return (irr::video::VkDriver*)getDriver(id); }

		void frameUpdate();
		void updateViewSizes();
		void setViewState(int viewId, int isOn, irr::core::recti rc, uint32_t w, uint32_t h);
		int getViewState(int viewId) { return viewOn[viewId]; }

		int mainViewId = 0;
		irr::core::dimension2du mainViewSize() { return rtSizes[mainViewId]; }
		irr::core::dimension2du getViewSize(int i) { return rtSizes[i]; }
		irr::core::recti	getViewRC(int i) { return viewRC[i]; }

#if APP_HAS_EQV
		EQV_Data eqv;
#endif
		irr::scene::IrrMMD* mmd;
		PythonMan* pym{};
		CXAudioManager *audioMan{};
		midilib::MidiMan Midi, MidiFw;

		bool useMidRT = 1;// !MMD_CONTROL_SD;
		bool vrOn = false; //VR 

		irr::video::ITexture* texMidRT[8]{}; //0,1: viewid, 2:ui   OR bloom use 3:hdr
		void ensureMidRT(int i, int div = 1, irr::core::dimension2du overrideSize = { 0,0 }, irr::video::ECOLOR_FORMAT colorFormat = irr::video::ECF_A8R8G8B8,
			void* extHwBuffer = nullptr)
		{
			auto tgtSize = Driver->getScreenSize() / div;
			if (overrideSize.Width > 0 && overrideSize.Height > 0) tgtSize = overrideSize;


			if (texMidRT[i] == nullptr || texMidRT[i]->getSize() != tgtSize)
			{
				if (texMidRT[i]) Driver->removeTexture(texMidRT[i]);
				std::string szTn = i==0? "midrt<DepthSamp>":"midrt";
				if (extHwBuffer)
				{
					szTn=ualib::strFmt( "<EHB><vp.depthRT> %llu", *(uint64_t*)extHwBuffer);
				}
				
				texMidRT[i] = Driver->addRenderTargetTexture(tgtSize, szTn.c_str(), colorFormat);
			}
		}
		
		void setSharedRbCb(saba::MMDRigidBody* rb);

		void cbHitMidi(saba::PhysicsEngineObjectUserData* hit);

		//screen capture
		CbScreenCapture mCbScreenCaptrue;
		int mpThreadNum=1;
		uu::ThreadPool* mpTPool;
		//bool mCbCapImage = true;
		bool scenePaused = false;
		void setNeedCaptureScreen(bool sc, bool needTexture);
		int FrameStepMul = 1;
		void setFixFrameTime(bool fix, double frameTime,int baseFPS) { mUseFixFrameTime = fix; mFixFrameTime = frameTime;
			frameStep = irr::core::clamp(int(FrameStepMul * frameTime* baseFPS + 0.5),1,32);
			DP(("SSSSSSSSSSSSSSSSSSSSSSSSSSSSSSet \nFRAME TIME %f\nFRAME STEP %d",mFixFrameTime,frameStep));
		}
		void changeSpeed(float spd);
		double getFixFrameTime() { return mFixFrameTime; }
		bool getUseFixFrameTime() { return mUseFixFrameTime; }

		irr::core::vector3df getPointerCamSpacePos(irr::core::vector2df *xy=nullptr) {
			return irr::core::vector3df(
				gd.screenX + (xy? xy->X:gd.mouseX),
				gd.screenY - (xy? xy->Y:gd.mouseY),
				SceneManager->getActiveCamera()->getScreenPlaneDistance(gd.scrHeight)
			);
		}
		irr::core::vector3df getPointerHitRealPos(irr::core::vector2df* xy = nullptr) {
			irr::core::vector3df pos = getPointerCamSpacePos(xy);	
			irr::core::matrix4 mi; SceneManager->getActiveCamera()->getViewMatrix().getInverse(mi); mi.transformVect(pos);
			//SceneManager->getActiveCamera()->getViewMatrix().inverseTranslateVect(pos);
			return pos;
		}
		const int& getFrameStep() { return frameStep; }
		void morphCamTgtTo(float dur, irr::core::vector3df* tgt = nullptr, irr::core::vector3df* rtt = nullptr);
		irr::scene::CSceneNodeAnimatorCameraTouchControl* getCamTCAnimator() { return ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*gd.CamRtt->getAnimators().begin()); }
	protected:
		bool mNeedCaptureScreen = false; bool needScrTexture=false;
		bool mUseFixFrameTime = false;
		double mFixFrameTime = 1.0 / 60;
		int frameStep=1;
	private:
		IUaLibBase* mLib{};
		irr::IrrlichtDevice* mIrrDevice{}, * IrrDeviceOffline{};
		irr::video::IVideoDriver* Driver{}, * DriverOfflline{};
		irr::scene::ISceneManager* SceneManager{};
		irr::io::IFileSystem* FileSystem{};

		UaLibEvtRcv* mEvtRcv;
		std::map<irr::io::path, irr::video::IImage* >  imgMap;
		int mCameraId = 0;
		uint32_t app_code = 0;
		irr::scene::ICameraSceneNode* curCamera[16]{};

		//multiview
		uint32_t viewOn[16] = { 1,0 };
		irr::core::recti viewRC[16]{};
		irr::core::dimension2du rtSizes[16];

		int midiChFrCC[16]{}; // midi play channel frame count
	};


	class ILibStage;

	class IUaLibBase
	{
	public:

		virtual void LibInitialize(LibCreateionParam *dpm) = 0;
		virtual void LibFinalize() = 0;

		virtual void SetStage(ILibStage* stg) = 0;
		virtual ILibStage* CurStage() = 0;
		virtual irr::IrrlichtDevice *getDevice() = 0;
		virtual void LibProcessEvent() = 0;
		virtual void LibUpdate() = 0;
		virtual void LibRender() = 0;
		virtual void libOnResize(uint32_t w, uint32_t h, void* win) = 0;
		virtual void libOnIdle() = 0;
		virtual void libPostCommand(int cmdId, int64_t pm1, int64_t pm2) =0;
		virtual void libPostEvent(const irr::SEvent &evt) = 0;
		virtual int64_t  libSendEvent(const irr::SEvent &evt, bool driverLock) = 0;
		virtual void libOnSurfaceDestroy() = 0;
		virtual void libAddFileArchive(const char* fn) = 0;
		virtual void sendMsgTolib(int msg, int64_t pm1, int64_t pm2, int64_t pm3) {};
		virtual void sendStringMsgToLib(std::string msg, std::string pm1){};
		virtual void renderLockRun(std::function<void()> cb)=0;
		virtual UaLibContext *getContext() = 0;
		UP_LOCK_DECLVAR_TYPE* getLock() { return mtLibMain; }


	protected:
		UP_LOCK_DECLVAR_TYPE* mtLibMain{};

	};
	IUaLibBase* CreateUaLib();

}

