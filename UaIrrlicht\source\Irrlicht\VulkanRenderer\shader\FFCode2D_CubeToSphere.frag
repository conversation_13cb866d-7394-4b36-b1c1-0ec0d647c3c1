#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
#define M_PI    3.14159265358979323846
#define M_PI_2  1.57079632679489661923
layout (binding = 0) uniform UBO 
{


				float blurStep;
				float blurWeightMul;
				float sigma, sigma2;
				int blurSize, addPattern, i02, i03;
				float apLenMul, apHeightMul, apHeightAdd, f13;
				float apf1, apf2, _22, _23;
				vec4 matRtt;
				vec4 pad[15 - 5];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2DArray g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;





float blurknl(in float x, in float sigma)
{
	return 0.39894*exp(-0.5*x*x/(sigma*sigma))/sigma;
}

float blurknl2(in float x,in float y, in float s2)
{
	return 0.15915*exp(-0.5*(x*x + y*y)/s2)/s2;
}

mat3 rotationMatrix(vec3 axis, float angle)
{
    axis = normalize(axis);
    float s = sin(angle);
    float c = cos(angle);
    float oc = 1.0 - c;
    
    return mat3(oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s,  
                oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s, 
                oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c        
               );
}

void main( )
{
    vec2 uv=i_tex0;
    vec3 sp=vec3(2 * M_PI * uv.x, M_PI * uv.y - M_PI_2, 1.f);
    sp= vec3(sp.z*cos(sp.y)*cos(sp.x), sp.z*sin(sp.y),sp.z*cos(sp.y)*sin(sp.x));
    vec3 cb= sp* (length(sp)/max(abs(sp.x),max(abs(sp.y),abs(sp.z))));

    float eps=0.00001;
    mat3 t=mat3(1);
    float cid=0;
    if (abs(cb.z-1.0)<eps)
    {
        cid=0;
       // outFragColor=vec4(1,0,0,1);
        t=rotationMatrix(vec3(0,1,0),M_PI);
    }
    else if (abs(cb.z+1.0)<eps) 
    {
        cid=2;
      //  outFragColor=vec4(0,1,0,1);
        t=mat3(1);
    }
    else if (abs(cb.x+1.0)<eps) 
    {
        cid=1;
      //  outFragColor=vec4(0,0,1,1);
      t=rotationMatrix(vec3(0,1,0),M_PI_2);
    }
    else if (abs(cb.x-1.0)<eps) 
    {
        cid=3;
        //outFragColor=vec4(1,0,1,1);
          t=rotationMatrix(vec3(0,1,0),-M_PI_2);
    }
    else if (abs(cb.y+1.0)<eps) 
    {
        cid=4;
       // outFragColor=vec4(1,1,0,1);
         t=rotationMatrix(vec3(1,0,0),M_PI_2);
         t[0][0]=-t[0][0];
    }
    else// if (abs(cb.x+1.0)<eps) 
    {
        cid=5;
       // outFragColor=vec4(0,1,1,1);
        t=rotationMatrix(vec3(1,0,0),-M_PI_2); t[0][0]=-t[0][0];
    }

    vec2 t2= (t*cb).xy;
    t2=(t2+1)/2;
    vec4 c0= texture(g_tex0_sampler,vec3(t2.x,t2.y,cid));

  //  uv.y=uv.y/2;
  //c0=texture(g_tex0_sampler,uv.xy);
		outFragColor = c0;//vec4(i_tex0,0,1);
}