#include "AppGlobal.h"
#include "CVoxelMeshSceneNode.h"
#include "IVideoDriver.h"
#include "ISceneManager.h"
#include "S3DVertex.h"
#include "SMeshBuffer.h"
#include "irrMMD.h"
#include "IDynamicMeshBuffer.h"
#include "CDynamicMeshBuffer.h"
#include "Helpers/ThreadPool/ThreadPool.h"
#include "IrrFw/eqv/EQV.h"
//#include "saba/Model/MMD/MMDPhysics.h"
using namespace glm;
using namespace irr::video;
using namespace irr::scene;
using namespace irr;

namespace {
	bool breakable = 1;
	static video::E_VERTEX_TYPE vertexType= EVT_STANDARD;
}
#define RESET_TO_THROW 0
#define SHOWING_STAGE	0
CVoxelMeshSceneNode::CVoxelMeshSceneNode(std::wstring meshFile, ISceneNode* parent, ISceneManager* mgr,
	irr::scene::IrrMMD* _mmd, const core::vector3df& position,
	const core::vector3df& rotation, const core::vector3df& scale)
	: IMeshSceneNode(parent, mgr, -1, position, rotation, scale),
	Mesh(0),   mmd(_mmd), hbVertices(nullptr), hbIndices(nullptr), hbInstances(nullptr)
{
#ifdef _DEBUG
	setDebugName("CInstancedMeshSceneNode");
#endif
	Ctx = mmd->Ctx;
	Eqv = mmd->sb0->Eqv;
	initPipeline();
	meshLocalTransform.setTranslation({ 0, 0, 0 });
	objectSize = 1.0f;
	objectMass = breakable?100.1f:1.0f;
	createInitialObjects(meshFile.c_str(), 0, 1.0f);
	Objs.reserve(INITIAL_CAPACITY);  // Pre-allocate hash table buckets
    instanceData.reserve(INITIAL_CAPACITY);  // Pre-allocate instance data

}

CVoxelMeshSceneNode::~CVoxelMeshSceneNode()
{
	if (Mesh)
		Mesh->drop();
	if (hbVertices)
		hbVertices->drop();
	if (hbIndices)
		hbIndices->drop();
	if (hbInstances)
		hbInstances->drop();
}

void CVoxelMeshSceneNode::initPipeline()
{
	Driver = SceneManager->getVideoDriver();

	if (vertexType== EVT_STANDARD){	
	core::array<SVertexElement> el;
	//S3DVertex
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT3, 0));
	el.push_back(SVertexElement(EVES_NORMAL, EVET_FLOAT3, 0));
	el.push_back(SVertexElement(EVES_COLOR, EVET_COLOUR, 0));
	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT2, 0));
	// Instance attributes
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT4, 0, 1, 0, true)); // Transform matrix (column 1)
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT4, 1, 1, 0, true)); // Transform matrix (column 2)
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT4, 2, 1, 0, true)); // Transform matrix (column 3)
	el.push_back(SVertexElement(EVES_POSITION, EVET_FLOAT4, 3, 1, 0, true)); // Transform matrix (column 4)
	el.push_back(SVertexElement(EVES_COLOR, EVET_FLOAT4, 0, 1, 0, true)); // Instance color
	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT2, 1, 1, 0, true)); // UV offset
	el.push_back(SVertexElement(EVES_TEXTURE_COORD, EVET_FLOAT2, 2, 1, 0, true)); // UV scale
	vertexType = Driver->registerVertexType(el);
	VkMaterialRenderer* mr = (VkMaterialRenderer*)Driver->getMaterialRenderer(EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING);
	mr->vertexType = vertexType;
	mr = (VkMaterialRenderer*)Driver->getMaterialRenderer(EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING_OIT);
	mr->vertexType = vertexType;
	}
}

void CVoxelMeshSceneNode::OnRegisterSceneNode()
{
	if (IsVisible)
		SceneManager->registerNodeForRendering(this);
	ISceneNode::OnRegisterSceneNode();
}

void CVoxelMeshSceneNode::render()
{
	if (instanceCount < 1) return;
	video::IVideoDriver* driver = SceneManager->getVideoDriver();
	driver->setTransform(video::ETS_WORLD, AbsoluteTransformation);

	auto& mt = Mesh->getMeshBuffer(0)->getMaterial();
	

	auto* cbcp = getMaterial0CbCp();
	//VkFixedFunctionMaterialRenderer* mr = (VkFixedFunctionMaterialRenderer*)driver->getMaterialRenderer(EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING);
	cbcp->iv = mmd->Ctx->gd.vspm.oit;
	mt.MaterialType = cbcp->iv == 1 ? EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING_OIT : EMT_TRANSPARENT_ALPHA_CHANNEL_2SIDES_INSTANCING;
	driver->setMaterial(mt);
	driver->drawInstancedHardwareBuffer(
		hbVertices,
		hbIndices,
		hbInstances,
		vertexType,
		scene::EPT_TRIANGLES,
		EIT_16BIT,
		instanceCount,
		Mesh->getMeshBuffer(0)->getIndexCount(),
		0
	);

	driver->setMaterial(SMaterial());

}

const core::aabbox3d<f32>& CVoxelMeshSceneNode::getBoundingBox() const
{
	return Mesh->getBoundingBox();
}

video::SMaterial& CVoxelMeshSceneNode::getMaterial(u32 i)
{
	static video::SMaterial s;
	if (!Mesh) return s;
	return Mesh->getMeshBuffer(0)->getMaterial();
}

u32 CVoxelMeshSceneNode::getMaterialCount() const
{
	return 1;
}

void CVoxelMeshSceneNode::OnAnimate(u32 timeMs)
{
	ISceneNode::OnAnimate(timeMs);
	updateFrame();
}




IShadowVolumeSceneNode* CVoxelMeshSceneNode::addShadowVolumeSceneNode(const IMesh* shadowMesh, s32 id, bool zfailmethod, f32 infinity)
{
	return nullptr;
}

void CVoxelMeshSceneNode::setReadOnlyMaterials(bool readonly)
{
}

bool CVoxelMeshSceneNode::isReadOnlyMaterials() const
{
	return false;
}

void irr::scene::CVoxelMeshSceneNode::loadVox(const std::string& filePath, glm::vec3 baseOfs, int div, bool edgeOnly,float cubeSize  )
{
	
	voxLdr.loadFromFile(filePath, { edgeOnly, div });
	objectSize = RESET_TO_THROW ? cubeSize * 1.25f: cubeSize;
	clear();
	auto objectSize3 = objectSize * objectSize * objectSize;
	
	auto size = uvec3(voxLdr.sizeX, voxLdr.sizeY, voxLdr.sizeZ);
	 baseOfs += vec3(-(float)voxLdr.sizeX, baseOfs.y, -(float)voxLdr.sizeZ) /2.f ;
	auto count = voxLdr.voxels.size();
	auto& voxels = voxLdr.voxels; 
	Objs.reserve(count);
	auto sb0 = mmd->sb0, sb = sb0;
	auto baseRb= sb0->Pom->groundRb;
	auto baseRbPos = baseRb->getPosition();

	obids.resize(voxLdr.sizeX * voxLdr.sizeY * voxLdr.sizeZ);
	memset(&obids[0], 0, sizeof(VoxData) * obids.size());
	float btShell = mmd->Ctx->gd.vspm.btShell;
	float btInner = mmd->Ctx->gd.vspm.btInner;

	vec3 vel = vec3(0, 0, 0);
	PhyObjParam pmt{};
	
	auto& vms = mmd->sb0->Eqv->GetPPT()->vtxMats;


	for (int i = 0,ic=0; i < count;i++) {
		auto& v = voxels[i];
		if (!v.edge && edgeOnly)
			continue;		
		auto pos = (vec3(v.position)+baseOfs+vec3(0.5f))* objectSize;
		CubeData data;
		data.tm0= glm::translate(glm::mat4(1),vec3(pos));
		data.transform = data.tm0;
		SColorf c = SColor(v.color);
		data.color = glm::vec4(c.r,c.g,c.b,c.a) ;

		PMXRigidbody ci{};
		ci.m_shape = PMXRigidbody::Shape::Box;
		ci.m_shapeSize = vec3(objectSize / 2 - 0.02f*2.5f) ;
		ci.pOwner = (void*)(OWNERID_VOXEL +0);
		ci.m_translate = vec3(pos);// +vec3(0.5f);
		ci.m_rotate = vec3(0);
		ci.m_mass = objectMass;// *objectSize3;
		ci.m_repulsion = breakable ? 0.3f : 0.f;
		ci.m_friction = 1000000000;// breakable ? 1.f : 0.f;
		ci.m_translateDimmer = 1;
		ci.m_rotateDimmer = 1;
		ci.flag = PHRBF_ContactOffset;
		ci.m_group =   breakable ? 16 + (SABA_USE_PHYSX ? 1 : 0) : 15;
		ci.m_collideMask32 =   0xFFFFFFFF;
		ci.materialId = PHYSX_MATERIALID_VOXELCUBE;
		MMDRigidBody* rb = g_mmdPhysics->newRigidBody();
		data.rb = rb;
		data.meshLocalMat = glm::scale(mat4(1), vec3(objectSize));
		auto &vd=obids[voxLdr.getIndex(v.position)];
		vd.colorIdx = v.color;
		vd.instIdx = v.instIdx;
		vd.objIdx = Objs.size();
		data.rp = ci; data.v = v;
		data.isTop = v.isTop; if (data.isTop) {
			data.vtxMid = vms.alloc({});
		}
		rb->CreatePMX(ci, nullptr, nullptr);
		g_mmdPhysics->AddRigidBody(rb); 
		//body->SetActivation(1); body->ResetMovement();
				
		//assert(data.tm0 == glm::translate(glm::mat4(1), data.rp.m_translate));
		
		saba::PMXJoint jp{};
		if (1) {
			jp.setLocalPos = true;
			jp.springT = glm::vec3(breakable?1000:10000);
			jp.springR = glm::vec3(1000);
			jp.translate = pos - baseRbPos;
			jp.limitMinT = glm::vec3(-1);
			jp.limitMaxT = glm::vec3(1);
			jp.dampingT = vec3(100);
			jp.dampingR = vec3(100);
			data.jp = jp; 
			 data.jt = mmd->sb0->Pmx->connectRb(baseRb, rb, breakable, 0, jp);
			data.isEdge = v.edge;

			if (breakable && data.jt)  data.jt->setBreakThreshold(data.threshold= ci.m_mass * (v.edge? btShell : btInner));
			if (v.edge || (v.position.x % 2 && v.position.y % 2 && v.position.z % 2)) //mmd->sb0->Ctx->setSharedRbCb(body);

			data.rb->usrDat.callHitCb = true; data.rb->cbHit = [=](saba::PhysicsEngineObjectUserData* hit) {
				Objs[ic].resetCD = mmd->Ctx->gd.vspm.resetTime; //Objs[ic].color = SColor(255, 255, 0, 0);
				mmd->sb0->Ctx->cbHitMidi(hit);
				};
		}
		
		if (1) {
			linkRbs(data);
		}
		//data.rb->setLinearVel(vec3(0, 11.1f, 0));
 
		assert(data.tm0 == glm::translate(glm::mat4(1), data.rp.m_translate));
		Objs.push_back(data); ic++;
	}
	 
	updateInstanceBuffer();

	 resetScene(1);
}
void irr::scene::CVoxelMeshSceneNode::launchTopFw()
{

	int fwid =    Eqv->getFwIdxByFwIdStr(2, "poGrassFlw");
	for (int i = 0; i < Objs.size(); i++) {


		auto& o = Objs[i];

		if (o.isTop)
		{
			auto oc = o.color;
			vec4 color = oc;
			EQVisual::LfwParam lpm; lpm.mid = o.vtxMid;
			for (int i = 0; i < 1; i++)
				Eqv->LaunchFw3D({ 0,i * 0.1f,0 }, fwid, { 0,0,0 }, color, &lpm);

		}

	}
}
void irr::scene::CVoxelMeshSceneNode::linkRbs(irr::scene::CVoxelMeshSceneNode::CubeData& data)
{
	  if (!RESET_TO_THROW) return;
 

	auto jp = data.jp;
	auto rb = data.rb;
	const auto& v = data.v;
	float btShell = mmd->Ctx->gd.vspm.btShell  ;
	jp.springT = glm::vec3(100000 ); jp.springR = glm::vec3(1000); int lockT = 0;
	jp.dampingT = jp.springT/100.f;	jp.dampingR = jp.springR/100.f;
	float bts = 1.f;// objectMass* btShell;

	for (auto& j : data.jtvvs) delete j;
	data.jtvvs.clear();
	auto& V = obids[voxLdr.getIndex(v.position)];
	auto connectFun = [&](ivec3 ofs) {
		auto ofsPos = v.position + ofs;
		if (ofsPos.x < 0 || ofsPos.y < 0 || ofsPos.z < 0) return false;
		const auto& Vofs = obids[voxLdr.getIndex(ofsPos)];
		if (Vofs.colorIdx && Vofs.instIdx == V.instIdx) {
			jp.translate = glm::vec3(-ofs) * objectSize; 
			auto j = mmd->sb0->Pmx->connectRb(Objs[Vofs.objIdx].rb, rb, lockT, 0, jp);
			j->setBreakThreshold(bts);
			data.jtvvs.push_back(j);
			return true;
		}
		return false;
		};

	bool l = connectFun({ -1,0,0 });
	bool d = connectFun({ 0,-1,0 });
	bool b = connectFun({ 0,0,-1 });
	if (!l && !d) if (!connectFun({ -1,-1,0 }) && v.position.x < voxLdr.sizeX - 1) /*if (!connectFun({ +1,-1,0 }) && v.position.y < voxLdr.sizeY - 1) connectFun({ -1,+1,0 })*/;
	if (!l && !b) if (!connectFun({ -1,0,-1 }) && v.position.x < voxLdr.sizeX - 1) /*if (!connectFun({ +1,0,-1 }) && v.position.z < voxLdr.sizeZ - 1) connectFun({ -1,0,+1 })*/;
	if (!d && !b) if (!connectFun({ 0,-1,-1 }) && v.position.y < voxLdr.sizeY - 1) /*if (!connectFun({ 0,+1,-1 }) && v.position.z < voxLdr.sizeZ - 1) connectFun({ 0,-1,+1 })*/;
	if (!l && !d && !b) connectFun({ -1,-1,-1 });

	//if (v.position.x > 0) {
	//	const auto& VLeft = obids[voxLdr.getIndex(v.position + ivec3(-1, 0, 0))];
	//	if (v.l != -1 && VLeft.colorIdx && VLeft.instIdx == V.instIdx//&& (data.isEdge || Objs[obids[v.l]].isEdge)
	//		) {
	//		jp.translate = vec3(objectSize, 0, 0);
	//		data.jtx = mmd->sb0->Pmx->connectRb(Objs[VLeft.objIdx].rb, rb, lockT, 0, jp);
	//		// Objs[obids[v.l]].color.r = 1;
	//		data.jtx->setBreakThreshold(bts);
	//	}
	//}
	//if (v.position.y > 0) {
	//	const auto& VDown = obids[voxLdr.getIndex(v.position + ivec3(0, -1, 0))];
	//	if (v.d != -1 && VDown.colorIdx && VDown.instIdx == V.instIdx //&& (data.isEdge || Objs[obids[v.d]].isEdge)
	//		) {
	//		jp.translate = vec3(0, objectSize, 0);
	//		data.jty = mmd->sb0->Pmx->connectRb(Objs[VDown.objIdx].rb, rb, lockT, 0, jp);
	//		// Objs[obids[v.d]].color.g = 1;
	//		data.jty->setBreakThreshold(bts);
	//	}
	//}
	//if (v.position.z > 0) {
	//	const auto& VBack = obids[voxLdr.getIndex(v.position + ivec3(0, 0, -1))];
	//	if (v.b != -1 && VBack.colorIdx && VBack.instIdx == V.instIdx//&& (data.isEdge || Objs[obids[v.b]].isEdge)
	//		) {
	//		jp.translate = vec3(0, 0, objectSize);
	//		data.jtz = mmd->sb0->Pmx->connectRb(Objs[VBack.objIdx].rb, rb, lockT, 0, jp);
	//		// Objs[obids[v.b]].color.b = 1;
	//		data.jtz->setBreakThreshold(bts);
	//	}
	//}
 
}
void irr::scene::CVoxelMeshSceneNode::resetCube(irr::scene::CVoxelMeshSceneNode::CubeData& o, bool skipBaseJt, bool resetRestoreCD)
{


	o.rb->SetCoMTransform(o.tm0); o.rb->ResetMovement();
	if (o.jt) delete o.jt, o.jt = nullptr;
	linkRbs(o);
	if (skipBaseJt)
	return;

	saba::PMXJoint jp = o.jp;	 
	o.jt = mmd->sb0->Pmx->connectRb(mmd->sb0->Pom->groundRb, o.rb, breakable, 0, jp);
	
	float btShell = mmd->Ctx->gd.vspm.btShell;
	float btInner = mmd->Ctx->gd.vspm.btInner;
	o.threshold = o.rp.m_mass * (o.isEdge ? btShell : btInner);
	o.jt->setBreakThreshold(o.threshold);
	o.resetCD = 0; o.rb->setCollideFilterMask(0, 0);
	if (resetRestoreCD) o.restoreCD = 0;

 

}

void CVoxelMeshSceneNode::updateFrame()
{
	frame++;
	const auto* vsp = &mmd->Ctx->gd.vspm;
	if (vsp->fwFrame>0 && frame % vsp->fwFrame == 0)
		launchTopFw();
#define RESTORE_TIME 0.25f
    if (!hbInstances || Objs.empty()) return;

    InstanceData* pGpuData = static_cast<InstanceData*>(hbInstances->lock(false));
    if (!pGpuData) return;
 
	updateStage();

    std::vector<std::future<void>> results;
    size_t threadNum = 8;
    size_t rangSize = (Objs.size() + threadNum - 1) / threadNum;

    // Clear queue before processing
    size_t dummy;
    while(resetCubeQueue.try_dequeue(dummy)) {}

	bool autoReset = mmd->Ctx->gd.vspm.autoReset;
	bool speedDelay = mmd->Ctx->gd.vspm.speedDelay;
	float alpha = mmd->Ctx->gd.vspm.alpha;
	float cubeScale = objectSize * mmd->Ctx->gd.vspm.scale;
    for (size_t i = 0; i < threadNum; i++)
    {
        size_t start = i * rangSize, end = (i + 1) * rangSize;
        if (end >= Objs.size()) end = Objs.size();
        results.emplace_back(gpRenderThreadPool->enqueue(
            [=](size_t start, size_t end) {
                for (size_t i = start; i < end; i++) {
                    auto& o = Objs[i];
					float csc = cubeScale * (o.visible ? 1.f : 0.f);
                    auto m = o.rb->GetTransform();
					float a = alpha;
					if (autoReset) {
						if (o.resetCD <= 0 && glm::length2(vec3(m[3] - o.tm0[3])) > 0.01f ) {
							o.resetCD = mmd->Ctx->gd.vspm.resetTime;
						}
						else if (speedDelay && o.resetCD<0.25f && glm::length2(o.rb->getLinearVel()) > 1.f) {
							o.resetCD = 0.25f;
						}
						if (o.resetCD > 0.f) {
							o.resetCD -= gFrameTime;

							if (o.resetCD < 0.f) { 
								vec3 dt = vec3(o.tm0[3]) - vec3(o.transform[3]);
								vec3 vel = glm::fastNormalize(dt) * 2.f;
								float len2 = glm::length2(dt);

								if (!RESET_TO_THROW) if (len2 > 1.f)	o.stage = 1, o.stageCD = .5f+0.5f*ualib::UaRandF();
								else o.stage = 2, o.stageCD = .25f;
								//resetCubeQueue.enqueue(i); o.restoreCD = RESTORE_TIME;// Use atomic queue instead of vector
								o.resetCD = 0.f;
							}
						}
						// ...rest of the existing update code...
						if (o.restoreCD > 0.f)
						{
							o.restoreCD -= gFrameTime;
							if (o.restoreCD < 0.f) {
								o.restoreCD = 0.f;
							}
						}
						if (o.restoreCD > 0.f) o.meshLocalMat = glm::scale(mat4(1), vec3(csc * (RESTORE_TIME - o.restoreCD) / RESTORE_TIME));
						//else if (o.resetCD > 0.f && o.resetCD < 0.1f) {
						//	a *= o.resetCD / 0.1f;
						//	o.meshLocalMat = glm::scale(mat4(1), vec3(objectSize * ((1 - a)*0.6f  + 1.f)));
						//}
						else o.meshLocalMat = glm::scale(mat4(1), vec3(csc));
					}
 	 
						
                    o.transform = core::matrix4(m) * o.meshLocalMat;
 

                    instanceData[i].transform = glm::transpose(o.transform);
					if (o.isTop) {
						EQVisual::VtxMatStruct vm{}; core::matrix4 m = mmd->baseMatrix * o.transform;
						vm.m = m.getTransformTR();
						Eqv->GetPPT()->setVtxMat(o.vtxMid, vm);
					}
					instanceData[i].color = //autoReset ? o.color * vec4(1, 1, 1, (RESTORE_TIME - o.restoreCD) / RESTORE_TIME) : 
						o.color*vec4(1,1,1,a);
                    pGpuData[i] = instanceData[i];
                }
            }
        , start, end));
    }

    for (auto&& result : results)
    {
        result.get();
    }
	hbInstances->unlock();

    // Process queued resets
   // size_t cubeId;    while(resetCubeQueue.try_dequeue(cubeId))         resetCube(Objs[cubeId]);
	float objsize2 = objectSize * objectSize *2;
	for (int i = 0; i < Objs.size(); i++) {
		auto& o = Objs[i];

		switch (o.stage) {
		case 0:
			
			break;
		case 1: //launch
		{
			vec3 vel = glm::mat3(o.transform) * vec3(0, 3, 0);
			//if (o.visible) mmd->sb0->mmdFw(1,"flame1", o.transform[3], -vel*2.f, 0xFFFFFFFF);  
			o.rb->scaleVel(0.7f, 3);
			o.rb->addLinearVel(vel);
			o.rb->addRotationToMatOnNode_MatRttResetXZ(100.f);
			ifCd0IncStage(o, 7);	
			
		}break;
		case 2: {
			vec3 dt = vec3(o.tm0[3]) - o.rb->getPosition();
			vec3 vel = glm::fastNormalize(dt),vel0=vel;

		 
			float len2 = glm::length2(dt);
			float lenv = glm::length(o.rb->getLinearVel());
			if (len2 < objsize2 || lenv<0.9f) 	o.rb->setCollideFilterMask(1, 0x10000); //
			float stageMul = glm::clamp(o.stageTime, 0.1f, 1.f);
			lenv = glm::clamp(1.f/lenv,2.f, 50.f)* stageMul;
			vel = vel* lenv + vec3(0, 98.1f * gFrameTime, 0);
			//if (o.visible) mmd->sb0->mmdFw(1, "flame2", o.transform[3], - vel, 0xFFFFFFFF);
			o.rb->scaleVel(0.98f, 1); 
			o.rb->scaleVel(0.7f, 2);
			o.rb->addLinearVel(vel			);			
			auto qr = glh::rotationFromTo(vec3(0, 1, 0), vel0);
			if (len2> objsize2) o.rb->addRotationToMatOnNode(mat4(qr),60.f* stageMul);
			else o.rb->addRotationToMatOnNode(mat4(1), 60.f);
			if (len2 < 0.1f) o.stage++, o.stageCD = 1.f, o.stageTime = 0.f;
			else ifCd0IncStage(o,1.f);
		}break;
		case 3:
			resetCube(o);
			o.stage = 0;
			break;
		}
	}
}

void irr::scene::CVoxelMeshSceneNode::updateStage()
{
	if (stage == 1)
	{
		int cc = std::clamp((int)Objs.size() / 60 /5,(int)mmd->sabas.size(), 200);
		do {
			static int sbcc = 0;	
			
			if (showIdx >= Objs.size()) {
				stage = 0;
				showIdx = 0;
				break;
			}
			else {
				
				auto& o = Objs[showIdx];
				vec3 tgtPos=o.tm0[3];
				auto sb = mmd->getAnmGlbNearestSaba(tgtPos);
				//auto nd = sb->ndHeadTgt, ndf = sb->ndHead;
				int rnd = ualib::UaRand(2);
				auto nd = rnd ? sb->ndHandR : sb->ndHandL;auto ndfrb = nd->rb0->parentRb;
				o.visible = 1;
				o.rb->setCollideFilterMask(1, 0);
				vec3 pos = nd->getGlobalPos();
				o.stage = 2;  o.stageCD = 0.5f + 0.f * ualib::UaRandF(); o.stageTime = 1.f;
				o.rb->ResetMovement(); 
				//o.rb->setLinearVel(mat3(sb->ndHeadTgt->GetGlobalTransform()) * vec3(0, 0, -10));
				o.rb->SetCoMTransform(glm::translate(glm::mat4(1), pos));
				vec3 dirAdd = glm::fastNormalize(vec3(sb->ndYao->mGlobalAnim[3])*vec3(1,0,1))*0.f;
				ndfrb->addLinearVelToPos(vec3(o.tm0[3])+vec3(0,0.f,-3)+dirAdd, vec3(6));
			}

			showIdx += 1;
		} while (--cc>0);

	}
}

bool irr::scene::CVoxelMeshSceneNode::ifCd0IncStage(irr::scene::CVoxelMeshSceneNode::CubeData& o, float dur)
{
	if (o.stageCD > 0.f)
	{
		o.stageCD -= gFrameTime; o.stageTime += gFrameTime;
		if (o.stageCD < 0.f) {
			o.stageCD = 0.f;
			o.stage++; o.stageCD = dur; o.stageTime = 0.f;
			return true;
		}		
	}
	return false;
}

void irr::scene::CVoxelMeshSceneNode::resetScene(bool skipBaseJt)
{
	auto count = Objs.size(); 
	auto& voxels = voxLdr.voxels;
	auto baseRb = mmd->sb0->Pom->groundRb;
	auto baseRbPos = baseRb->getPosition();
	vec3 vel = vec3(0, 0, 0);
	PhyObjParam pmt{};
	for (int i = 0; i < count; i++) {
		auto& o = Objs[i];

		if (i == 0 && RESET_TO_THROW) {
			pmt.pmxrb = o.rp;
			pmt.size = vec3(5);
			pmt.poType = 1; pmt.visible = 0;
			pmt.mass = 1000;
			pmt.pos = SceneManager->getActiveCamera()->getAbsolutePosition()/MMD_SABA_SCALE + Ctx->gd.apm.camOfsCtr;
			actAnimPm(mmd->sb0, false, 0, pmt);
			//pmt.disableHitForTime = 0.6f;//
			pmt.pmxrb.m_collideMask32 = 0;
			auto po=mmd->Pom->addObj(pmt);
			auto rb = po->rb;
			rb->setAngularVel(vec3(0, 36, 0));
			baseRb = rb; baseRbPos = rb->getPosition();
		}
		auto& v = voxels[i];
		
#if !RESET_TO_THROW
		//o.jp.translate = pmt.pos  + o.rp.m_translate;		o.tm0 = glm::translate(glm::mat4(1),  o.jp.translate);
		resetCube(o, skipBaseJt,true);
#else
		
		vec3 pos = pmt.pos + vec3(0, -10, 0) * objectSize + o.rp.m_translate;
		o.rb->ResetMovement();
		o.rb->SetCoMTransform(glm::translate(glm::mat4(1), pos));
		o.rb->setLinearVel(pmt.vel);
		if (o.jt) delete o.jt, o.jt = nullptr;

		saba::PMXJoint jp = o.jp;
		o.jp.translate = pos-baseRbPos;
		o.jt = mmd->sb0->Pmx->connectRb(baseRb, o.rb, breakable, 0, jp);
		o.threshold = o.rp.m_mass * 10000.f;
		o.jt->setBreakThreshold(o.threshold);
#endif
		linkRbs(o);
#if SHOWING_STAGE
		o.visible =  0 ;
		o.rb->setCollideFilterMask(1, 0);
#endif
		
	} 
#if SHOWING_STAGE
	stage = 1; showIdx = 0;
#endif
}



void irr::scene::CVoxelMeshSceneNode::clear()
{
	auto count = Objs.size();
	auto& voxels = voxLdr.voxels; 
	auto Pmx = mmd->sb0->Pmx;
	auto phyMan = Pmx->GetPhysicsManager();
	for (int i = 0; i < count; i++) {
		auto& o = Objs[i]; 
		if (o.jt) delete o.jt;
		o.rb->removeFromWorld();

		if (o.vtxMid) {
			auto pd = Eqv->GetPPT()->vtxMats.dataPtr();
			pd[o.vtxMid].cvt = 1;
			Eqv->GetPPT()->vtxMats.freeItem(o.vtxMid);
		}
	}
	Objs.clear();
}

void CVoxelMeshSceneNode::createInitialObjects(const io::path meshPath, size_t count, float scale)
{
	meshFilename = meshPath;
	objectSize = scale;

	// Load mesh
	if (Mesh)
		Mesh->drop();

	IAnimatedMesh* animMesh = SceneManager->getMesh(meshPath);
	if (!animMesh) return;

	Mesh = animMesh->getMesh(0);
	Mesh->grab();

	IMeshBuffer* buffer = Mesh->getMeshBuffer(0);
	if (!buffer) return;

	// Create vertex and index buffers
	auto vt = buffer->getVertexType();
	if (hbVertices) hbVertices->drop();
	hbVertices = Driver->createHardwareBuffer(
		video::EHBT_VERTEX,
		video::EHBA_DEFAULT,
		buffer->getVertexCount() * sizeof(S3DVertex),
		0,
		buffer->getVertices()
	);

	if (hbIndices) hbIndices->drop();
	hbIndices = Driver->createHardwareBuffer(
		video::EHBT_INDEX,
		video::EHBA_DEFAULT,
		buffer->getIndexCount() * (buffer->getIndexType() == EIT_32BIT ? 4 : 2),
		0,
		buffer->getIndices()
	);

	// Set up material
	video::SMaterial& material = buffer->getMaterial();
 
	material.SpecularColor = 0x10808080;
	material.DiffuseColor = 0xFFFFFFFF;
	setPassType(IrrPassType_ShadowMap, true);
	material.RcvShadow = true;
	IsVisible = true;

	
}

void CVoxelMeshSceneNode::updateInstanceBuffer()
{
    if (!Driver) return;

    instanceCount = Objs.size();
    
    if (instanceCount == 0) {
        if (hbInstances) {
            hbInstances->drop();
            hbInstances = nullptr;
        }
        instanceBufferCapacity = 0;
        return;
    }

    if (instanceCount > instanceBufferCapacity) {
        // Use power of 2 sizing for better 
        size_t newCapacity = 1;
        while (newCapacity < instanceCount) {
            newCapacity <<= 1;
        }
        
        instanceData.resize(newCapacity);
        
        if (hbInstances) {
            hbInstances->drop();
            hbInstances = nullptr;
        }

        hbInstances = Driver->createHardwareBuffer(
            video::EHBT_VERTEX,
            video::EHBA_DEFAULT,
            newCapacity * sizeof(InstanceData),
            0,
            instanceData.data()
        );
        
        instanceBufferCapacity = newCapacity;
    }
}



void CVoxelMeshSceneNode::reserve(size_t count)
{
    if (count > Objs.size()) {
        Objs.reserve(count);
        instanceData.reserve(count);
        // Don't resize hardware buffer yet - wait until actually needed
    }
}
