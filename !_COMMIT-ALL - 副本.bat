@echo off
setlocal enabledelayedexpansion
set "MESSAGE=%~1"
if "!MESSAGE!"=="" (
    set "MESSAGE=AUTO"
)

echo.
echo ========================================
echo Committing to all repositories with message:
echo !MESSAGE!
echo ========================================
echo.

echo [1/4] Committing to VkUpApp...
cd VkUpApp
git add .
git commit -m "!MESSAGE!"
if %errorlevel% neq 0 (
    echo Warning: VkUpApp commit failed or no changes to commit
)
cd ..

echo.
echo [2/4] Committing to AppMainLib...
cd AppMainLib
git add .
git commit -m "!MESSAGE!"
if %errorlevel% neq 0 (
    echo Warning: AppMainLib commit failed or no changes to commit
)
cd ..

echo.
echo [3/4] Committing to CommonStaticLib...
cd CommonStaticLib
git add .
git commit -m "!MESSAGE!"
if %errorlevel% neq 0 (
    echo Warning: CommonStaticLib commit failed or no changes to commit
)
cd ..

echo.
echo [4/4] Committing to UaIrrlicht...
cd UaIrrlicht
git add .
git commit -m "!MESSAGE!"
if %errorlevel% neq 0 (
    echo Warning: UaIrrlicht commit failed or no changes to commit
)
cd ..

echo.
echo [Main] Updating main repository...
git add .
git commit -m "!MESSAGE!"
if %errorlevel% neq 0 (
    echo Warning: Main repository commit failed or no changes to commit
)

echo.
echo ========================================
echo All repositories committed locally!
echo Use 'push-all.bat' to push all changes to remote repositories.
echo ========================================
pause