#include "appGlobal.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkDriver.h"
#include "../../../UaIrrlicht/include/IFileSystem.h"
#include "VulkanRenderer/VkVertexDeclaration.h"
#include "VulkanRenderer/vulkanRenderPass.h"
#include "VulkanRenderer/VkShaderMan/VkFxBase.h"
#include "VulkanRenderer/VkShaderMan/VkFxDescriptorSetManager.h"
#include "VulkanRenderer/VkTexture.h"

// Compiled shader headers - using actual VkFluid shaders
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidShaderToy_vert_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidShaderToy_frag_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidDensity_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidCovariance_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluid_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidVelocity_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidDistance_comp_SpirV.h"


using namespace irr;
using namespace irr::video;
using namespace VkFxUtil;

// Fluid shader bytecode declarations - using actual VkFluid shaders
static const ShaderBytecode Sbc_VkFluid__VkFluidShaderToy_vert = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidShaderToy_vert);
static const ShaderBytecode Sbc_VkFluid__VkFluidShaderToy_frag = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidShaderToy_frag);
static const ShaderBytecode Sbc_VkFluid__VkFluidDensity_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidDensity_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidVelocity_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidVelocity_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidDistance_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidDistance_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidCovariance_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidCovariance_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluid_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluid_comp);

namespace irr
{
namespace video
{

E_MATERIAL_TYPE MrVkFluid::s_materialType = EMT_TRANSPARENT_ALPHA_CHANNEL;

// FIXED ISSUES:
// 1. Graphics pipeline creation was commented out - now enabled
// 2. Shader bytecode references - now using actual VkFluid compiled shaders
// 3. OnRender method didn't check for valid pipeline - now checks VK_NULL_HANDLE
// 4. Vertex input state setup to match full-screen quad rendering
// 5. Found and linked actual VkFluid shaders in VkFluid directory
// 6. Added missing compute descriptor binding 3 for storage image (density texture)
// 7. Fixed storage image descriptor validation by deferring binding until actual textures are set
// 8. Added texture validation checks in compute dispatch methods to prevent unbound descriptor errors

MrVkFluid::MrVkFluid(IVideoDriver* driver, io::IFileSystem* fileSystem)
    : VkMaterialRenderer(driver)
    , m_fileSystem(fileSystem)
    , m_descriptorSet(VK_NULL_HANDLE)
    , m_densityComputeDescriptorSet(VK_NULL_HANDLE)
    , m_covarianceComputeDescriptorSet(VK_NULL_HANDLE)
    , m_mainComputeDescriptorSet(VK_NULL_HANDLE)
    , m_descriptorSetLayout(VK_NULL_HANDLE)
    , m_computeDescriptorSetLayout(VK_NULL_HANDLE)
    , m_descriptorPool(VK_NULL_HANDLE)
    , m_defaultSampler(VK_NULL_HANDLE)
    , m_pipelineLayout(VK_NULL_HANDLE)
    , m_computePipelineLayout(VK_NULL_HANDLE)
    , m_graphicsPipeline(VK_NULL_HANDLE)
    , m_densityComputePipeline(VK_NULL_HANDLE)
    , m_covarianceComputePipeline(VK_NULL_HANDLE)
    , m_mainComputePipeline(VK_NULL_HANDLE)
    , m_densityComputeCommandBuffer(VK_NULL_HANDLE)
    , m_covarianceComputeCommandBuffer(VK_NULL_HANDLE)
    , m_mainComputeCommandBuffer(VK_NULL_HANDLE)
    , m_computeFence(VK_NULL_HANDLE)
    , m_time(0.0f)
    , m_deltaTime(0.0f)
    , m_needsUpdate(true)
    , m_renderPipeline(FRP_RAYMARCHING)
    , m_particleBuffer(nullptr)
    , m_covarianceBuffer(nullptr)
    , m_densityTexture(nullptr)
    , m_velocityTexture(nullptr)
    , m_distanceTexture(nullptr)
    , m_covarianceTexture(nullptr)
{
    if (m_fileSystem)
        m_fileSystem->grab();

    // Initialize uniform data
    memset(&m_uniforms, 0, sizeof(m_uniforms));
    memset(&m_computeParams, 0, sizeof(m_computeParams));
    memset(&m_shaderToyUniforms, 0, sizeof(m_shaderToyUniforms));

    // Set default volume parameters
    m_uniforms.mViewProjectionInverse.makeIdentity();
    m_uniforms.volumeMin = core::vector3df(-10.0f, -5.0f, -10.0f);
    m_uniforms.volumeMax = core::vector3df(10.0f, 15.0f, 10.0f);
    m_uniforms.volumeSize = m_uniforms.volumeMax - m_uniforms.volumeMin;
    m_uniforms.rayStepSize = 0.02f;
    m_uniforms.densityThreshold = 0.1f;
    m_uniforms.maxRaymarchSteps = 128;
    m_uniforms.noiseScale = 2.0f;
    m_uniforms.refractionIndex = 1.333f;
    m_uniforms.absorptionScale = 1.0f;
    m_uniforms.scatteringScale = 0.5f;
    m_uniforms.numParticles = 0;

    initializeShaders();
    createUniformBuffers();
    createDefaultSampler();
    createComputeCommandBuffers();
    setupDescriptorSets();
    recreatePipelines();
}

MrVkFluid::~MrVkFluid()
{
    // Clean up Vulkan resources
    VkDevice device = Driver->Device;

    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
    }

    if (m_densityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_densityComputePipeline, nullptr);
    }

    if (m_covarianceComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_covarianceComputePipeline, nullptr);
    }

    if (m_mainComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_mainComputePipeline, nullptr);
    }

    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
    }

    if (m_descriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_descriptorSetLayout, nullptr);
    }

    if (m_computeDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_computeDescriptorSetLayout, nullptr);
    }

    if (m_descriptorPool != VK_NULL_HANDLE) {
        vkDestroyDescriptorPool(device, m_descriptorPool, nullptr);
    }

    if (m_defaultSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_defaultSampler, nullptr);
    }

    if (m_computeFence != VK_NULL_HANDLE) {
        vkDestroyFence(device, m_computeFence, nullptr);
    }

    // Free compute command buffers
    if (m_densityComputeCommandBuffer != VK_NULL_HANDLE ||
        m_covarianceComputeCommandBuffer != VK_NULL_HANDLE ||
        m_mainComputeCommandBuffer != VK_NULL_HANDLE) {
        VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
        VkCommandBuffer cmdBuffers[] = {
            m_densityComputeCommandBuffer,
            m_covarianceComputeCommandBuffer,
            m_mainComputeCommandBuffer
        };
        vkFreeCommandBuffers(device, vkDriver->getComputeCommandPool(), 3, cmdBuffers);
    }

    if (m_fileSystem)
        m_fileSystem->drop();
}

bool MrVkFluid::OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId)
{
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    // Check if pipeline is valid before using it
    if (m_graphicsPipeline == VK_NULL_HANDLE) {
        return false; // Pipeline not created yet
    }

    // Bind descriptor sets and pipeline for volume raymarching
    VkCommandBuffer cmdBuffer = Driver->currentCmdBuffer();

    vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_graphicsPipeline);
    vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                           m_pipelineLayout, 0, 1, &m_descriptorSet, 0, nullptr);

    return true;
}

void MrVkFluid::OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                             bool resetAllRenderstates, IMaterialRendererServices* services)
{
    CurrentMaterial = material;
    services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);

    // Set render pipeline from material parameter
    m_renderPipeline = static_cast<FluidRenderPipeline>(static_cast<u32>(material.MaterialTypeParam));
    m_needsUpdate = true;
}

void MrVkFluid::OnUnsetMaterial()
{
    // Nothing specific to do here
}

bool MrVkFluid::setVariable(const c8* name, const f32* floats, int count)
{
    // Handle fluid-specific shader variables
    if (strcmp(name, "time") == 0 && count >= 1) {
        setTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "deltaTime") == 0 && count >= 1) {
        setDeltaTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "volumeMin") == 0 && count >= 3) {
        m_uniforms.volumeMin = core::vector3df(floats[0], floats[1], floats[2]);
        m_uniforms.volumeSize = m_uniforms.volumeMax - m_uniforms.volumeMin;
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "volumeMax") == 0 && count >= 3) {
        m_uniforms.volumeMax = core::vector3df(floats[0], floats[1], floats[2]);
        m_uniforms.volumeSize = m_uniforms.volumeMax - m_uniforms.volumeMin;
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "rayStepSize") == 0 && count >= 1) {
        m_uniforms.rayStepSize = floats[0];
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "densityThreshold") == 0 && count >= 1) {
        m_uniforms.densityThreshold = floats[0];
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "refractionIndex") == 0 && count >= 1) {
        m_uniforms.refractionIndex = floats[0];
        m_needsUpdate = true;
        return true;
    }

    return false;
}

const void* MrVkFluid::getShaderByteCode() const
{
    // Return vertex shader bytecode for pipeline validation
    return Sbc_VkFluid__VkFluidShaderToy_vert.code;
}

u32 MrVkFluid::getShaderByteCodeSize() const
{
    return Sbc_VkFluid__VkFluidShaderToy_vert.length;
}

void MrVkFluid::cleanFrameCache()
{
    // Clean up per-frame resources if any
}

void MrVkFluid::preSubmit()
{
    // Pre-submission setup if needed
}

void MrVkFluid::ReloadShaders()
{
    initializeShaders();
    recreatePipelines();
}

// Volume parameter setters
void MrVkFluid::setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds)
{
    m_uniforms.volumeMin = minBounds;
    m_uniforms.volumeMax = maxBounds;
    m_uniforms.volumeSize = maxBounds - minBounds;
    m_needsUpdate = true;
}

void MrVkFluid::setRaymarchParameters(u32 maxSteps, f32 stepSize, f32 densityThreshold)
{
    m_uniforms.maxRaymarchSteps = maxSteps;
    m_uniforms.rayStepSize = stepSize;
    m_uniforms.densityThreshold = densityThreshold;
    m_needsUpdate = true;
}

void MrVkFluid::setFluidColor(const SColor& color)
{
    // Store color information in uniforms if needed
    m_needsUpdate = true;
}

void MrVkFluid::setRefractionIndex(f32 index)
{
    m_uniforms.refractionIndex = index;
    m_needsUpdate = true;
}

void MrVkFluid::setAbsorptionColor(const core::vector3df& absorption)
{
    // Store absorption color if needed
    m_needsUpdate = true;
}

void MrVkFluid::setCameraPosition(const core::vector3df& position)
{
    m_uniforms.cameraPosition = position;
    m_needsUpdate = true;
}

// Matrix setters
void MrVkFluid::setWorldMatrix(const core::matrix4& world)
{
    // For volume rendering, world matrix is less important
    m_needsUpdate = true;
}

void MrVkFluid::setViewMatrix(const core::matrix4& view)
{
    // Update view-projection inverse matrix
    m_needsUpdate = true;
}

void MrVkFluid::setProjectionMatrix(const core::matrix4& projection)
{
    // Update view-projection inverse matrix
    m_needsUpdate = true;
}

// ShaderToy compatibility methods (following VkMr2D pattern)
void MrVkFluid::setResolution(const float3& resolution)
{
    m_shaderToyUniforms.iResolution = resolution;
    m_shaderToyUniforms.res = float2(resolution.x, resolution.y);
    m_shaderToyUniforms.resWdH = resolution.x / resolution.y;
    m_shaderToyUniforms.resHdW = resolution.y / resolution.x;
    m_needsUpdate = true;
}

void MrVkFluid::setFrame(s32 frame)
{
    m_shaderToyUniforms.iFrame = frame;
    m_needsUpdate = true;
}

void MrVkFluid::setMouse(const float4& mouse)
{
    m_shaderToyUniforms.iMouse = mouse;
    m_needsUpdate = true;
}

// Buffer management
void MrVkFluid::setParticleBuffer(VkHardwareBuffer* buffer)
{
    m_particleBuffer = buffer;

    // Update descriptor sets for all compute pipelines that use particle data
    if (buffer && m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_densityComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1,  // binding 1 for particle buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }

    if (buffer && m_covarianceComputeDescriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_covarianceComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1,  // binding 1 for particle buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }

    if (buffer && m_descriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1,  // binding 1 for particle buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }
}

void MrVkFluid::setCovarianceBuffer(VkHardwareBuffer* buffer)
{
    m_covarianceBuffer = buffer;

    // Update descriptor sets
    if (buffer && m_covarianceComputeDescriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_covarianceComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            2,  // binding 2 for covariance buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }

    if (buffer && m_descriptorSet != VK_NULL_HANDLE) {
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            2,  // binding 2 for covariance buffer
            &buffer->Descriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
    }
}

// Volume texture setters (these would update descriptor sets when textures change)
void MrVkFluid::setDensityFieldTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_densityTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }

    m_densityTexture = texture;

    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);

        // Update compute descriptor set binding 3 for storage image usage
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                3,  // binding 3 for density texture
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated density texture compute descriptor set\n"));
        }

        // Update graphics descriptor set binding 3 for sampler3D usage in fragment shader
        if (m_descriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo samplerDescriptor{};
            samplerDescriptor.imageView = vkTexture->getShaderResourceView();
            samplerDescriptor.sampler = m_defaultSampler; // Use sampler for combined image sampler
            samplerDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL; // Use GENERAL layout for both storage and sampling

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_descriptorSet,
                VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
                3,  // binding 3 for density texture in fragment shader
                &samplerDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated density texture graphics descriptor set for fragment shader\n"));
        }
    }
}

void MrVkFluid::setVelocityFieldTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_velocityTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }

    m_velocityTexture = texture;

    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);

        // Update compute descriptor set binding 4 for storage image usage
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                4,  // binding 4 for velocity texture
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated velocity texture compute descriptor set\n"));
        }

        // Update graphics descriptor set binding 4 for sampler3D usage in fragment shader
        if (m_descriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo samplerDescriptor{};
            samplerDescriptor.imageView = vkTexture->getShaderResourceView();
            samplerDescriptor.sampler = m_defaultSampler; // Use sampler for combined image sampler
            samplerDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL; // Use GENERAL layout for both storage and sampling

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_descriptorSet,
                VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
                4,  // binding 4 for velocity texture in fragment shader
                &samplerDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated velocity texture graphics descriptor set for fragment shader\n"));
        }
    }
}

void MrVkFluid::setDistanceFieldTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_distanceTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }

    m_distanceTexture = texture;

    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);

        // Update compute descriptor set binding 5 for storage image usage
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                5,  // binding 5 for distance texture
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated distance texture compute descriptor set\n"));
        }

        // Update graphics descriptor set binding 5 for sampler3D usage in fragment shader
        if (m_descriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo samplerDescriptor{};
            samplerDescriptor.imageView = vkTexture->getShaderResourceView();
            samplerDescriptor.sampler = m_defaultSampler; // Use sampler for combined image sampler
            samplerDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL; // Use GENERAL layout for both storage and sampling

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_descriptorSet,
                VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
                5,  // binding 5 for distance texture in fragment shader
                &samplerDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated distance texture graphics descriptor set for fragment shader\n"));
        }
    }
}

void MrVkFluid::setCovarianceFieldTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_covarianceTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }

    m_covarianceTexture = texture;

    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);

        // Update compute descriptor set binding 6 for storage image usage
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                6,  // binding 6 for covariance texture
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated covariance texture compute descriptor set\n"));
        }

        // Update graphics descriptor set binding 6 for sampler3D usage in fragment shader
        if (m_descriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo samplerDescriptor{};
            samplerDescriptor.imageView = vkTexture->getShaderResourceView();
            samplerDescriptor.sampler = m_defaultSampler; // Use sampler for combined image sampler
            samplerDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL; // Use GENERAL layout for both storage and sampling

            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_descriptorSet,
                VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
                6,  // binding 6 for covariance texture in fragment shader
                &samplerDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);

            DP(("DEBUG: Updated covariance texture graphics descriptor set for fragment shader\n"));
        }
    }
}

void MrVkFluid::updateVolumeUniforms(const VkFluidVolumeUniforms& uniforms)
{
    m_uniforms = uniforms;
    m_needsUpdate = true;
}

// Private implementation methods
void MrVkFluid::initializeShaders()
{
    try {
        // Load and create vertex/fragment shader for volume raymarching
        m_vertexFragmentShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);

        // Use actual VkFluid compiled shaders
        m_vertexFragmentShader->createVS(Sbc_VkFluid__VkFluidShaderToy_vert, "main");
        m_vertexFragmentShader->createPS(Sbc_VkFluid__VkFluidShaderToy_frag, "main");

        // Load compute shaders - now available
        m_densityComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_densityComputeShader->createCS(Sbc_VkFluid__VkFluidDensity_comp, "main");

        // Load velocity compute shader (if compiled)
        try {
            m_velocityComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
            m_velocityComputeShader->createCS(Sbc_VkFluid__VkFluidVelocity_comp, "main");
        } catch (...) {
            DP(("WARNING: VkFluidVelocity compute shader not available - skipping\n"));
            m_velocityComputeShader = nullptr;
        }

        // Load distance compute shader (if compiled)
        try {
            m_distanceComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
            m_distanceComputeShader->createCS(Sbc_VkFluid__VkFluidDistance_comp, "main");
        } catch (...) {
            DP(("WARNING: VkFluidDistance compute shader not available - skipping\n"));
            m_distanceComputeShader = nullptr;
        }

        m_covarianceComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_covarianceComputeShader->createCS(Sbc_VkFluid__VkFluidCovariance_comp, "main");

        m_mainComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_mainComputeShader->createCS(Sbc_VkFluid__VkFluid_comp, "main");
    } catch (...) {
        // If shader loading fails, set vertex/fragment shader to null
        m_vertexFragmentShader = nullptr;
    }
}

void MrVkFluid::createUniformBuffers()
{
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create uniform buffer for graphics pipeline (ShaderToy uniforms)
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_uniformBuffer,
        sizeof(CbVkFluid)));

    // Create uniform buffer for compute pipelines (legacy volume uniforms)
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_computeUniformBuffer,
        sizeof(VkFluidVolumeUniforms)));

    // Create dummy storage buffer for descriptor set validation
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
        VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT,
        &m_dummyStorageBuffer,
        64));

    // Map buffers persistently
    VK_CHECK_RESULT(m_uniformBuffer.map());
    VK_CHECK_RESULT(m_computeUniformBuffer.map());
}

void MrVkFluid::createDefaultSampler()
{
    VkDevice device = Driver->Device;

    VkSamplerCreateInfo samplerInfo{};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
    samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.mipLodBias = 0.0f;
    samplerInfo.anisotropyEnable = VK_FALSE;
    samplerInfo.maxAnisotropy = 1.0f;
    samplerInfo.compareEnable = VK_FALSE;
    samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
    samplerInfo.minLod = 0.0f;
    samplerInfo.maxLod = 1.0f;
    samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
    samplerInfo.unnormalizedCoordinates = VK_FALSE;

    VK_CHECK_RESULT(vkCreateSampler(device, &samplerInfo, nullptr, &m_defaultSampler));
}

void MrVkFluid::createComputeCommandBuffers()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create command buffers for compute operations (5 total)
    VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(
        vkDriver->getComputeCommandPool(),
        VK_COMMAND_BUFFER_LEVEL_PRIMARY,
        5);

    VkCommandBuffer cmdBuffers[5];
    VK_CHECK_RESULT(vkAllocateCommandBuffers(device, &cmdBufAllocateInfo, cmdBuffers));

    m_densityComputeCommandBuffer = cmdBuffers[0];
    m_velocityComputeCommandBuffer = cmdBuffers[1];
    m_distanceComputeCommandBuffer = cmdBuffers[2];
    m_covarianceComputeCommandBuffer = cmdBuffers[3];
    m_mainComputeCommandBuffer = cmdBuffers[4];

    // Create fence for compute synchronization
    VkFenceCreateInfo fenceCreateInfo = vks::initializers::fenceCreateInfo(VK_FENCE_CREATE_SIGNALED_BIT);
    VK_CHECK_RESULT(vkCreateFence(device, &fenceCreateInfo, nullptr, &m_computeFence));
}

void MrVkFluid::updateUniformBuffers()
{
    // Sync legacy uniforms to flat ShaderToy uniform structure
    m_shaderToyUniforms.fluidParams = float4(
        m_uniforms.refractionIndex,        // x = refraction index
        m_uniforms.densityThreshold,       // y = density threshold
        m_uniforms.rayStepSize,            // z = ray step size
        (f32)m_uniforms.maxRaymarchSteps   // w = max steps
    );

    // Convert volume bounds
    m_shaderToyUniforms.volumeMin = float4(
        m_uniforms.volumeMin.X, m_uniforms.volumeMin.Y, m_uniforms.volumeMin.Z, 0.0f);
    m_shaderToyUniforms.volumeMax = float4(
        m_uniforms.volumeMax.X, m_uniforms.volumeMax.Y, m_uniforms.volumeMax.Z, 0.0f);
    m_shaderToyUniforms.cameraPos = float4(
        m_uniforms.cameraPosition.X, m_uniforms.cameraPosition.Y, m_uniforms.cameraPosition.Z, 0.0f);

    // Copy view-projection inverse matrix (direct assignment works with flat structure)
    m_shaderToyUniforms.mViewProjectionInverse = m_uniforms.mViewProjectionInverse;

    // Update ShaderToy uniform buffer for graphics pipeline
    if (m_uniformBuffer.mapped) {
        // Copy flat ShaderToy compatible uniforms to graphics pipeline
        memcpy(m_uniformBuffer.mapped, &m_shaderToyUniforms, sizeof(CbVkFluid));
    }

    // Update compute uniform buffer (legacy volume uniforms)
    m_computeParams.time = m_time;
    m_computeParams.numParticles = m_uniforms.numParticles;

    if (m_computeUniformBuffer.mapped) {
        memcpy(m_computeUniformBuffer.mapped, &m_computeParams, sizeof(VkFluidVolumeUniforms));
    }
}

void MrVkFluid::setupDescriptorSets()
{
    VkDevice device = Driver->Device;

    // Graphics descriptor set layout (7 bindings as identified in the validation error)
    std::vector<VkDescriptorSetLayoutBinding> graphicsBindings = {
        // Binding 0: Uniform buffer
        { 0, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 1: Particle buffer
        { 1, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 2: Covariance buffer
        { 2, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 3: Environment texture
        { 3, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 4: Scene color texture
        { 4, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 5: Scene depth texture
        { 5, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 6: Density field texture
        { 6, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr }
    };

    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = static_cast<uint32_t>(graphicsBindings.size());
    layoutInfo.pBindings = graphicsBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &layoutInfo, nullptr, &m_descriptorSetLayout));

    // Compute descriptor set layout
    std::vector<VkDescriptorSetLayoutBinding> computeBindings = {
        // Binding 0: Compute uniform buffer
        { 0, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 1: Particle buffer
        { 1, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 2: Output buffer (covariance or density texture)
        { 2, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 3: Density texture (storage image for compute shader)
        { 3, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 4: Velocity texture (storage image for compute shader)
        { 4, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 5: Distance texture (storage image for compute shader)
        { 5, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 6: Covariance texture (storage image for compute shader)
        { 6, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr }
    };

    VkDescriptorSetLayoutCreateInfo computeLayoutInfo{};
    computeLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    computeLayoutInfo.bindingCount = static_cast<uint32_t>(computeBindings.size());
    computeLayoutInfo.pBindings = computeBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &computeLayoutInfo, nullptr, &m_computeDescriptorSetLayout));

    // Create descriptor pool
    std::vector<VkDescriptorPoolSize> poolSizes = {
        { VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 4 },           // Graphics + 3 compute uniform buffers
        { VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 8 },           // Particle + covariance buffers for multiple sets
        { VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 16 },  // Graphics texture samplers + extras
        { VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 16 }            // Compute storage images (density, velocity, distance, covariance textures)
    };

    VkDescriptorPoolCreateInfo poolInfo = vks::initializers::descriptorPoolCreateInfo(
        static_cast<uint32_t>(poolSizes.size()),
        poolSizes.data(),
        6  // maxSets: graphics + 5 compute descriptor sets
    );

    VK_CHECK_RESULT(vkCreateDescriptorPool(device, &poolInfo, nullptr, &m_descriptorPool));

    // Allocate descriptor sets
    VkDescriptorSetLayout layouts[] = {
        m_descriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout
    };

    VkDescriptorSetAllocateInfo allocInfo = vks::initializers::descriptorSetAllocateInfo(
        m_descriptorPool,
        layouts,
        6
    );

    VkDescriptorSet descriptorSets[6];
    VK_CHECK_RESULT(vkAllocateDescriptorSets(device, &allocInfo, descriptorSets));

    m_descriptorSet = descriptorSets[0];
    m_densityComputeDescriptorSet = descriptorSets[1];
    m_velocityComputeDescriptorSet = descriptorSets[2];
    m_distanceComputeDescriptorSet = descriptorSets[3];
    m_covarianceComputeDescriptorSet = descriptorSets[4];
    m_mainComputeDescriptorSet = descriptorSets[5];

    // Initialize descriptor sets with dummy data
    // This will be properly updated when buffers and textures are set
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Default texture descriptor
    VkDescriptorImageInfo defaultTextureDescriptor{};
    defaultTextureDescriptor.imageView = VKTEX(vkDriver->NullTexture)->getShaderResourceView();
    defaultTextureDescriptor.sampler = m_defaultSampler;
    defaultTextureDescriptor.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;

    // Update graphics descriptor set
    std::vector<VkWriteDescriptorSet> writeDescriptorSets = {
        // Binding 0: Uniform buffer
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 0, &m_uniformBuffer.descriptor),
        // Binding 1: Particle buffer (dummy for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, &m_dummyStorageBuffer.descriptor),
        // Binding 2: Covariance buffer (dummy for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 2, &m_dummyStorageBuffer.descriptor),
        // Bindings 3-6: Texture samplers (default textures for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 3, &defaultTextureDescriptor),
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 4, &defaultTextureDescriptor),
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 5, &defaultTextureDescriptor),
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 6, &defaultTextureDescriptor)
    };
    vkUpdateDescriptorSets(device, static_cast<uint32_t>(writeDescriptorSets.size()), writeDescriptorSets.data(), 0, nullptr);

    // Create a dummy storage image for compute shaders (since NullTexture doesn't have STORAGE_BIT)
    // For now, we'll skip binding 3 in the initial setup and only bind it when we have actual storage images
    VkDescriptorImageInfo defaultStorageImageDescriptor{};
    defaultStorageImageDescriptor.imageView = VK_NULL_HANDLE; // Will be set when actual storage images are available
    defaultStorageImageDescriptor.sampler = VK_NULL_HANDLE;
    defaultStorageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

    // Update compute descriptor sets with dummy data (skip storage image binding for now)
    for (VkDescriptorSet computeSet : { m_densityComputeDescriptorSet, m_velocityComputeDescriptorSet, m_distanceComputeDescriptorSet, m_covarianceComputeDescriptorSet, m_mainComputeDescriptorSet }) {
        std::vector<VkWriteDescriptorSet> computeWriteDescriptorSets = {
            vks::initializers::writeDescriptorSet(computeSet, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 0, &m_computeUniformBuffer.descriptor),
            vks::initializers::writeDescriptorSet(computeSet, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, &m_dummyStorageBuffer.descriptor),
            vks::initializers::writeDescriptorSet(computeSet, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 2, &m_dummyStorageBuffer.descriptor)
            // Note: Binding 3-6 (storage images) will be updated when actual textures are set
        };
        vkUpdateDescriptorSets(device, static_cast<uint32_t>(computeWriteDescriptorSets.size()),
                              computeWriteDescriptorSets.data(), 0, nullptr);
    }
}

void MrVkFluid::recreatePipelines()
{
    VkDevice device = Driver->Device;

    // Clean up existing pipelines
    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
        m_graphicsPipeline = VK_NULL_HANDLE;
    }

    if (m_densityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_densityComputePipeline, nullptr);
        m_densityComputePipeline = VK_NULL_HANDLE;
    }

    if (m_velocityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_velocityComputePipeline, nullptr);
        m_velocityComputePipeline = VK_NULL_HANDLE;
    }

    if (m_distanceComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_distanceComputePipeline, nullptr);
        m_distanceComputePipeline = VK_NULL_HANDLE;
    }

    if (m_covarianceComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_covarianceComputePipeline, nullptr);
        m_covarianceComputePipeline = VK_NULL_HANDLE;
    }

    if (m_mainComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_mainComputePipeline, nullptr);
        m_mainComputePipeline = VK_NULL_HANDLE;
    }

    // Clean up pipeline layouts
    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
        m_pipelineLayout = VK_NULL_HANDLE;
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
        m_computePipelineLayout = VK_NULL_HANDLE;
    }

    // Recreate everything
    createPipelineLayouts();
    createGraphicsPipelines();
    createComputePipelines();
}

void MrVkFluid::createPipelineLayouts()
{
    VkDevice device = Driver->Device;

    // Create graphics pipeline layout
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &m_descriptorSetLayout;
    pipelineLayoutInfo.pushConstantRangeCount = 0;
    pipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &pipelineLayoutInfo, nullptr, &m_pipelineLayout));

    // Create compute pipeline layout
    VkPipelineLayoutCreateInfo computePipelineLayoutInfo{};
    computePipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    computePipelineLayoutInfo.setLayoutCount = 1;
    computePipelineLayoutInfo.pSetLayouts = &m_computeDescriptorSetLayout;
    computePipelineLayoutInfo.pushConstantRangeCount = 0;
    computePipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &computePipelineLayoutInfo, nullptr, &m_computePipelineLayout));
}

void MrVkFluid::createGraphicsPipelines()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Set up pipeline state using base class helper
    SetPipelineCommonInfo();

    // Create graphics pipeline for volume raymarching
    VkGraphicsPipelineCreateInfo pipelineInfo = vks::initializers::pipelineCreateInfo(
        m_pipelineLayout,
        vkDriver->curRenderPass(),
        0);

    // Set up vertex input state to match the full-screen quad vertices from SnVkFluid
    auto declNode = vkDriver->declarationMap.find(E_VERTEX_TYPE::EVT_STANDARD);
    if (declNode && m_vertexFragmentShader) {
        pipelineInfo.pVertexInputState = declNode->getValue()->getInputLayout(this,
            (u64)m_vertexFragmentShader->CompiledCodeWithSignature());
    } else {
        // Fallback to empty vertex input state
        VkPipelineVertexInputStateCreateInfo emptyVertexInputState = vks::initializers::pipelineVertexInputStateCreateInfo();
        pipelineInfo.pVertexInputState = &emptyVertexInputState;
    }

    // Set up blending for transparency
    VkPipelineColorBlendAttachmentState blendAttachmentState =
        vks::initializers::pipelineColorBlendAttachmentState(0xf, VK_TRUE);
    blendAttachmentState.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
    blendAttachmentState.dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
    blendAttachmentState.colorBlendOp = VK_BLEND_OP_ADD;
    blendAttachmentState.srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
    blendAttachmentState.dstAlphaBlendFactor = VK_BLEND_FACTOR_ZERO;
    blendAttachmentState.alphaBlendOp = VK_BLEND_OP_ADD;

    VkPipelineColorBlendStateCreateInfo colorBlendState =
        vks::initializers::pipelineColorBlendStateCreateInfo(1, &blendAttachmentState);

    // Apply common pipeline state
    pipelineInfo.pInputAssemblyState = &inputAssemblyState;
    pipelineInfo.pRasterizationState = &rasterizationState;
    pipelineInfo.pColorBlendState = &colorBlendState;
    pipelineInfo.pMultisampleState = &multisampleState;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pDepthStencilState = &depthStencilState;
    pipelineInfo.pDynamicState = &dynamicState;

    // Apply shaders (when available)
    if (m_vertexFragmentShader) {
        m_vertexFragmentShader->applyShaders(&pipelineInfo);

        // Create graphics pipeline now that shaders are available
        VK_CHECK_RESULT(vkCreateGraphicsPipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_graphicsPipeline));
    }
}

void MrVkFluid::createComputePipelines()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create compute pipelines now that actual shaders are available
    if (m_densityComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_densityComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_densityComputePipeline));
    }

    if (m_velocityComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_velocityComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_velocityComputePipeline));
    }

    if (m_distanceComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_distanceComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_distanceComputePipeline));
    }

    if (m_covarianceComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_covarianceComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_covarianceComputePipeline));
    }

    if (m_mainComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_mainComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_mainComputePipeline));
    }
}

// Compute shader dispatching methods
void MrVkFluid::dispatchDensityComputation3D(u32 resX, u32 resY, u32 resZ)
{
    if (m_densityComputePipeline == VK_NULL_HANDLE) return;

    // Check if density texture is set (required for binding 3)
    if (!m_densityTexture) {
        // Cannot dispatch without a valid density texture for storage image binding
        DP(("DEBUG: Skipping density computation - no density texture set\n"));
        return;
    }

    VkDevice device = Driver->Device;

    // Wait and reset fence
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    vkResetFences(device, 1, &m_computeFence);

    // Record and submit compute commands
    VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
    vkBeginCommandBuffer(m_densityComputeCommandBuffer, &cmdBufInfo);

    vkCmdBindPipeline(m_densityComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_densityComputePipeline);
    vkCmdBindDescriptorSets(m_densityComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_densityComputeDescriptorSet, 0, nullptr);

    uint32_t groupCountX = (resX + 7) / 8;
    uint32_t groupCountY = (resY + 7) / 8;
    uint32_t groupCountZ = (resZ + 7) / 8;
    vkCmdDispatch(m_densityComputeCommandBuffer, groupCountX, groupCountY, groupCountZ);

    // Add memory barrier to ensure compute writes complete before graphics reads
    VkMemoryBarrier memoryBarrier = vks::initializers::memoryBarrier();
    memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
    memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
    vkCmdPipelineBarrier(
        m_densityComputeCommandBuffer,
        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
        VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr
    );

    vkEndCommandBuffer(m_densityComputeCommandBuffer);

    VkSubmitInfo submitInfo = vks::initializers::submitInfo();
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_densityComputeCommandBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
}

void MrVkFluid::dispatchVelocityComputation3D(u32 resX, u32 resY, u32 resZ)
{
    if (m_velocityComputePipeline == VK_NULL_HANDLE) return;

    // Check if velocity texture is set (required for binding 4)
    if (!m_velocityTexture) {
        // Cannot dispatch without a valid velocity texture for storage image binding
        DP(("DEBUG: Skipping velocity computation - no velocity texture set\n"));
        return;
    }

    // Begin command buffer recording
    VkCommandBufferBeginInfo beginInfo = vks::initializers::commandBufferBeginInfo();
    vkBeginCommandBuffer(m_velocityComputeCommandBuffer, &beginInfo);

    // Bind compute pipeline
    vkCmdBindPipeline(m_velocityComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_velocityComputePipeline);

    // Bind descriptor set
    vkCmdBindDescriptorSets(m_velocityComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_velocityComputeDescriptorSet, 0, nullptr);

    uint32_t groupCountX = (resX + 7) / 8;
    uint32_t groupCountY = (resY + 7) / 8;
    uint32_t groupCountZ = (resZ + 7) / 8;
    vkCmdDispatch(m_velocityComputeCommandBuffer, groupCountX, groupCountY, groupCountZ);

    // Add memory barrier to ensure compute writes complete before graphics reads
    VkMemoryBarrier memoryBarrier = vks::initializers::memoryBarrier();
    memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
    memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
    vkCmdPipelineBarrier(
        m_velocityComputeCommandBuffer,
        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
        VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr
    );

    vkEndCommandBuffer(m_velocityComputeCommandBuffer);

    VkSubmitInfo submitInfo = vks::initializers::submitInfo();
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_velocityComputeCommandBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
}

void MrVkFluid::dispatchDistanceComputation3D(u32 resX, u32 resY, u32 resZ)
{
    if (m_distanceComputePipeline == VK_NULL_HANDLE) return;

    // Check if distance texture is set (required for binding 5)
    if (!m_distanceTexture) {
        // Cannot dispatch without a valid distance texture for storage image binding
        DP(("DEBUG: Skipping distance computation - no distance texture set\n"));
        return;
    }

    // Begin command buffer recording
    VkCommandBufferBeginInfo beginInfo = vks::initializers::commandBufferBeginInfo();
    vkBeginCommandBuffer(m_distanceComputeCommandBuffer, &beginInfo);

    // Bind compute pipeline
    vkCmdBindPipeline(m_distanceComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_distanceComputePipeline);

    // Bind descriptor set
    vkCmdBindDescriptorSets(m_distanceComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_distanceComputeDescriptorSet, 0, nullptr);

    uint32_t groupCountX = (resX + 7) / 8;
    uint32_t groupCountY = (resY + 7) / 8;
    uint32_t groupCountZ = (resZ + 7) / 8;
    vkCmdDispatch(m_distanceComputeCommandBuffer, groupCountX, groupCountY, groupCountZ);

    // Add memory barrier to ensure compute writes complete before graphics reads
    VkMemoryBarrier memoryBarrier = vks::initializers::memoryBarrier();
    memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
    memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
    vkCmdPipelineBarrier(
        m_distanceComputeCommandBuffer,
        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
        VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr
    );

    vkEndCommandBuffer(m_distanceComputeCommandBuffer);

    VkSubmitInfo submitInfo = vks::initializers::submitInfo();
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_distanceComputeCommandBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
}

void MrVkFluid::dispatchCovarianceComputation3D(u32 numParticles)
{
    if (m_covarianceComputePipeline == VK_NULL_HANDLE) return;

    // Check if required resources are available
    if (!m_covarianceTexture) {
        return; // Cannot dispatch without covariance texture
    }
    // TODO: Implement when compute pipeline is available
}

void MrVkFluid::waitForComputeCompletion()
{
    if (m_computeFence != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
        vkResetFences(device, 1, &m_computeFence);
    }
}

void MrVkFluid::ensureComputeToGraphicsSync()
{
    // Wait for all compute operations to complete before graphics rendering
    waitForComputeCompletion();

    // Additional synchronization could be added here if needed
    // For example, explicit queue synchronization between compute and graphics queues
}

} // namespace video
} // namespace irr





