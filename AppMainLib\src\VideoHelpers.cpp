#include "AppGlobal.h"
#include "VideoHelpers.h"
#include "cppIncDefine.h"
#include "stlUtils.h"
#include "FFHelper/UaFfmpeg.h"
#include "FFHelper/UaFfmpegFile.h"
#include <filesystem>
#include "VulkanRenderer/VkDriver.h"
#include "VulkanRenderer/VkMr2d.h"

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

#include <glm/gtx/euler_angles.hpp>
#include <irrfw/eqv/EQV.h>
#ifdef _WIN32
#include <shellapi.h>
#include "winUtils.h"
#endif
using namespace ualib;
using namespace irr;
using namespace irr::video;
#define COLMAP_NAME "cs1"//trSakura"//treeRoad"//cs2"//fpvtree"//vnb"//fp2"//tr" //"fp3"
#define COLMAP_FPS 10
#define COLMAP_CHANGE_MP4_FPS_PTS 0
#define COLMAP_DIR "E:/tmp/"
#define COLMAP_PATH  COLMAP_DIR COLMAP_NAME
#define REVERSE_OUTPUT 0

#define USE_MULTI_VIDEO		0

#if USE_MULTI_VIDEO
#define MV_FILENAME "arout_220528_153728_1920x1080_000"//arout_220525_153920_1920x1080_000"//arout_220525_152951_1920x1080_000"//arout_220525_150944_1920x1080_000"
//arout_220520_164058_1920x1080_024"
#define AUTO_SELECT_FILE	0
#else
static std::vector<InputMediaInfo> videolists = {
#if !DRONE_AR
	//{"D:/medias/ar (1).mp4",0.f },{"D:/medias/ar (1).mp4",0.f },
	//{"D:/medias/car.mp4",6.f}  ,	{"D:/medias/car.mp4",0.f,5.f}  ,	{"D:/medias/car3.mp4",0.f,2.7f},  
	 
	//{"D:/medias/th8.mp4",5.f,33.f}   ,{"D:/medias/th2.mp4",2.f}   , {"D:/medias/th5.mp4",0.f}  ,{"D:/medias/th6.mp4",0.f} , {"D:/medias/th9.mp4",0.f} , {"D:/medias/th10.mp4",0.f} , {"D:/medias/th7.mp4",3.f}   
	//"D:/medias/ar (3).mp4"  , "D:/medias/ar (2).mp4", "D:/medias/ar (5).mp4", "D:/medias/ar (4).mp4", "D:/medias/ar (1).mp4" , "D:/medias/ar (6).mp4" };
  //{"d:/tmp/ar/my3b.mp4",0.f,12.f},{"d:/tmp/ar/my0.mp4",30.f,99.f} , {"d:/tmp/ar/my3.mp4",53.f,72.f}, 
	
  //{"d:/tmp/ar/y1.mp4",0.f,5.f} ,	 {"d:/tmp/ar/y1.mp4",21.f,60.f} ,	 {"d:/tmp/ar/y1.mp4",72.f,99.f} ,
	 //{"d:/tmp/ar/yhj.mp4",0.f,99.f} ,
	 //{"d:/tmp/ar/yh1.mp4",52.f,64.3f} , {"d:/tmp/ar/yh2.mp4",23.f,39.f} ,	 {"d:/tmp/ar/yh3.mp4",39.f,60.f} ,//	
  //{"d:/tmp/ar/yh1.mp4",50.f,99} ,  {"d:/tmp/ar/yh2.mp4",23.f,39.f} ,	 {"d:/tmp/ar/yh3.mp4",39.f,60.f} ,

 //{"d:/tmp/ar/trr.mp4",0.f,999.f} , {"d:/tmp/ar/th (3).mp4",0.f,29.f} ,	{"d:/tmp/ar/th (2).mp4",0.f,15.f} ,		{"d:/tmp/ar/th (3).mp4",0.f,27.f}  ,	{"d:/tmp/ar/th (2).mp4",0.f,15.f} ,		{"d:/tmp/ar/th (3).mp4",0.f,27.f} ,																							  //{"d:/tmp/ar/th (3).mp4",0.f,25.f} , {"d:/tmp/ar/th (3).mp4",0.f,25.f} ,{"d:/tmp/ar/th (2).mp4",0.f,17.f} ,
  //{"D:/medias/bgd01.mp4",2.f,10.f}, {"D:/medias/bgd01.mp4",11.f,66.f},{"D:/medias/bgd02.mp4",1.5f,12.f},{"D:/medias/bgd03.mp4",2.5f,16.f},

	//AROUT
	//{"d:/tmp/ar/aroutColMap.mp4",0.f,999.f} ,

//	{"r:/outputCam.mp4",0.f,999.f} ,
	{"e:/tmp/!OUTPUT/t1.mkv",0.f,999.f}
	//{"d:/tmp/ar/aroutRoad.mp4",0.f,999.f},//,{"d:/tmp/ar/arout.mp4",0.f,999.f},// {"d:/tmp/ar/t0.mp4",0.f,999.f}, {"d:/tmp/ar/t1.mp4",0.f,999.f}, {"d:/tmp/ar/ar.mp4",0.f,999.f}, {"d:/tmp/ar/ar.mp4",0.f,999.f},
	
	//{"d:/tmp/ar/mv/16.mp4",0.f,999.f}, {"d:/tmp/ar/mv/21.mp4",0.f,999.f},{"d:/tmp/ar/mv/22.mp4",0.f,999.f}	,{"d:/tmp/ar/mv/31.mp4",0.f,999.f},{"d:/tmp/ar/mv/32.mp4",0.f,999.f},{"d:/tmp/ar/mv/33.mp4",0.f,999.f},{"d:/tmp/ar/mv/34.mp4",0.f,999.f}
 //{"M:/AR/Lyyao0.mp4" ,0.0f,12.f}//,{"M:/AR/Lyyao2.mp4" ,0.0f,10.f},
 // {"M:/AR/LyMoon.mp4" ,0.0f,999.f} //,
	//	{"M:/AR/LyTak.mp4" ,0.f,999.f}
	
#else
	//DJI
	#define AIR_FILE "sjf2"//DJI_0937"//DJI_0901"//510"//DJI_0895"//DJI_yllihu2"//DJI_ylyihu"//DJI_lysjt"//DJI_0879"
	{"M:/ARD/" AIR_FILE  ".mp4" ,0.f,999.f} //{"M:/ARD/DJI_lysjt.mp4",0.f,999.f}//{"M:/ARD/DJI_ylyihu.mp4",0.f,999.f}//,
	//{"M:/ARD/DJI_0890.mp4" ,40.f,60.f}
	//{"R:/test.mp4" ,0.f,999.f}
											//{"d:/tmp/dji/airCam.mp4",0.f,999.f}//{"d:/tmp/dji/dji.mp4",0.f,999.f}
	//{"M:/ARD/lhYaoL.mp4" ,0.f,999.f}
#endif
};
#if DRONE_AR
const char* arDatPath = VidPathPre "dji/d/" AIR_FILE ".json";//DJI_lysjt.json"//DJI_ylyihu.json"//
#endif
#endif
void VideoProcessor::openOutDir(int run)
{
#ifdef _WIN32
	//app->SendMsgToUI(102010, vp.mis.curMakeId, 0);
	auto ef = ovpm.file;
	std::wstring fp = ualib::Utf8toWcs(ualib::strReplaceAll(ef, "/", "\\"));
	WCHAR sz[1024];
	swprintf_s(sz, 1024, L"Exported File: %s\nOpen the folder of exported file and locate the file?", fp.c_str());
	std::wstring filestr(L"/select, \"");

	if (fp.length() > 0)
	{
		filestr += fp + L"\"";
		std::wstring fpDir = fp.substr(0, fp.find_last_of(L'\\') + 1);
		if (!shellFindExplorerAndSelectFile(fpDir.c_str(), fp.c_str()))
		{
			if (!shellOpenExplorerAndSelectFile(fpDir.c_str(), fp.c_str()))
				ShellExecuteW(0, 0, L"explorer.exe", filestr.c_str(), 0, SW_SHOW);
		}
		if (run) {
			ShellExecuteW(0, 0, fp.c_str(), 0, 0, SW_SHOW);
		}

	}
#endif
}
void VideoProcessor::stage(int s) {
	DP(("VP stage set %d", s));
	if (_stage == 0 && s == 1) { //start
		renderId = -1; recTimeS = -1;
		
		if (curVFrame.tex) {
			UP_LOCK_GUARD(vfLock);
			vFramesToFree.push_back(curVFrame);
		}
		curVFrame.tex = nullptr;
		startVFrame.tex = nullptr; startVFrame.vfIdx = 0; startVFrame.timeS = 0.f;
		endVFrame.tex = nullptr; endVFrame.vfIdx = 0; endVFrame.timeS = 0.f;
		curFrameIdx = startFrameIdx = endFrameIdx = 0;
		switching = false; 
		paused = startPause;		startPause = 0;
		if (onStart) onStart();
		
	}
	_stage = s;
}

void VideoProcessor::reset()
{
	mediaTime = renderTime = 0;
	stage(0);
	clearVFrames();
	clearScrFrames();
}

void VideoProcessor::clearVFrames() {
	UP_LOCK_GUARD(vfLock);
	while (vFrames.size() > 0) {
		vFramesToFree.push_back(vFrames.front());
		vFrames.pop_front();
	}
	vFrames.clear();
}

void VideoProcessor::clearScrFrames() {
	UP_LOCK_GUARD(ssLock);
	for (auto tex : scrShots) {
		scrShotsToFree.push_back(tex);
	}
	scrShots.clear();
}

void VideoProcessor::doToFreeVFrames() {
	if (vFramesToFree.size() > 0)
	{
		UP_LOCK_GUARD(vfLock);
		for (auto tex : vFramesToFree) {
			tex.tex->getDriver()->removeTexture(tex.tex);
		}
		vFramesToFree.clear();
	}
}

void VideoProcessor::doToFreeScrFrames()
{
	if (scrShotsToFree.size() > 0)
	{
		UP_LOCK_GUARD(ssLock);
		for (auto scr : scrShotsToFree) {
			if (scr.tex) scr.tex->getDriver()->removeTexture(scr.tex);
			if (scr.img) scr.img->drop();
		}
		scrShotsToFree.clear();
	}
}

void VideoProcessor::startRecordDynImg(const VideoParam& pm)
{
#ifdef _WIN32
	auto fn = getTempStrByTime("OutImg", "", false);

	outImgFolderFile = ualib::WcstoUtf8(Ctx->getPictureFilePath("Images/").c_str());
	std::filesystem::create_directories(std::filesystem::path(outImgFolderFile));
	outImgFolderFile += fn;
	mDynImgEnc.texWaterMark = nullptr;
	mDynImgEnc.startImage(mis.formatDynamic, outImgFolderFile, true, mis.rewind);
#else
	mDynImgEnc.startImage(mis.formatDynamic, Ctx->getTempFilePath("temp/outTempImg").c_strA(), false, mis.rewind);
#endif
	Ctx->setNeedCaptureScreen(true, false);
	dynimgCountDown = 0;
	mEncImgOutCount = 0;
#
	Ctx->mCbScreenCaptrue = [this](IImage* img) {
		//assert(img);

		if (dynimgCountDown == 0)
		{
			dynimgCountDown = -1;
			recordCmdTime = Ctx->gd.time;
			return false;
		}

		bool processed = false;
		if (mEncImgCount % (mis.dpSkipFrame + 1) == mis.dpSkipFrame)
		{			
			auto texMidRt = Ctx->texMidRT[Ctx->mainViewId];
			if (!img) {
				assert(texMidRt);
				img = Driver->createImageTextureCopy(texMidRt);
			}
			u32 bgColor = (mis.bgColor & 0x00FFFFFF) | (mis.bgTransparent ? 0x00000000 : 0xFF000000);
			auto tgtSize = img->getDimension();
			//if (mis.formatDynamic!=12)
			tgtSize.scale(mis.dpPercent / 100.0);
			//CPU_COUNT_BEGIN("XXX");
			ImageData imd;
			imd.w = tgtSize.Width;
			imd.h = tgtSize.Height;
			imd.pb = nullptr;
			imd.img = img;
			
			auto cam = Ctx->getSceneManager()->getActiveCamera(); 
			imd.fovY = cam->getFOV();

			cam->getViewMatrix().getInverse(imd.mat); imd.mat.setTranslation(imd.mat.getTranslation() - ballShotPos);
			mDynImgEnc.pMis = &mis;
			mDynImgEnc.onProgress = [=](int i, int c) {
#if PC_TF_VER
				//SendMsgToUI(102022, c, vp.mis.dpFrameNum); SendMsgToUI(102021, i, vp.mis.dpFrameNum);
#else
				//SendMsgToUI(102009, mis.curMakeId, i * 10000 / mis.dpFrameNum);
#endif
			};
			mDynImgEnc.driver = DrvOfs;
			mDynImgEnc.bgColor = bgColor;
			mDynImgEnc.pDrvLock = &DrvOfs->dsd.driverLock;;
			//DP(("cret %d %p", vp.mEncImgCount, imd.img));

			if (writeFrameToFile) {
				if (depthRT) Driver->saveTexture(depthRT, ualib::strFmt("r:/imgdp/%05d.png", mDynImgEnc.inCount).c_str());
				if (texPD)	 Driver->saveTexture(texPD, ualib::strFmt("r:/imgmk/%05d.png", mDynImgEnc.inCount).c_str());
			}
			mDynImgEnc.addImgData(imd);

			mEncImgOutCount++;
			//SendMsgToUI(102009, mis.curMakeId, mEncImgOutCount * 10000 / mis.dpFrameNum);
			processed = true;
		}

		mEncImgCount++;
		DP(("Encc %d", mEncImgCount));
		if (mEncImgOutCount >= mis.dpFrameNum
			) {
			stopVideo();
			DP(("Rec Finish"));
			//SendMsgToUI(mis.easyMode ? 102011 : 102010, mis.curMakeId, 0);
		}
		return processed;
	};
	//assert(Ctx->getUseFixFrameTime());
	auto& fw = *Eqv->getCurPtrFw();
	if (mis.dpAutoDur)
		if (fw.durationMs)		mis.dpFrameNum = int(fw.durationMs / 1000.0 / (Ctx->getFixFrameTime() * (mis.dpSkipFrame + 1.0)));
		else 			mis.dpFrameNum = 60;
}

void VideoProcessor::startRecordScreen(const VideoParam& pm)
{
	ovpm = pm;
	if (useFFEncoder)	ImgEnc = std::make_shared<FFImgVideoEncoder>(Ctx);
	else					ImgEnc = std::make_shared<MFImgVideoEncoder>(Ctx);
	ImgEnc->InitEncoder();
	ImgEnc->EncBegin(pm);
#if 0
	Eqv->recreateOnUpdate();
#endif
	Ctx->setNeedCaptureScreen(true, false);
	Ctx->mCbScreenCaptrue = [this](IImage* img) 
	{	 int r = 0;
	if (paused && !(recMode == 9 && REC_PAUSE_RM9))
		return false;
		//assert(mis.vdFPS == 30);
		if ( (SVG_MMD_WRITE) || 1 ||
			mEncImgCount % (mis.dpSkipFrame + 1) == mis.dpSkipFrame)
		{
	#if 0
			ITexture* texOut = Driver->addRenderTargetTexture(img->getDimension(), "rgb<NoSwapRB><STO>");
			Driver->setRenderTarget(texOut, true, false, 0);
			DP(("1"));
			ITexture* texIn = mDriver->addTexture("tex", img);
			Driver->draw2DImage(texIn, texIn->getRectI(), texIn->getRectI());
			DP(("2"));
			Driver->setRenderTarget(nullptr, false, false, 0);
			img = Driver->createImage(texOut, vector2di(0, 0), texOut->getSize());
			DP(("3"));
			Driver->freeTexture(texIn);
			Driver->freeTexture(texOut);
			//r = ImgEnc->AddImageFrame(img, vp.mEncImgCount * Ctx->getFixFrameTime() * 1000000, 10, false);
			img->drop();
	#else
			bool useMidRt = HDR_USE_MIDRT ? Driver->dsd.initHDR || Ctx->getViewState(1)==0 : 0;
			auto texMidRt = Ctx->gd.libCaptureScreen?nullptr:Ctx->texMidRT[Ctx->mainViewId];
			if (useMidRt) texMidRt = Ctx->texMidRT[Ctx->mainViewId];// VKDRV_CAST(Driver)->getSysRT();
			auto tex = useMidRt ? texMidRt: img ? nullptr : texMidRt;
			if (texMidRt && img && texMidRt->getSize() == img->getDimension())
				img=0,tex = texMidRt;
			r = ImgEnc->AddImageFrame(img,tex , int64_t(mEncImgCount * (1.0 / APP_FPS) * 1000000), 10,false, false);
			 
	#endif
		}
		if (r < 0)
		{
			DP(("Encoder ERROR %d", r));
		}
	#ifdef DBG_ON_PC
		mis.vdDurationS = 600;
	#endif
		mEncImgCount++;
		if (r < 0 || mEncImgCount >= mis.vdDurationS * mis.vdFPS) {
			//stopVideo();
			//SendMsgToUI(MSG_RecordVideoFinished, 0, 0);
		}
		return false;
	};
}

void VideoProcessor::stopVideo()
{
	working = 0;	
	Ctx->setNeedCaptureScreen(false, false);
	switch (recMode) {
	case 2:
		mDynImgEnc.finishEnc(); 
		break;
	case 9:  //rec screen
		ImgEnc->EncEnd();
		break;
	case 0: //dec only
	case 10: //convert
		//stage(2);
		//if (mFF) mFF->stopDecode();
	break;
	}

	//moved from up to check
	stage(2);
	if (mFF) mFF->stopDecode();

	clearScrFrames();
	clearVFrames();
	doToFreeScrFrames();
	doToFreeVFrames();
	recMode = 0;
}

bool VideoProcessor::curVFrameNeedToRender(float frameTimeS) {
	return vFrames.size() == 0 && mFF->getMedia(0) &&
		recTimeS - frameTimeS < curVFrame.timeS + mFF->getMeidaFrameTime(0);
}

void VideoProcessor::frameUpdateSubtitle()
{

}

void VideoProcessor::copyScrShotToClipboard(uint32_t w , uint32_t h , int blurR, uint32_t flag)
{
	IImage* img0 = Driver->createScreenShot();
	if (w == 0 || h == 0) {
		w = Driver->getCurrentRenderTargetSize().Width; h = Driver->getCurrentRenderTargetSize().Height;
	}
	IImage* img = Driver->createImage(ECF_A8R8G8B8, irr::core::dimension2du(w,h));
	img0->copyToScalingBoxFilter(img);
	if (flag & 0x100) {
		u32 *pc=(u32*)img->lock();
		u32 len = w * h;
		u32 max = 0;
		u32* p = pc;
		for (int i = 0; i < len; i++) {
			if ((*p & 0xff) > max) max = (*p & 0xff);
			p++;
		};
		p = pc;
		for (int i = 0; i < len; i++) {
			u32 c= (*p & 0xff) * 255 / max;
			(*p)= SColor(c,c,c,255).color;
			p++;
		};
		img->unlock();
	}
	if (blurR) {
		PITex tex = Driver->addTexture("tex", img);
		PITex rt = Driver->addRenderTargetTexture(tex->getSize());
		Driver->setRenderTarget(rt, 1, 1, 0);

		video::SMaterial mr;
		mr.MaterialType = VKDRV_CAST(Driver)->getMT(Fx2DIdEnum::Fx2D_Blur);
		mr.setTexture(0, tex);
		irr::video::CbMr2D* cb = VKDRV_CAST(Driver)->getCB(Fx2DIdEnum::Fx2D_Blur);
		cb->blur.blurSize = blurR;
		cb->blur.blurStep = 1;
		cb->blur.blurWeightMul = 1;
		cb->blur.Directions = 16;
		cb->blur.Quality = 8;
		cb->blur.sigma = blurR / 2.f; cb->blur.sigma2 = cb->blur.sigma * cb->blur.sigma;
		Driver->draw2DImageMr(mr, rt->getRectI(), tex->getRectI());
		Driver->setRenderTarget(nullptr, 0, 0, 0);

		img->drop();
		img = Driver->createImageTextureCopy(rt);
		Driver->removeTexture(tex); Driver->removeTexture(rt);
	}
	copyBitmapToClipboard((char*)img->lock(), img->getDimension().Width, img->getDimension().Height);
	img->unlock();		img->drop();			img0->drop();
}


void VideoProcessor::startEncodeThread(const VideoParam &pm)
{
	recording = true;
#if 1
	auto thread1 = std::thread([=]() {
		DP(("ENC THREAD START "));
		if (useFFEncoder)	ImgEnCvt = std::make_shared<FFImgVideoEncoder>(Ctx);
		else					ImgEnCvt = std::make_shared<MFImgVideoEncoder>(Ctx);
		ImgEnCvt->InitEncoder();
		ImgEnCvt->EncBegin(ovpm);
		auto tgtSize = core::dimension2du(ovpm.w, ovpm.h);

		{
			UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
			texOut = DrvOfs->addRenderTargetTexture(tgtSize, "rgb<NoSwapRB><STO>", encodeHDR ? video::ECF_A16B16G16R16UN : video::ECF_A8R8G8B8);
		}
		recTimeS = 0.0;
		DP(("ENC THREAD START CYCLE"));
		while (stage() != 0 && !gameStage->isExiting())
		{
			//DP(("SCRSHOTS =========================== %8d : %d",scrShots.size(), mFF->frameCacheCount()));

			//CPU_COUNT_B(GET);
			//if (toSkip >= 0) throw;//to check toSkip--;
			if (vFrames.size() < 10 && !paused)
				if (mediaListId >= 0 && hasVideo && !mFF->getOneFrame(recTimeS + 0.001f))
				{  //dec finished					
					if (vFrames.size() == 0) {
						if (cbSetAnimating && mediaListId<mediaListCount-1) cbSetAnimating(false);
#ifdef _WIN32
#if 0
						if (recTimeS /*+ frameTimeS*0.0f-0.01f*/ > curVFrame.timeS + mFF->getMedia(0)->getFrameTime()) {
#else
						if (recTimeS +0.001f/*+ frameTimeS*0.0f-0.01f*/ > curVFrame.timeS  ) {
#endif
#else
						if (recTimeS + gameStage->getFrameTimeS()+0.001f  > curVFrame.timeS /*+ mFF->getMedia(0)->getFrameTime()*/) {
#endif
#if INPUT_VIDEO_LIST

							if (mediaListId < mediaListCount - 1) {

								switching = true;
								recTimeS = 9999999;
								clearVFrames();
								//clearScrFrames();

								DP(("vp1 scr %d", scrShots.size()));
								SEvent evt; evt.EventType = EET_CMD_INPUT_EVENT;
								evt.CmdInput.cmdId = 1291010;
								Ctx->getLib()->libSendEvent(evt, false);
								SleepMs(100);

								while (vFrames.size() < 1) {
									SleepMs(10);
									mFF->getOneFrame(0);
								}
								clearScrFrames();

								recTimeS = 0.0;
								if (cbSetAnimating) cbSetAnimating(true);
								DP(("vp2 scr %d", scrShots.size()));
								DP(("CONTINUE VIDEO %d / %d", mediaListId + 1 / mediaListCount));
							}
							else {
								mediaListId = -1;
#if INPUT_VIDEO_LIST_FINISH_STOP // 自动停止录制
								cbStopRecord();
								break;
#endif
							}

#else
							cbStopRecord();
							break;
#endif
						}
					}
				}

			//CPU_COUNT_E(GET);


			VideoProcessor::VPFrameTexture vpf{};
			irr::video::ITexture* tex{};

			if (scrShots.size() == 0)
			{
				if (stage() == 2)
					break;
				UaYield();
				SleepMs(10);
				continue;
			}
			else
			{
				UP_LOCK_GUARD(ssLock);
				vpf = scrShots.front();
				scrShots.pop_front();

				tex = vpf.tex;
			}

			//DP(("REC Enc SCR IMAGE %d / %d!  scr %lld / vf %lld  recTimeS=%f  rdS=%f", vpf.vfIdx, Ctx->gd.mediaSrcFrameId, scrShots.size(), vFrames.size(), recTimeS, renderTime));
			if (composeFrame)	
			{	
				int r = 0;
					
				cbDrawBg(texOut,tex);

				//CPU_COUNT_BEGIN("ENC");
				bool useGPU = false;// fileID % 2;

				r = ImgEnCvt->AddImageFrame(nullptr, texOut,
					int64_t(recTimeS * 1000000LL), 11, isExtDev, // mEncImgCount * Ctx->getFixFrameTime() * 1000000,
					useGPU);
				//CPU_COUNT_END("ENC");
				if (r < 0) { DP(("Encoder ERROR %d", r)); }
				mEncImgCount++;

				if (r < 0) {
					cbStopRecord();
					//SendMsgToUI(MSG_RecordVideoFinished, 0, 0);
				}

			}
			else
			{	//DRAW SCR ONLY
#if 0
				// YUV
				IImage* imgOut{};
				{
					UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
					auto tex = VKTEX(texOut);
					if (!mVFP) mVFP = new VideoFrameProcessor(Ctx);
					ITexture *texYUV = mVFP->gpuGenYUV(tex);
					imgOut = DrvOfs->createImage(texYUV, core::vector2di(0, 0), texYUV->getSize());
					DrvOfs->freeTexture(texYUV);
				}
				int r = ImgEnCvt->AddImageFrame(imgOut, nullptr,
					int64_t(recTimeS * 1000000LL), 11, isExtDev, // mEncImgCount * Ctx->getFixFrameTime() * 1000000,
					false);

				if (r < 0) { DP(("Encoder ERROR %d", r)); }
				mEncImgCount++;
				if (imgOut) imgOut->drop();
				if (r < 0) { DP(("Encoder ERROR %d", r)); }

				if (r < 0) {
					cbStopRecord();
					//SendMsgToUI(MSG_RecordVideoFinished, 0, 0);
				}
#else
				int r = ImgEnCvt->AddImageFrame(vpf.img, vpf.tex,

					int64_t(recTimeS * 1000000LL), 11, isExtDev, // mEncImgCount * Ctx->getFixFrameTime() * 1000000,
					false);

				if (r < 0) { DP(("Encoder ERROR %d", r)); }
				mEncImgCount++;
				
#endif
			}
			recTimeS += gameStage->getFrameTimeS();
			{
				UP_LOCK_GUARD(ssLock);
				scrShotsToFree.push_back(vpf);
				//UP_LOCK_GUARD(*mLib->getLock());
				//DP(("FREE+"));;
				//Driver->freeTexture(tex);
				//DP(("FREE-"));
			}

			// is stepTime in Update

		}
		DP(("ENC END !!!!!!!!!!!"));
		ImgEnCvt->EncEnd();
		recording = false;
		clearScrFrames();
		if (texOut)
		{
#if USE_VIDEO_TEXTURE
			//CMrCsParticle* mr = (CMrCsParticle*)driver->getMaterialRenderer(CMrCsParticle::getMaterialType());
			//mr->setTexture(driver->NullTexture);
#else
			texOut->getDriver()->freeTexture(texOut);
			DP(("texOut FREE"));
#endif
		}
		});
	thread1.detach();
#endif
}

void VideoProcessor::startRecordCvt(const VideoParam& pm)
{

	ovpm = pm;

	//Ctx->mCbCapImage = false;
	{
		//UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
		Ctx->setNeedCaptureScreen(true, false);
	}
	clearScrFrames();

	clearVFrames();

	startEncodeThread(ovpm);

	Ctx->mCbScreenCaptrue = [this, pm](video::IImage* )
	{

		if (renderId < 0)
			return false;
		if (paused)
			return false;
		if (mEncImgCount % (mis.vdSkipFrame + 1) != 0)
		{
			mEncImgCount++;
			return false;
		}
		//DP(("RECVID 10 Frame %d   %d ", mEncImgCount, ImgEnCvt->getSampleCount() ));
		if (stage() != 1) {
			//StopRecord();
			return false;
			//SendMsgToUI(MSG_RecordVideoFinished, 0, 0);
		}

		{
#if USE_VIDEO_TEXTURE
			Driver->setRenderTarget(texOut, true, true, 0xFF808080);
			Driver->flush();
			CMrCsParticle* mr = (CMrCsParticle*)Driver->getMaterialRenderer(CMrCsParticle::getMaterialType());
			mr->setTexture(texOut);
			eqvLaunchVideoFw();
#endif
		}
		auto sss = scrShots.size();

		if (sss >= 10)
		{
			SleepMs(50);
			if (sss > (IS_WIN?99:16))
				SleepMs(950);
		}
		else if (sss >= 5)
			SleepMs(10);

		{
			ITexture* texIn{}, * texIn1{};
			video::IImage* img{};
			bool needCopy = false;
			if (encodeHDR && !Driver->dsd.hdrOn)
			{
				UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
				texIn = Driver->getSysRT();
				texIn1 = DrvOfs->addRenderTargetTexture(texIn->getSize(), "rttemphdr", ECF_A16B16G16R16UN);
				needCopy = true;
			}
			else
			{
				texIn = texSphere0Ptr ? *texSphere0Ptr : nullptr;

				{
					//UP_LOCK_GUARD(DrvOfs->dsd.driverLock); //DP(("texIn1 created"));
					if (encodeHDR)
						texIn1 = Driver->addRenderTargetTexture(Driver->getScreenSize(), "rttemphdr", ECF_A16B16G16R16UN);
					else
						texIn1 = Driver->addTexture(Driver->getScreenSize(), "scrbuf", Driver->getColorFormat());
				}
				if (texIn) needCopy = true;
				else
				{
					if (encodeHDR) {
						VKDRV_CAST(Driver)->copyLastFrame(nullptr);
						texIn = VKDRV_CAST(Driver)->texFrameCopy;
						needCopy = true;
					}
					else
						VKDRV_CAST(Driver)->copyLastFrame(texIn1);
				}



			}
			if (needCopy || encodeHDR)
			{
				UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
				texIn->copyTo(texIn1);
			}
			if (writeFrameToFile && writeFrameToFileEveryNFrames && renderId % writeFrameToFileEveryNFrames == 0) {
				ualib::SleepMs(16);
				Driver->saveTexture(texIn1, ualib::strFmt("r:/img01/%05d.png", renderId / writeFrameToFileEveryNFrames).c_str());
			}

			if (!composeFrame)
			{
				img = Driver->createImageTextureCopy(texIn1);
				Driver->removeTexture(texIn1);
				texIn1 = nullptr;
			}
			UP_LOCK_GUARD(ssLock);
			VideoProcessor::VPFrameTexture vpf{};
			vpf.vfIdx = Ctx->gd.mediaSrcFrameIdDrawn;
			vpf.tex = texIn1;
			vpf.img = img;
			vpf.timeS = mediaTime;
			scrShots.push_back(vpf);

			//DP(("renderR %d", renderId));
			renderId++;

			//DP(("REC Save SCR IMAGE %d / %d! %d", Ctx->gd.mediaSrcFrameIdDrawn, Ctx->gd.mediaSrcFrameId, scrShots.size()));
		}


		return false;
	};
}

 
//=====================================================
MediaListManager::MediaListManager()
{
	isMultiVideo = USE_MULTI_VIDEO;
	resetFileList();
}

void MediaListManager::resetFileList()
{
	using namespace std::filesystem;
	medias.clear();
#if USE_MULTI_VIDEO
	int fc = 0;
	bool autoSel = AUTO_SELECT_FILE;
	std::string str, fn;
	if (autoSel)
	{
		file_time_type maxTime{};
		const std::filesystem::path mvdir{ "d:/tmp/ar/ardatcam/mv" };
		for (auto const& dir_entry : std::filesystem::directory_iterator{ mvdir })
		{
			std::string n = dir_entry.path().filename().string();
			size_t sz = n.find("_000.mp4");
			if (sz != std::string::npos && dir_entry.last_write_time()>maxTime)
			{
				fn = n.substr(0, sz);
			}
		}
	}
	else
	{
		fn = MV_FILENAME; 
		fn = fn.substr(0, fn.size() - 4);

	}
	do {
		if (autoSel)
			str = strFmt("d:/tmp/ar/ardatcam/mv/%s_%03d.mp4", fn.c_str(), fc++);
		else
			str = strFmt("d:/tmp/ar/mv/%s_%03d.mp4",fn.c_str(), fc++);

		if (fileExists(str)) {
			InputMediaInfo mi;
			mi.filePath = str;

			medias.push_back(mi);
		}
		else break;
	} while (true);

#else
	for (int i = 0; i < videolists.size(); i++)
	{

		medias.push_back(videolists[i]);
	}
#endif
}



void VideoProcessor::testColmap(irr::scene::ISceneNode *root)
{
#ifdef _WIN32


	auto sm = Ctx->getSceneManager();
	if (!cmRoot) {
		cmRoot = sm->addEmptySceneNode();
		//cmRoot->setRotation({-150, 0, 0 });
	}


	float W=1920, H = 1080, F = 2828.9279893119615;
	{
		char camType[256]; int id;
		std::ifstream fstr(COLMAP_PATH "/txt/cameras.txt");   // 打开文件，建立数据流
		std::string lineStr;
		std::getline(fstr, lineStr); std::getline(fstr, lineStr); std::getline(fstr, lineStr); 
		std::getline(fstr, lineStr);
		sscanf(lineStr.c_str(), "%d %s %f %f %f \n",&id, camType,  &W, &H, &F);
	}
	float fovy = 2 * atan(H / F / 2);

	std::ifstream fstr(COLMAP_PATH "/txt/images.txt");   // 打开文件，建立数据流
	std::string lineStr;
	std::getline(fstr, lineStr); std::getline(fstr, lineStr); std::getline(fstr, lineStr); std::getline(fstr, lineStr);
	int id = 0;
	if (snPly) snPly->setParent(sm->getRootSceneNode());
	cmRoot->removeAll();
	if (snPly) cmRoot->addChild(snPly);
	auto ms = sm->getMesh("res/txtBox.obj");
	//if (!camCM) camCM = SceneManager->addCameraSceneNode();

	
#if 0
	if (!snPly)
	{
		core::matrix4 mtr = //glm::rotate(glm::mat4(1), -core::PI / 2, glm::vec3(1, 0, 0))
			glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 1, 0))
			//
			*
			glm::scale(glm::mat4(1), glm::vec3(CMPosScale, -CMPosScale, CMPosScale))
			;
		auto msPly = sm->getMesh("D:/tmp/" COLMAP_NAME "/1.ply");
		snPly = sm->addMeshSceneNode(msPly, cmRoot, -1);//		snPly->setScale(CMPosScale);
		snPly->setMatrix(mtr);
		snPly->setMaterialType(EMT_POINT_CLOUD);
		snPly->setMaterialFlag(EMF_LIGHTING, false);
		snPly->setPickData(EPD_PointCloud);
	}
#endif

 

	Ctx->gd.CamNormal->setFOV(fovy);
	DP(("FOV %f", fovy * core::RADTODEG));
	Ctx->getSceneManager()->addCubeSceneNode(1, 0, 0, { 0,0,0 }, { 0,0,0 }, { 10,1,10 });
	cmArr.clear();
	core::matrix4 mRelToArRoot;	root->getAbsoluteTransformation().getInverse(mRelToArRoot);
	ColMapImage cmi;
	cmRoot->updateAbsolutePosition();
	core::matrix4 mul = //mRelToArRoot * 
		cmRoot->getAbsoluteTransformation();
	while (std::getline(fstr, lineStr)) {
		DP(("%d: %s", id, lineStr.c_str()));
		int fid, cid;
		char fn[256];
		core::vector3df pos, rtt;
		glm::quat gq;
		sscanf(lineStr.c_str(), "%d %f %f %f %f %f %f %f %d %s\n", &fid, &gq.w, &gq.x, &gq.z, &gq.y, &pos.X, &pos.Z, &pos.Y, &cid, fn);
		//pos.X = -pos.X;
		pos.Y = -pos.Y;
		//pos.Z = -pos.Z;

		pos *= CMPosScale;
		gq.x = -gq.x; 
		//gq.y = -gq.y;
		gq.z = -gq.z;
#if 1

		core::matrix4 mtr = //glm::scale(glm::mat4(1), glm::vec3(1, -1, 1)); //
		//glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 0, 1)) ;
		glm::mat4(1);
		//auto oldRtt=cmRoot->getRotation();
		//cmRoot->setMatrixTR(mtr);
		//cmRoot->setRotation(cmRoot->getRotation() + core::vector3df( oldRtt.X, oldRtt.Y, oldRtt.Z ));

		glm::mat4 m =
			//glm::rotate(glm::mat4(1), -60 * core::DEGTORAD, glm::vec3(1, 0, 0))*
			//glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 0, 1))
			 

			 
			glm::mat4(gq)
			* glm::translate(glm::mat4(1), (float3)pos)

			* glm::rotate(glm::mat4(1), -core::PI, glm::vec3(0, 0, 1))
			* glm::rotate(glm::mat4(1), core::PI/2, glm::vec3(1, 0, 0))
			//* glm::rotate(glm::mat4(1), -core::PI / 6, glm::vec3(1, 0, 0))
			;
		cmi.omat =   m;
		cmi.snCam = sm->addEmptySceneNode( cmRoot, -1);
		cmi.snCam->setPosition(cmi.omat.getTranslation());
		cmi.snCam->setRotation(cmi.omat.getRotationDegrees());
		cmi.snCam->setScale({ CMCamScale,CMCamScale ,CMCamScale });
			// sm->addMeshSceneNode(ms, cmRoot, -1, cmi.omat.getTranslation(), cmi.omat.getRotationDegrees(), { CMCamScale,CMCamScale ,CMCamScale });
		
		cmi.matout =  	 	cmi.omat;
#else
		core::quaternion qt = *(core::quaternion*)&gq;
		qt.toEuler(rtt); rtt *= core::RADTODEG; rtt += {0, 0, 180};
		auto snRtt = sm->addEmptySceneNode(cmRoot); snRtt->setRotation(rtt);
		auto snRtt1 = sm->addEmptySceneNode(snRtt); snRtt1->setPosition(pos);
		cmi.snCam = //SceneManager->addEmptySceneNode(snRtt1);// 
			sm->addMeshSceneNode(ms, snRtt1, -1, { 0,0,0 }, { 0,0,0 }, { 50,50 ,50 });
		cmi.snCam->setRotation({ 0,0,-180 });
		cmi.snCam->updateTransform();
		cmi.mat = cmi.snCam->getAbsoluteTransformation();
#endif
		cmi.fn = fn;
		cmi.frameId = atoi(cmi.fn.substr(cmi.fn.find_first_of("_")+1).c_str());
		cmi.fn = (io::path(COLMAP_PATH "/in/") + fn).c_strA();
		cmi.tex = nullptr;
		cmArr.push_back(cmi);

		id++;
		std::getline(fstr, lineStr);
	}

#if REVERSE_OUTPUT
	std::sort(cmArr.begin(), cmArr.end(), [](auto& a, auto& b) {return a.fn > b.fn; });
#else
	std::sort(cmArr.begin(), cmArr.end(), [](auto& a, auto& b) {return a.fn < b.fn; });
#endif
	UaJsonSetting jss;
	Json::Value frames, models;
	core::vector3df ort,art; bool first = true;
	for (auto& cmi : cmArr) {

		//auto pos = cmi.mat.getTranslation();
		//auto rtt = cmi.mat.getRotationDegrees();
		//if (first) {
		//	first = false;
		//	ort = rtt;
		//}
		//else {
		//	//irr rotation is 0-360
		//	if		(ort.X > 270 && rtt.X < 90) art.X += 360;
		//	else if (ort.X < 90 && rtt.X > 270) art.X -= 360;
		//	if		(ort.Y > 270 && rtt.Y < 90) art.Y += 360;
		//	else if (ort.Y < 90 && rtt.Y > 270) art.Y -= 360;
		//	if		(ort.Z> 270 && rtt.Z < 90) art.Z += 360;
		//	else if (ort.Z < 90 && rtt.Z > 270) art.Z -= 360;
		//}
		//ort = rtt;
		//rtt += art;
		Json::Value arr;
#if REVERSE_OUTPUT
		arr.append(cmArr.size()-cmi.frameId);
#else
		arr.append(cmi.frameId);
#endif
		//arr.append(pos.X); arr.append(pos.Y); arr.append(pos.Z);
		//arr.append(rtt.X); arr.append(rtt.Y); arr.append(rtt.Z);
		//cmi.matout=mul * cmi.omat;
		for (int i = 0; i < 16; i++)
		{
			arr.append(cmi.matout[i]);
		}
		frames.append(arr);
	}

	{
		Json::Value arr; core::matrix4 mi;
		cmRoot->updateAbsolutePosition();
		cmRoot->getAbsoluteTransformation().getInverse(mi);
		arr.append("pointCloud");
		//snPly->updateAbsolutePosition();
		core::matrix4 mat = cmRoot->getAbsoluteTransformation();// mul;// mRelToArRoot* cmRoot->getAbsoluteTransformation();// mi;
		DP(("CMROOT Mat %f,%f,%f,%f", mat[00], mat[1], mat[2], mat[3]));
		for (int i = 0; i < 16; i++)		
			arr.append(mat[i]);
		Ctx->gd.CMCloudMat = mat;
		models.append(arr);

	}
	jss.AddChildValue("format", "raw");
	jss.AddChildValue("ver", 1);
	jss.AddChildValue("fovY", fovy);
	jss.AddChildValue("fps", COLMAP_FPS);
	jss.AddChildValue("frameCount", cmArr.size());
	jss.AddChildValue("frames", frames);
	jss.AddChildValue("models", models);
	jss.AddChildValue("arRootScale", root->getScale().X);
	jss.SetFile("E:/tmp/sfm.json", false);
	cmJson = jss.saveToString(false);
	jss.SaveFile();
	DP(("======================================SAVED JSON================================="));
	std::string sJson = jss.saveToString(false);

#if COLMAP_CHANGE_MP4_FPS_PTS
	UaFFmpegFile ff(0, Ctx, mFF);
	InputMediaInfo fi;
	fi.filePath = COLMAP_PATH ".mp4";
	fi.copyStream = true;
	fi.copyFPS = 30;
	fi.copyToFile =    "R:/" COLMAP_NAME ".mp4";
	//fi.dataStr = sJson;
	ff.doDecodeFile(fi, false);
#endif
	return;

	//auto ImgEnc = new FFImgVideoEncoder(Ctx);;
	//VideoParam videoPm;
	//videoPm.w = cmArr[0].tex->getSize().Width;
	//videoPm.h = cmArr[0].tex->getSize().Height;
	//videoPm.file = "R:\\outputCam.mp4";
	//videoPm.camDat = sJson;
	//videoPm.fps = 30;
	//ImgEnc->InitEncoder();
	//ImgEnc->EncBegin(videoPm);
	//int fid = 0;
	//for (auto& cmi : cmArr) {
	//	ImgEnc->AddImageFrame(nullptr, cmi.tex, 1.0 / 30 * 1000000 * fid, 10, false);
	//	fid++;
	//}
	//ImgEnc->EncEnd();
	//delete ImgEnc;


#endif
}
void VideoProcessor::testColmapPly(io::path spath, bool force ) {
	if (spath.size() < 5) return;
 
	
	static io::path fns[] = { "1.ply", "fused.ply","meshed-delaunay.ply", "meshed-poisson.ply","1s.ply", };
	static int isPointClouid[] = { 1,1,0,0};
	static int n = -1; 
	if (!snPly || force) {
		n = (n + 1) % (sizeof(fns) / sizeof(io::path));
		spath = spath.pathGetFileName();
		spath.remove(L".mkv");
		spath = io::path(COLMAP_DIR) + spath + "/" + fns[n];
#if REVERSE_OUTPUT
		spath = io::path(COLMAP_DIR COLMAP_NAME "/") + fns[n];
#endif
		bool v = snPly ? snPly->isVisible() : true;
		
		auto sm = Ctx->getSceneManager();
		if (!cmRoot) {
			cmRoot = sm->addEmptySceneNode();
		}
		Eqv->MatRec->rootNode = cmRoot;
		cmRoot->setMatrix(Ctx->gd.CMCloudMat);
		
		{
			irr::scene::IMeshSceneNode*  newPly{};
			auto sm = Ctx->getSceneManager();
			core::matrix4 mtr = //glm::rotate(glm::mat4(1), -core::PI / 2, glm::vec3(1, 0, 0))
				glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 1, 0))
				//
				*
				glm::scale(glm::mat4(1), glm::vec3(CMPosScale, -CMPosScale, CMPosScale))
				;

			auto msPly = sm->getMesh(//"R:/1.OBJ");
				spath);//"/1.obj");// "/meshed-delaunay.ply"); // //meshed-poisson.ply");//);// " / 
			if (!msPly) return;
			newPly = sm->addMeshSceneNode(msPly, cmRoot, -1);//	bb	snPly->setScale(CMPosScale);
			if (newPly) {
				//core::matrix4 cami; Ctx->gd.CamNormal->getAbsoluteTransformation().getInverse(cami);
				//cmRoot->updateAbsolutePosition();

				newPly->setMatrix(mtr);
				if (isPointClouid[n])
					newPly->setMaterialType(EMT_POINT_CLOUD);
				newPly->setMaterialFlag(EMF_LIGHTING, !isPointClouid[n]);
				newPly->setPickData(EPD_PointCloud);
				newPly->setVisible(v);

				if (snPly) snPly->remove();
				snPly = newPly;
#if 0
				snPly->getMaterial(0).RcvShadow = true;
				snPly->getMaterial(0).DiffuseColor = 0xC0080808; //0xC0FF8080;
				snPly->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite);
				snPly->setPassType(IrrPassType_Mirror, false);
#endif
			}
		}
	}
		//snPly->setScale(0.999);
		{
			
			snPly->updateTransform();
			
			auto cldpt = (S3DVertex*)snPly->getMesh()->getMeshBuffer(0)->getVertices();
			int vtc = snPly->getMesh()->getMeshBuffer(0)->getVertexCount();
			for (int i = 0; i < vtc; i++) {
				auto& pt = cldpt[i];
				pt.TCoords.X = 0;

			}
		}
	
}

void VideoProcessor::colmapFw()
{
	//if (cmArr.size() > 0) return;
	auto cldpt = (S3DVertex*)snPly->getMesh()->getMeshBuffer(0)->getVertices();
	int vtc = snPly->getMesh()->getMeshBuffer(0)->getVertexCount();
	int fid = Eqv->getFwIdxByFwIdStr(1, "mmdVtx9");// getCurPtrFw(1)->gfd.FwId;
	auto m = snPly->getAbsoluteTransformation();
	auto cam = Ctx->getSceneManager()->getActiveCamera();
	auto camPos = cam->getAbsolutePosition();
	auto srcPos = camPos + core::vector3df( 0, -100, 0 );
	auto dir = (cam->getTarget() - camPos).normalize();
	for (int i = 0; i < vtc; i++) {
		auto& pt = cldpt[i];
		if (pt.TCoords.X == 0 && ualib::UaRandF()<0.001f) {
			core::vector3df pickPos = pt.Pos;
			m.transformVect(pickPos);
			if (pickPos.getDistanceFromSQ(camPos)<2000000) {
				if (dir.angleBetweenInDegree((pickPos - camPos).normalize()) < 30) {
					EQVisual::LfwParam lpm; float3 tgt = pickPos; srcPos = tgt;
					Eqv->LaunchFw3D(srcPos, fid, { 0,1000,0 }, pt.Color, &lpm, &tgt);
				}
					pt.TCoords.X = 1;	
			}
		};

	}
}

void VideoProcessor::colmapWriteObj(irr::core::matrix4 mat)
{
	using namespace irr::scene;

	auto mesh = snPly->getMesh();
 
	core::quaternion qt = mat;
	u32 allVertexCount = 1; // count vertices over the whole file
#if 1
	for (u32 i = 0; i < mesh->getMeshBufferCount(); ++i)
	{
		IMeshBuffer* buffer = mesh->getMeshBuffer(i);
		if (buffer && buffer->getVertexCount())
		{

			u32 j;
			const u32 vertexCount = buffer->getVertexCount();
			auto pv = (S3DVertex*)buffer->getVertices();
			for (j = 0; j < vertexCount; ++j)
			{
				mat.transformVect(pv[j].Pos);
				pv[j].Normal = (qt * pv[j].Normal).normalize();

			}
		}
		buffer->setDirty(EBT_VERTEX);
	}
#endif
	auto mr=Ctx->getSceneManager()->createMeshWriter(EMESH_WRITER_TYPE::EMWT_OBJ);
	auto wf = Ctx->getFileSystem()->createAndWriteFile("r:/out.obj");
	mr->writeMesh(wf, mesh);



	wf->drop();
	mr->drop();
}
