#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;

#include "colorutil.glsl"

layout (binding = 0) uniform UBO 
{
    int mode;
    uint flag;
    int bit,pad3;
    vec4 fv;
	vec4 pad[15 - 2];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;

layout(location = 0) in vec4 iColorD;
layout(location = 1) in vec2 iTex0;

layout (location = 0) out vec4 outFragColor;


float getVal(vec2 uv)
{
    return length(texture(g_tex0_sampler,uv).xyz);
}
    
vec2 getGrad(vec2 uv,float delta)
{
    vec2 d=vec2(delta,0);
    return vec2(
        getVal(uv+d.xy)-getVal(uv-d.xy),
        getVal(uv+d.yx)-getVal(uv-d.yx)
    )/delta;
}

// adding pixel shader
void main()
{
    vec4 tc=texture(g_tex0_sampler,iTex0);

    switch (ubo.mode) {
    case 1:
    {	
	    vec2 tpos=(iTex0-0.5)*2;
        tc.a=clamp(1-pow(dot(tpos,tpos),5),0,1);
    }
    break;
    case 2:{	
#if 1
	vec2 uv = iTex0;
    vec3 n = vec3(getGrad(uv,1.0/ubo.res.y),180.0*(1.1+sin(ubo.fv.x*3.1415927*2) ));
    //n *= n;
    n=normalize(n);
    outFragColor=vec4(n,1);
    vec3 light = normalize(vec3( 1 ,1,2));
    float diff=clamp(dot(n,light),0.5,1.0);
    float spec=clamp(dot(reflect(light,n),vec3(0,0,-1)),0.0,1.0);
    spec=pow(spec,20.0)*7.5;
   // tc.rgb = tc.rgb*vec3(diff)+vec3(spec);
 
tc  = tc *(diff)+(spec);
	    vec3 hsl=RGBtoHSL(tc.rgb);
        hsl.x= fract(hsl.x+0.1*sin(ubo.fv.x*3.1415927*2));
        tc.rgb= HSLtoRGB(hsl);
#endif
    }
    break;
    case 3: //DEPTH to COLOR
    {
        float n = ubo.fv.x; // camera z near
        float f = ubo.fv.y; // camera z far 
        float z = tc.x;

        #if 0
        // Convert depth to linear depth
        z = z * 2.0 - 1.0;
        float linearDepth = 2.0 * n * f / (f + n - depth * (f - n));        
        // Normalize linear depth
        float c= (linearDepth - n) / (f - n);
        #else
        float c=   (1-(2.0 * n) / (f + n - z * (f - n)))*ubo.fv.z;	 // DEPTH to COLOR value
        #endif
        if ( ubo.flag==1) c=1-c;
        tc=vec4(c,c,c,1); 
    }
    break;
    case 4:
    {
       const float weight[9]={0.5,1,0.5, 1,2,1,0.5,1,0.5};        
        //const float weight[9]={1,1,1,1,1,1,1,1,1};    
        float tx=iTex0.x,ty=iTex0.y;
        vec2 divRes=1.0f/ ubo.res ;        
        vec2 sumNorm =vec2(0.0f);
        int ixy=0;
#if 1
        for (int y=-1; y<=1;y++)for (int x=-1;x<=1;x++)
        {
            vec2 cad = vec2(x,y) * divRes;
            
            //vec2 h1=texture( g_tex1_sampler, vec2(tx-dx,ty)).ra;
            float dx=texture( g_tex0_sampler, iTex0+cad+vec2(divRes.x,0)).r;
            float dy=texture( g_tex0_sampler, iTex0+cad+vec2(0,divRes.y)).r;
            
            //vec2 v2=texture( g_tex1_sampler, vec2(tx,ty-dy)).ra;

            //float dh=(h1.x+h1.y-h2.x-h2.y);	float dv=(v1.x+v1.y-v2.x-v2.y);


            float dh=((tc.r-dx));	float dv=  ((tc.r-dy));
            sumNorm.x+=dh * weight[ixy]; sumNorm.y+=dv * weight[ixy];
            ixy++;
        }
#else
        float n = ubo.fv.x; // camera z near
        float f = ubo.fv.y; // camera z far 
        float z = tc.x;
        float c=  (1-(2.0 * n) / (f + n - z * (f - n)))*ubo.fv.z;	

   

        for (int y=-1; y<=1;y++)for (int x=-1;x<=1;x++)
        {
            vec2 cad = vec2(x,y) * divRes;
            
            //vec2 h1=texture( g_tex1_sampler, vec2(tx-dx,ty)).ra;
            float dx=texture( g_tex0_sampler, iTex0+cad+vec2(divRes.x,0)).r;
            float dy=texture( g_tex0_sampler, iTex0+cad+vec2(0,divRes.y)).r;
            
            //vec2 v2=texture( g_tex1_sampler, vec2(tx,ty-dy)).ra;

            //float dh=(h1.x+h1.y-h2.x-h2.y);	float dv=(v1.x+v1.y-v2.x-v2.y);
            float dcx=  (1-(2.0 * n) / (f + n -dx * (f - n)))*ubo.fv.z;	
            float dcy=  (1-(2.0 * n) / (f + n -dy * (f - n)))*ubo.fv.z;
            float dh=clamp((c-dcx),-0.1,0.1);	float dv= clamp((c-dcy),-0.1,0.1);
            sumNorm.x+=dh * weight[ixy]; sumNorm.y+=dv * weight[ixy];
            ixy++;
        }
#endif
        sumNorm/=8.f;
       // tc= vec4(.5+.5*normalize(vec2((sumNorm.x),(sumNorm.y))),0.5,1);
       if (sumNorm.x==0 && sumNorm.y==0)
         tc=vec4(0.5,0.5,1,1);
       else 
         tc= vec4(.5+.5*normalize(vec3((sumNorm.x),(sumNorm.y), 0.f )),1);
        tc.rgba=tc.bgra;
    }
    break;
    case 5:
    {
            if (iTex0.y>ubo.fv.y) tc=vec4(0,0,0,0);
    }
    break;
    case 6:
    {
            if (iTex0.y<ubo.fv.y) tc=vec4(0,0,0,0);
    }
    break;
    
    }
    outFragColor = tc;
}

