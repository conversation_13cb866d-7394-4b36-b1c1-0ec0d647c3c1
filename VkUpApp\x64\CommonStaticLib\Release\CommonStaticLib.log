﻿  MMDCamera.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDCamera.cpp')
  
  MMDIkSolver.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/MMDIkSolver.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(16,34): warning C4305: 'initializing': truncation from 'double' to 'const float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(16,41): warning C4305: 'initializing': truncation from 'double' to 'const float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,11): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,20): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,31): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,39): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,47): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,58): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,66): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,75): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,83): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(21,90): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,42): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,50): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,59): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,70): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,79): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,86): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,97): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,105): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,114): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,122): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305: 'initializing': truncation from 'double' to '_Ty'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:             _Ty=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDIkSolver.cpp(23,129): warning C4305:         ]
  MMDMaterial.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDMaterial.cpp')
  
  MMDModel.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file 'saba/src/Saba/Model/MMD/MMDModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDModel.cpp(382,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  MMDNode.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDNode.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDNode.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDNode.cpp')
  
  MMDPhysics.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysics.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysics.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysics.cpp')
  
  MMDPhysicsPhysX.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(205,48): warning C4311: 'type cast': pointer truncation from 'void *' to 'physx::PxU32'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(205,48): warning C4302: 'type cast': truncation from 'void *' to 'physx::PxU32'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(420,36): warning C4244: '=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(422,37): warning C4244: '=': conversion from 'physx::PxI32' to 'PhyReal', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(426,36): warning C4244: '=': conversion from 'double' to 'PhyReal', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\physxinc.inl(468,14): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(93,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(94,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305: '=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(95,33): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(100,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(111,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(118,35): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(125,51): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(300,34): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(308,41): warning C4834: discarding return value of function with [[nodiscard]] attribute
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(309,9): warning C4834: discarding return value of function with [[nodiscard]] attribute
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(366,29): warning C4305: 'argument': truncation from 'double' to 'physx::PxReal'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(907,67): warning C4311: '<function-style-cast>': pointer truncation from 'void *' to 'physx::PxU32'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(907,67): warning C4302: '<function-style-cast>': truncation from 'void *' to 'physx::PxU32'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1034,19): warning C4267: 'argument': conversion from 'size_t' to 'const uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1982,29): warning C4267: 'argument': conversion from 'size_t' to 'const uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(1988,110): warning C4267: 'argument': conversion from 'size_t' to 'physx::PxU32', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysicsPhysX.cpp(2143,22): warning C4018: '<': signed/unsigned mismatch
  MMDPhysicsPhysX_vehicle.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/MMDPhysicsPhysX_vehicle.cpp')
  
  PMXFile.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXFile.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(796,47): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(799,22): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(823,31): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(824,39): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(832,52): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(835,36): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(839,29): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(862,141): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1026,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1028,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1030,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1032,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1034,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(1036,23): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXFile.cpp')
      D:\AProj\CommonStaticLib\saba\src\Saba\Base\File.h(72,37):
      the template instantiation context (the oldest one first) is
          D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile.cpp(64,16):
          see reference to function template instantiation 'bool saba::File::Read<char16_t>(T *,size_t)' being compiled
          with
          [
              T=char16_t
          ]
  
  PMXFile_Generator.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXFile_Generator.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(492,87): warning C4267: 'argument': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(649,31): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(649,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(674,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(674,32): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(675,39): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(675,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(817,35): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(818,35): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(819,122): warning C4267: 'argument': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(819,44): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(842,34): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(852,30): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(853,39): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(854,39): warning C4267: 'initializing': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(894,51): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(912,53): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(916,45): warning C4267: 'initializing': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_Generator.cpp(999,26): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  PMXFile_P2.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXFile_P2.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(174,27): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(194,34): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267: '=': conversion from 'size_t' to '_Ty', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:             _Ty=int32_t
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(207,30): warning C4267:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(228,21): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(349,62): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(356,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(369,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(384,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(414,25): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(429,26): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(512,26): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(546,31): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(547,31): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(604,61): warning C4018: '<': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXFile_P2.cpp(613,24): warning C4267: '-=': conversion from 'size_t' to 'int32_t', possible loss of data
  PMXModel.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXModel.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXModel.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(971,21): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1188,66): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1245,48): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1250,48): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1270,79): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244: '+=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1365,51): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305: '*=': truncation from 'double' to 'T'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1367,30): warning C4305:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1368,49): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244: '=': conversion from 'double' to 'T', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         with
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         [
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:             T=float
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1369,49): warning C4244:         ]
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1410,40): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1442,37): warning C4267: '=': conversion from 'size_t' to 'int32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1548,58): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1555,34): warning C4312: 'type cast': conversion from 'int' to 'void *' of greater size
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1572,16): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1624,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1635,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1655,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(1669,5): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2170,14): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2346,52): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2348,80): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2368,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2378,28): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2379,25): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2569,31): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(2598,30): warning C4018: '>=': signed/unsigned mismatch
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(3333,11): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4046,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4096,39): warning C4267: '=': conversion from 'size_t' to 'uint32_t', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\PMXModel.cpp(4114,10): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  PMXWriter.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/PMXWriter.cpp')
  
  VMDAnimation.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(532,43): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(553,21): warning C4085: expected pragma parameter to be 'on' or 'off'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(553,25): warning C4081: expected 'newline'; found ')'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(575,21): warning C4085: expected pragma parameter to be 'on' or 'off'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(575,24): warning C4081: expected 'newline'; found ')'
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(667,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.cpp(668,33): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  VMDCameraAnimation.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/VMDCameraAnimation.cpp')
  
  RenderPrimitive.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
  PMXGenerator_Test.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../CommonStaticLib/saba/src/Saba/Model/MMD/pmx/PMXGenerator_Test.cpp')
  
  CommonStaticLib.vcxproj -> D:\AProj\VkUpApp\x64\Release\CommonStaticLib.lib
