#pragma once
#include "../MrSnTemplate.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/shader/snTemplate/VkFluid/VkFluidSharedHeader.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkHardwareBuffer.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/base/VulkanBuffer.hpp"
#include "../../../UaIrrlicht/include/ITexture.h"
#include <memory>

#define VKFUILD_HAS_DEPTH 1
#define FLUID_SCALE 0
#if FLUID_SCALE
const float FluidSC = 1.0f;
#define MUL_FLUID_SCALE(x) ((x) * FluidSC)
#else
#define MUL_FLUID_SCALE(x) (x)
#endif


namespace irr
{
namespace video
{
    // Volume rendering uniforms structure (legacy - for compute shaders)
    struct VkFluidVolumeUniforms {
        // Volume resolution (3D texture dimensions) - for C++ only
        u32 volumeResX = 128;
        u32 volumeResY = 128; 
        u32 volumeResZ = 128;
        u32 flag; // Padding for alignment
        
        // GPU shader uniforms (must match shader layout)
        core::vector3df volumeMin;
        f32 time;
        core::vector3df volumeMax;
        f32 rayStepSize;
        core::vector3df volumeSize;
        f32 densityThreshold;
        float4 baseColor;
        glm::vec3 absorb;
        int renderDiffuse;
        u32 numParticles;
        u32 maxRaymarchSteps;
        f32 gridCellSize;        // Grid cell size in world units
        f32 kernelRadius = 0.2f;
        core::vector3df lightDir;
        f32 traceMaxDensity;
        glm::vec4 pm1 = glm::vec4(1);
		glm::vec4 pm2 = glm::vec4(1);
        glm::vec4 mtrColor[32];
        // Additional C++-only fields (not sent to GPU)

 
        
    };
	static_assert(sizeof(VkFluidVolumeUniforms)%16== 0);
// ShaderToy-compatible uniform buffer structure (FLAT layout to match shader exactly)
struct CbVkFluid
{
    float4x4 mViewProjectionInverse;  // mat4 - For screen-to-world ray computation
 
    // ShaderToy standard uniforms (must match shader layout EXACTLY)
    float3 iResolution;    // vec3 in shader - 12 bytes + 4 padding = 16 bytes total
    f32 iTime;             // float in shader - 4 bytes
    float2 pad01;          // vec2 in shader - 8 bytes
    f32 iTimeDelta;        // float in shader - 4 bytes
    s32 iFrame;            // int in shader - 4 bytes
    float4 iMouse;         // vec4 in shader - 16 bytes
    float4 iDate;          // vec4 in shader - 16 bytes

    // Fluid-specific extensions
     float4 pad;     // vec4 - RGBA fluid color
    float4 volumeMin;      // vec4 - xyz=volume bounds min
    float4 volumeMax;      // vec4 - xyz=volume bounds max
    float4 cameraPos;      // vec4 - xyz=camera position, w=unused


    // Screen resolution info (at the end to match shader)
    float2 res;            // vec2 - screen resolution
    f32 resWdH;            // float - w/h ratio
    f32 resHdW;            // float - h/w ratio

    VkFluidVolumeUniforms u;
    
};
    static_assert(sizeof(CbVkFluid) ); 



class MrVkFluid : public VkMaterialRenderer
{
public:
    // Fluid rendering pipeline modes
    enum FluidRenderPipeline {
        FRP_RAYMARCHING = 0,    // Volume raymarching mode
        FRP_SURFACE = 1,        // Surface extraction mode
        FRP_DEBUG = 2           // Debug visualization mode
    };

    MrVkFluid(IVideoDriver* driver, io::IFileSystem* fileSystem);
    virtual ~MrVkFluid();

    // Inherited via VkMaterialRenderer
    virtual void InitMaterialRenderer() {};

    // IMaterialRenderer interface
    virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1) override;
    virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                              bool resetAllRenderstates, IMaterialRendererServices* services) override;
    virtual void OnUnsetMaterial() override;
    virtual bool isTransparent() const override { return true; }

    // VkMaterialRenderer interface
    virtual const void* getShaderByteCode() const override;
    virtual u32 getShaderByteCodeSize() const override;
    virtual void cleanFrameCache() override;
    virtual void preSubmit() override;
    virtual void ReloadShaders() override;

    // Fluid-specific methods
    void setTime(f32 time) {
        m_time = time;
        cb.iTime = time;
        m_needsUpdate = true;
    }
    void setDeltaTime(f32 deltaTime) {
        m_deltaTime = deltaTime;
        cb.iTimeDelta = deltaTime;
        m_needsUpdate = true;
    }
    void setRenderPipeline(FluidRenderPipeline pipeline) { m_renderPipeline = pipeline; }
	void transitDepthImageLayout();
    // ShaderToy compatibility methods (following VkMr2D pattern)
    void setResolution(const float3& resolution);
    void setFrame(s32 frame);
    void setMouse(const float4& mouse);

    // Volume parameters
    void setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds);
    void setRaymarchParameters(u32 maxSteps, f32 stepSize, f32 densityThreshold);

 
    void setAbsorptionColor(const core::vector3df& absorption);
    void setCameraPosition(const core::vector3df& position);

    // Matrix setters
    void setWorldMatrix(const core::matrix4& world);
    void setViewProjMatrix(const core::matrix4& view, const core::matrix4& projection); 
    
    // Buffer management
    void setParticleBuffer(VkHardwareBuffer* buffer);
    void setParticleGridTexture(ITexture* texture);
    void setParticleGridTextureSecondary(ITexture* texture);
    void setCovarianceBuffer(VkHardwareBuffer* buffer);

    // Volume texture setters
    void setDensityFieldTexture(ITexture* texture);
    void setVelocityFieldTexture(ITexture* texture);
    void setDistanceFieldTexture(ITexture* texture);
    void setCovarianceFieldTexture(ITexture* texture);
    
    // Depth texture setter for depth testing
    void setDepthTexture(ITexture* texture);

	// Set environment map for reflections
	void setEnvironmentMap(ITexture* texture);

    // Volume uniforms update
    void updateVolumeUniforms(const VkFluidVolumeUniforms& uniforms);

    // Compute shader dispatching
    void dispatchParticleToGridComputation3D(const core::array<float4>& particles, u32 resX, u32 resY, u32 resZ);

    void dispatchCovarianceComputation3D(u32 resX, u32 resY, u32 resZ);

    void waitForComputeCompletion();

private:
    void initializeShaders();
    void createUniformBuffers();
    void createDefaultSampler();
    void createComputeCommandBuffers();
    void setupDescriptorSets();
    void recreatePipelines();
    void createPipelineLayouts();
    void createGraphicsPipelines();
    void createComputePipelines();
    void updateUniformBuffers();
    void updateViewProjectionInverse();                // Update view-projection inverse matrix
    void updateGraphicsDescriptorSetsForShaderRead();  // Update graphics descriptor sets after layout transitions

    // File system reference
    io::IFileSystem* m_fileSystem;

    // Shaders
    std::shared_ptr<VkFxUtil::VkFxBase> m_vertexFragmentShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_particleToGridComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_densityComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_velocityComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_distanceComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_covarianceComputeShader; 

    // Vulkan resources - Graphics
    VkDescriptorSet m_descriptorSet;
    VkDescriptorSetLayout m_descriptorSetLayout;
    VkPipelineLayout m_pipelineLayout;
    VkPipeline m_graphicsPipeline;

    // Vulkan resources - Compute
    VkDescriptorSet m_particleToGridComputeDescriptorSet;
    VkDescriptorSet m_densityComputeDescriptorSet;
    VkDescriptorSet m_velocityComputeDescriptorSet;
    VkDescriptorSet m_distanceComputeDescriptorSet;
    VkDescriptorSet m_covarianceComputeDescriptorSet;
    VkDescriptorSet m_mainComputeDescriptorSet;
    VkDescriptorSetLayout m_computeDescriptorSetLayout;
    VkDescriptorSetLayout m_particleToGridDescriptorSetLayout;
    VkPipelineLayout m_computePipelineLayout;
    VkPipelineLayout m_particleToGridPipelineLayout;
    VkPipeline m_particleToGridComputePipeline;
    VkPipeline m_densityComputePipeline;
    VkPipeline m_velocityComputePipeline;
    VkPipeline m_distanceComputePipeline;
    VkPipeline m_covarianceComputePipeline;
    VkPipeline m_mainComputePipeline;

    // Compute command buffers and synchronization
    VkCommandBuffer m_particleToGridComputeCommandBuffer{}; 
    VkCommandBuffer m_velocityComputeCommandBuffer;
    VkCommandBuffer m_distanceComputeCommandBuffer;
    VkCommandBuffer m_covarianceComputeCommandBuffer;
    VkCommandBuffer m_mainComputeCommandBuffer;
    VkFence m_computeFence;

    // Descriptor pool
    VkDescriptorPool m_descriptorPool;
    VkSampler m_defaultSampler;

    // Uniform buffers
    vks::Buffer m_cbBuffer;
    vks::Buffer m_computeUniformBuffer;
    vks::Buffer m_dummyStorageBuffer;

    // Uniform data
    VkFluidVolumeUniforms m_uniforms;
    VkFluidVolumeUniforms m_computeParams;
    CbVkFluid cb;

    // Particle buffers
    VkHardwareBuffer* m_particleBuffer;
    VkHardwareBuffer* m_covarianceBuffer;

    // Volume textures
    ITexture* m_gridTexture;           // Particle grid texture (storage image)
    ITexture* m_gridTextureSecondary;  // Secondary particle grid texture (storage image)
    ITexture* m_densityTexture;
#if COVARIANCE_COMPUTATION
    ITexture* m_covarianceTexture;
#endif
    ITexture* m_depthTexture;          // Depth buffer texture for depth testing
    ITexture* m_environmentTexture;

    // Transformation matrices
    core::matrix4 m_worldMatrix;
    core::matrix4 m_viewMatrix;
    core::matrix4 m_projectionMatrix;

    // State
    f32 m_time;
    f32 m_deltaTime;
    bool m_needsUpdate;
    FluidRenderPipeline m_renderPipeline;
    VkImage vkDepthImg{};
    
    // Static material type
    static E_MATERIAL_TYPE s_materialType;
};

} // namespace video
} // namespace irr