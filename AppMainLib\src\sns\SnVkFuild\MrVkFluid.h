#pragma once

#include "../MrSnTemplate.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkHardwareBuffer.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/base/VulkanBuffer.hpp"
#include "../../../UaIrrlicht/include/ITexture.h"
#include <memory>

namespace irr
{
namespace video
{

// ShaderToy-compatible uniform buffer structure (FLAT layout to match shader exactly)
struct CbVkFluid
{
    // ShaderToy standard uniforms (must match shader layout EXACTLY)
    float3 iResolution;    // vec3 in shader - 12 bytes + 4 padding = 16 bytes total
    f32 iTime;             // float in shader - 4 bytes
    float2 pad01;          // vec2 in shader - 8 bytes
    f32 iTimeDelta;        // float in shader - 4 bytes
    s32 iFrame;            // int in shader - 4 bytes
    float4 iMouse;         // vec4 in shader - 16 bytes
    float4 iDate;          // vec4 in shader - 16 bytes

    // Fluid-specific extensions
    float4 fluidParams;    // vec4 - x=refractionIndex, y=densityThreshold, z=rayStepSize, w=maxSteps
    float4 fluidColor;     // vec4 - RGBA fluid color
    float4 volumeMin;      // vec4 - xyz=volume bounds min
    float4 volumeMax;      // vec4 - xyz=volume bounds max
    float4 cameraPos;      // vec4 - xyz=camera position, w=unused
    float4x4 mViewProjectionInverse;  // mat4 - For screen-to-world ray computation

    // Screen resolution info (at the end to match shader)
    float2 res;            // vec2 - screen resolution
    f32 resWdH;            // float - w/h ratio
    f32 resHdW;            // float - h/w ratio
};

// Volume rendering uniforms structure (legacy - for compute shaders)
struct VkFluidVolumeUniforms {
    core::matrix4 mViewProjectionInverse;
    core::vector3df volumeMin;
    f32 time;
    core::vector3df volumeMax;
    f32 rayStepSize;
    core::vector3df volumeSize;
    f32 densityThreshold;
    core::vector3df cameraPosition;
    u32 maxRaymarchSteps;
    f32 noiseScale;
    f32 refractionIndex;
    f32 absorptionScale;
    f32 scatteringScale;
    u32 numParticles;
    f32 padding0;
    f32 padding1;
    f32 padding2;
};

class MrVkFluid : public VkMaterialRenderer
{
public:
    // Fluid rendering pipeline modes
    enum FluidRenderPipeline {
        FRP_RAYMARCHING = 0,    // Volume raymarching mode
        FRP_SURFACE = 1,        // Surface extraction mode
        FRP_DEBUG = 2           // Debug visualization mode
    };

    MrVkFluid(IVideoDriver* driver, io::IFileSystem* fileSystem);
    virtual ~MrVkFluid();

    // Inherited via VkMaterialRenderer
    void InitMaterialRenderer() override {}

    // IMaterialRenderer interface
    virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1) override;
    virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                              bool resetAllRenderstates, IMaterialRendererServices* services) override;
    virtual void OnUnsetMaterial() override;
    virtual bool setVariable(const c8* name, const f32* floats, int count) override;
    virtual bool isTransparent() const override { return true; }

    // VkMaterialRenderer interface
    virtual const void* getShaderByteCode() const override;
    virtual u32 getShaderByteCodeSize() const override;
    virtual void cleanFrameCache() override;
    virtual void preSubmit() override;
    virtual void ReloadShaders() override;

    // Fluid-specific methods
    void setTime(f32 time) {
        m_time = time;
        m_shaderToyUniforms.iTime = time;
        m_needsUpdate = true;
    }
    void setDeltaTime(f32 deltaTime) {
        m_deltaTime = deltaTime;
        m_shaderToyUniforms.iTimeDelta = deltaTime;
        m_needsUpdate = true;
    }
    void setRenderPipeline(FluidRenderPipeline pipeline) { m_renderPipeline = pipeline; }

    // ShaderToy compatibility methods (following VkMr2D pattern)
    void setResolution(const float3& resolution);
    void setFrame(s32 frame);
    void setMouse(const float4& mouse);

    // Volume parameters
    void setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds);
    void setRaymarchParameters(u32 maxSteps, f32 stepSize, f32 densityThreshold);
    void setFluidColor(const SColor& color);
    void setRefractionIndex(f32 index);
    void setAbsorptionColor(const core::vector3df& absorption);
    void setCameraPosition(const core::vector3df& position);

    // Matrix setters
    void setWorldMatrix(const core::matrix4& world);
    void setViewMatrix(const core::matrix4& view);
    void setProjectionMatrix(const core::matrix4& projection);

    // Buffer management
    void setParticleBuffer(VkHardwareBuffer* buffer);
    void setCovarianceBuffer(VkHardwareBuffer* buffer);

    // Volume texture setters
    void setDensityFieldTexture(ITexture* texture);
    void setVelocityFieldTexture(ITexture* texture);
    void setDistanceFieldTexture(ITexture* texture);
    void setCovarianceFieldTexture(ITexture* texture);

    // Volume uniforms update
    void updateVolumeUniforms(const VkFluidVolumeUniforms& uniforms);

    // Compute shader dispatching
    void dispatchDensityComputation3D(u32 resX, u32 resY, u32 resZ);
    void dispatchVelocityComputation3D(u32 resX, u32 resY, u32 resZ);
    void dispatchDistanceComputation3D(u32 resX, u32 resY, u32 resZ);
    void dispatchCovarianceComputation3D(u32 numParticles);

    void waitForComputeCompletion();

private:
    void initializeShaders();
    void createUniformBuffers();
    void createDefaultSampler();
    void createComputeCommandBuffers();
    void setupDescriptorSets();
    void recreatePipelines();
    void createPipelineLayouts();
    void createGraphicsPipelines();
    void createComputePipelines();
    void updateUniformBuffers();

    // File system reference
    io::IFileSystem* m_fileSystem;

    // Shaders
    std::shared_ptr<VkFxUtil::VkFxBase> m_vertexFragmentShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_densityComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_velocityComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_distanceComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_covarianceComputeShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_mainComputeShader;

    // Vulkan resources - Graphics
    VkDescriptorSet m_descriptorSet;
    VkDescriptorSetLayout m_descriptorSetLayout;
    VkPipelineLayout m_pipelineLayout;
    VkPipeline m_graphicsPipeline;

    // Vulkan resources - Compute
    VkDescriptorSet m_densityComputeDescriptorSet;
    VkDescriptorSet m_velocityComputeDescriptorSet;
    VkDescriptorSet m_distanceComputeDescriptorSet;
    VkDescriptorSet m_covarianceComputeDescriptorSet;
    VkDescriptorSet m_mainComputeDescriptorSet;
    VkDescriptorSetLayout m_computeDescriptorSetLayout;
    VkPipelineLayout m_computePipelineLayout;
    VkPipeline m_densityComputePipeline;
    VkPipeline m_velocityComputePipeline;
    VkPipeline m_distanceComputePipeline;
    VkPipeline m_covarianceComputePipeline;
    VkPipeline m_mainComputePipeline;

    // Compute command buffers and synchronization
    VkCommandBuffer m_densityComputeCommandBuffer;
    VkCommandBuffer m_velocityComputeCommandBuffer;
    VkCommandBuffer m_distanceComputeCommandBuffer;
    VkCommandBuffer m_covarianceComputeCommandBuffer;
    VkCommandBuffer m_mainComputeCommandBuffer;
    VkFence m_computeFence;

    // Descriptor pool
    VkDescriptorPool m_descriptorPool;
    VkSampler m_defaultSampler;

    // Uniform buffers
    vks::Buffer m_uniformBuffer;
    vks::Buffer m_computeUniformBuffer;
    vks::Buffer m_dummyStorageBuffer;

    // Uniform data
    VkFluidVolumeUniforms m_uniforms;
    VkFluidVolumeUniforms m_computeParams;
    CbVkFluid m_shaderToyUniforms;

    // Particle buffers
    VkHardwareBuffer* m_particleBuffer;
    VkHardwareBuffer* m_covarianceBuffer;

    // Volume textures
    ITexture* m_densityTexture;
    ITexture* m_velocityTexture;
    ITexture* m_distanceTexture;
    ITexture* m_covarianceTexture;

    // State
    f32 m_time;
    f32 m_deltaTime;
    bool m_needsUpdate;
    FluidRenderPipeline m_renderPipeline;

    // Static material type
    static E_MATERIAL_TYPE s_materialType;
};

} // namespace video
} // namespace irr