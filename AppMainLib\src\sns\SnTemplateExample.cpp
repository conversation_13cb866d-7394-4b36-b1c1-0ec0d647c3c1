// Example usage of the SnVkPipelineTemplate scene node
// This file demonstrates how to create and use the template scene node
#include "appGlobal.h" // Include the precompiled header file
#include "SnVkPipelineTemplate.h"
#include "../../UaIrrlicht/include/irrlicht.h"

using namespace irr;
using namespace core;
using namespace scene;
using namespace video;

// Example function to create and use the template scene node
void createTemplateSceneNodeExample(ISceneManager* smgr, IVideoDriver* driver, io::IFileSystem* fs)
{
    // Initialize the material renderer (do this once at startup)
    SnVkPipelineTemplate::initializeMaterialRenderer(driver, fs);
    
    // Create parameters for the scene node
    SnVkPipelineTemplateParam params;
    params.maxVertices = 2000;
    params.maxIndices = 6000;
    params.enableCompute = true;
    params.enableLighting = true;
    params.initialPosition = vector3df(0, 1000, 500);
    params.materialColor = SColor(255, 128, 255, 128); // Light green
    
    // Create the scene node
    SnVkPipelineTemplate* templateNode = new SnVkPipelineTemplate(
        smgr->getRootSceneNode(), smgr, -1, params);

    // Set lighting properties
    templateNode->setLightDirection(vector3df(1, -1, 1));
    templateNode->setLightColor(SColor(255, 255, 255, 200)); // Warm white
    
    // Create different geometries (examples)
    
    // Example 1: Create a box
   templateNode->createBox(vector3df(200, 100, 100));
    
    // Example 2: Create a sphere (uncomment to use)
    // templateNode->createSphere(150.f, 32);
    
    // Example 3: Create a cylinder (uncomment to use)
    // templateNode->createCylinder(100.0f, 200.0f, 16);
    
    // Example 4: Create a plane (uncomment to use)
    // templateNode->createPlane(vector2df(300, 300), 10, 10);
    
    // Set animation properties
    templateNode->setAnimationSpeed(1.0f);
    templateNode->setWaveAmplitude(0.1f);
    templateNode->setWaveFrequency(2.0f);
    
    // Example of compute shader usage
    if (params.enableCompute) {
        // Create some test data for compute shader
        array<float4> computeData;
        for (u32 i = 0; i < 100; ++i) {
            computeData.push_back(float4(
                (f32)i / 100.0f,
                sin((f32)i * 0.1f),
                cos((f32)i * 0.1f),
                1.0f
            ));
        }
        
        // Set compute data and dispatch
        templateNode->setComputeData(computeData);
        templateNode->dispatchCompute(computeData.size());
        
        // Get results (in a real application, you'd do this after the GPU has finished)
        // array<vector4df> results = templateNode->getComputeResults();
    }
    
    // The scene node will automatically be rendered when the scene is rendered
    // It will also automatically clean up when the scene manager is destroyed
}

// Example of creating multiple template nodes with different properties
void createMultipleTemplateNodes(ISceneManager* smgr, IVideoDriver* driver, io::IFileSystem* fs)
{
    // Initialize the material renderer (do this once at startup)
    SnVkPipelineTemplate::initializeMaterialRenderer(driver, fs);
    
    // Create several nodes with different geometries and positions
    for (s32 i = 0; i < 5; ++i) {
        SnVkPipelineTemplateParam params;
        params.initialPosition = vector3df(i * 3.0f - 6.0f, 0, 5);
        params.materialColor = SColor(255, 
            50 + i * 40,   // Red component
            100 + i * 30,  // Green component
            200 - i * 30   // Blue component
        );
        
        SnVkPipelineTemplate* node = new SnVkPipelineTemplate(
            smgr->getRootSceneNode(), smgr, i, params);
        
        // Create different geometry for each node
        switch (i) {
            case 0:
                node->createBox(vector3df(1, 1, 1));
                break;
            case 1:
                node->createSphere(0.8f, 16);
                break;
            case 2:
                node->createCylinder(0.6f, 1.5f, 12);
                break;
            case 3:
                node->createPlane(vector2df(1.5f, 1.5f), 5, 5);
                node->setWaveAmplitude(0.2f);
                break;
            case 4:
                // Custom geometry example
                node->clearGeometry();
                node->addVertex(S3DVertex(vector3df(0, 1, 0), vector3df(0, 1, 0), SColor(255, 255, 0, 0), vector2df(0.5f, 0)));
                node->addVertex(S3DVertex(vector3df(-1, -1, 0), vector3df(0, 0, 1), SColor(255, 0, 255, 0), vector2df(0, 1)));
                node->addVertex(S3DVertex(vector3df(1, -1, 0), vector3df(0, 0, 1), SColor(255, 0, 0, 255), vector2df(1, 1)));
                node->addTriangle(0, 1, 2);
                node->updateGeometry();
                break;
        }
        
        // Set different animation speeds
        node->setAnimationSpeed(0.5f + i * 0.3f);
    }
}

// Example of runtime material property changes
void updateTemplateNodeProperties(SnVkPipelineTemplate* node, f32 time)
{
    if (!node) return;
    
    // Animate material color
    f32 r = (sin(time) + 1.0f) * 0.5f;
    f32 g = (sin(time + 2.0f) + 1.0f) * 0.5f;
    f32 b = (sin(time + 4.0f) + 1.0f) * 0.5f;
    
    node->setMaterialColor(SColor(255, 
        (u32)(r * 255), 
        (u32)(g * 255), 
        (u32)(b * 255)));
    
    // Animate light direction
    vector3df lightDir(
        sin(time * 0.7f),
        -1.0f,
        cos(time * 0.7f)
    );
    node->setLightDirection(lightDir);
    
    // Animate wave properties
    node->setWaveAmplitude(0.1f + 0.1f * sin(time * 2.0f));
    node->setWaveFrequency(1.0f + 0.5f * cos(time * 1.5f));
}
