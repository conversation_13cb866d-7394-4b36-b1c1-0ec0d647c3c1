﻿  MrCsParticle.cpp
  VideoFrameProcessor.cpp
  IrrMMD.cpp
  MrSnTemplate.cpp
  MrVkFluid.cpp
  SnVkFluid.cpp
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/sns/MrSnTemplate.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/IrrFw/MrCsParticle.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/sns/SnVkFuild/MrVkFluid.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/sns/SnVkFuild/SnVkFluid.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/sns/SnVkFuild/SnVkFluid.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\sns\SnVkFuild\MrVkFluid.cpp(267,46): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/sns/SnVkFuild/MrVkFluid.cpp')
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(70,64): warning C4267: 'argument': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\VideoFrameProcessor.cpp(104,11): warning C4101: 'res': unreferenced local variable
  (compiling source file '/src/VideoFrameProcessor.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\UaIrrlicht\source\Irrlicht\CNullDriver.h(647,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/sns/SnVkFuild/SnVkFluid.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\VideoHelpers.h(34,9): warning C4099: 'ualib::VideoProcessor': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
      D:\AProj\AppMainLib\src\VideoHelpers.h(34,9):
      see declaration of 'ualib::VideoProcessor'
  
D:\AProj\AppMainLib\src\irrmmd\IrrMMD.cpp(497,44): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\IrrMMD.cpp(1010,3): warning C4065: switch statement contains 'default' but no 'case' labels
  (compiling source file '/src/irrmmd/IrrMMD.cpp')
  
