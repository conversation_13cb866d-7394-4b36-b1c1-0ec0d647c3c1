﻿#include "VkFxBase.h"

//#include <EQVisual.h>

#if USE_PARSER
#include "SOParser.h"
#endif

VkFxUtil::VkFxBase::VkFxBase(VkDevice device)
	:Device(device)
{

}

VkFxUtil::VkFxBase::~VkFxBase()
{
	if (mVertexShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mVertexShader,nullptr);
	if (mGeometryShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mGeometryShader,nullptr);
	if (mPixelShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mPixelShader, nullptr);
	if (mComputeShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mComputeShader, nullptr);	
	if (mPipeline != VK_NULL_HANDLE)  vkDestroyPipeline(<PERSON><PERSON>, mPipeline, nullptr);
	if (mOwnLayoutObject)
	{
		if (mDescSetLayout != VK_NULL_HANDLE) vkDestroyDescriptorSetLayout(Device, mDescSetLayout, nullptr);
		//mDescSet: Descriptor sets allocated from a given pool do not need to be freed before destroying that descriptor pool.
		if (mDescPool != VK_NULL_HANDLE) vkDestroyDescriptorPool(Device, mDescPool, nullptr);
		if (mPplLayout != VK_NULL_HANDLE) vkDestroyPipelineLayout(Device, mPplLayout, nullptr);
	}
}

void VkFxUtil::VkFxBase::createPipelineLayoutAndDescriptorSet(
	const VkDescriptorSetLayoutCreateInfo& layoutCI,
 	const VkDescriptorPoolCreateInfo& poolCI, 
	uint32_t descSetCount
)
{
	VK_CHECK_RESULT(vkCreateDescriptorSetLayout(Device, &layoutCI, nullptr, &mDescSetLayout));
	VK_CHECK_RESULT(vkCreateDescriptorPool(Device, &poolCI, nullptr, &mDescPool));
	
	VkPipelineLayoutCreateInfo pPipelineLayoutCreateInfo = vks::initializers::pipelineLayoutCreateInfo(&mDescSetLayout, 1);
	VK_CHECK_RESULT(vkCreatePipelineLayout(Device, &pPipelineLayoutCreateInfo, nullptr, &mPplLayout));

	VkDescriptorSetAllocateInfo ai;
	ai.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
	ai.pNext = nullptr;
	ai.descriptorPool = mDescPool;
	ai.descriptorSetCount = 1;
	ai.pSetLayouts = &mDescSetLayout;
	mDescSets.resize(descSetCount);
	for (uint32_t i = 0; i < descSetCount; i++)
	{
		VkResult res = vkAllocateDescriptorSets(Device, &ai, &mDescSets[i]);
		if (res != VK_SUCCESS)
		{
			//VK_ERROR_OUT_OF_POOL_MEMORY
			assert(0);
			mDescSets.clear();
		}
	}
	mOwnLayoutObject = true;
}
void VkFxUtil::VkFxBase::allocDs(uint32_t c, VkDescriptorSet *ds)
{
	VkDescriptorSetAllocateInfo ai;
	ai.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
	ai.pNext = nullptr;
	ai.descriptorPool = mDescPool;
	ai.descriptorSetCount = 1;
	ai.pSetLayouts = &mDescSetLayout;
	for (uint32_t i = 0; i < c; i++)
	{
		VkResult res = vkAllocateDescriptorSets(Device, &ai, ds+i);
		if (res != VK_SUCCESS)
		{
			//VK_ERROR_OUT_OF_POOL_MEMORY
		 
			throw "VK_ERROR_OUT_OF_POOL_MEMORY, allocDs not enough VkDescriptorPoolSize";
			mDescSets.clear();
		}
	}

}


void VkFxUtil::VkFxBase::copyLayoutFrom(VkFxBase* other)
{
	mDescSetLayout = other->mDescSetLayout;
	mDescPool = other->mDescPool;
	mPplLayout = other->mPplLayout;
}


// Vulkan loads it's shaders from an immediate binary representation called SPIR-V
// Shaders are compiled offline from e.g. GLSL using the reference glslang compiler
// This function loads such a shader from a binary file and returns a shader module structure
VkShaderModule VkFxUtil::VkFxBase::createSPIRVShader(const ShaderBytecode & code)
{


	if (code.code)
	{
		// Create a new shader module that will be used for pipeline creation
		VkShaderModuleCreateInfo moduleCreateInfo{};
		moduleCreateInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
		moduleCreateInfo.codeSize = code.length;
		assert(code.length % 4  == 0);
		moduleCreateInfo.pCode = (uint32_t*)code.code;

		VkShaderModule shaderModule;
		VK_CHECK_RESULT(vkCreateShaderModule(Device, &moduleCreateInfo, NULL, &shaderModule));


		return shaderModule;
	}
	else
	{
		assert(0);
		return VK_NULL_HANDLE;
	}
}

void VkFxUtil::VkFxBase::createVS(const ShaderBytecode & code,const char* name)
{
	assert(mStageCount+1 < VKR_MAX_STAGES);
	auto &stage = mStages[mStageCount];
	// Vertex shader
	stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
	// Set pipeline stage for this shader
	stage.stage = VK_SHADER_STAGE_VERTEX_BIT;
	// Load binary SPIR-V shader
	stage.module = mVertexShader = createSPIRVShader(code);
	// Main entry point for the shader
	stage.pName = name;
	assert(mStages[mStageCount].module != VK_NULL_HANDLE);
	mIdVS = mStageCount; mStageCount++;

	mCompiledCodeWithSignature = &code;
}
void VkFxUtil::VkFxBase::createGS(const ShaderBytecode & code, const char* name)
{
	assert(mStageCount < VKR_MAX_STAGES);
	auto &stage = mStages[mStageCount];
	//  shader
	stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
	// Set pipeline stage for this shader
	stage.stage = VK_SHADER_STAGE_GEOMETRY_BIT;
	// Load binary SPIR-V shader
	stage.module = mGeometryShader = createSPIRVShader(code);
	// Main entry point for the shader
	stage.pName = name;
	assert(mStages[mStageCount].module != VK_NULL_HANDLE);
	mIdGS = mStageCount; mStageCount++;
}

void VkFxUtil::VkFxBase::createPS(const ShaderBytecode & code, const char* name)
{
	if (mIdPS < 0)
	{
		assert(mStageCount < VKR_MAX_STAGES);
		auto& stage = mStages[mStageCount];
		//  shader
		stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
		// Set pipeline stage for this shader
		stage.stage = VK_SHADER_STAGE_FRAGMENT_BIT;
		// Load binary SPIR-V shader
		stage.module = mPixelShader = createSPIRVShader(code);
		// Main entry point for the shader
		stage.pName = name;
		assert(mStages[mStageCount].module != VK_NULL_HANDLE);
		mIdPS = mStageCount; mStageCount++;
	}
	else //recreate
	{
		auto& stage = mStages[mIdPS];
		assert(stage.module == mPixelShader);
		if (mPixelShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mPixelShader, nullptr);
		stage.module = mPixelShader = createSPIRVShader(code);
		stage.pName = name;
		assert(mStages[mIdPS].module != VK_NULL_HANDLE);
		
	}

}

void VkFxUtil::VkFxBase::createPS(const char* fName, const char* name)
{
	if (mIdPS < 0)
	{
		assert(mStageCount < VKR_MAX_STAGES);
		auto& stage = mStages[mStageCount];
		//  shader
		stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
		// Set pipeline stage for this shader
		stage = loadVkShader(Device, fName, VK_SHADER_STAGE_FRAGMENT_BIT);
		// Load binary SPIR-V shader
		mPixelShader = stage.module;
		assert(stage.module != VK_NULL_HANDLE);
		// Main entry point for the shader
		stage.pName = name;
		assert(mStages[mStageCount].module != VK_NULL_HANDLE);
		mIdPS = mStageCount; mStageCount++;
	}
	else //recreate
	{
		throw;//to copy from createPS(const ShaderBytecode & code, const char* name)
	}

}
void VkFxUtil::VkFxBase::recreatePS(const char* fName)
{
	assert(mIdPS >= 0);
	if (mIdPS >= 0)
	{
		if (mIdPS >= mStageCount) throw;
		auto& stage = mStages[mIdPS];
		if (mPixelShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mPixelShader, nullptr);
		stage = loadVkShader(Device, fName, VK_SHADER_STAGE_FRAGMENT_BIT);
		mPixelShader = stage.module;
		assert(stage.module != VK_NULL_HANDLE);
	}
}
void VkFxUtil::VkFxBase::recreateVS(const char* fName)
{
	assert(mIdVS >= 0);
	if (mIdVS >= 0)
	{
		if (mIdVS >= mStageCount) throw;
		auto& stage = mStages[mIdVS];
		if (mVertexShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mVertexShader, nullptr);
		stage = loadVkShader(Device, fName, VK_SHADER_STAGE_VERTEX_BIT);
		mVertexShader = stage.module;
		assert(stage.module != VK_NULL_HANDLE);
}
}
void VkFxUtil::VkFxBase::recreateCS(const char* fName)
{
	assert(mIdCS >= 0);
	if (mIdCS >= 0)
	{
		if (mIdCS >= mStageCount) throw;
		auto& stage = mStages[mIdCS];
		if (mComputeShader != VK_NULL_HANDLE) vkDestroyShaderModule(Device, mComputeShader, nullptr);
		stage = loadVkShader(Device, fName, VK_SHADER_STAGE_COMPUTE_BIT);
		mComputeShader = stage.module;
		assert(stage.module != VK_NULL_HANDLE);
	}
	}



void VkFxUtil::VkFxBase::createCS(const ShaderBytecode& code, const char* name)
{
	assert(mStageCount == 0);
	auto& stage = mStages[mStageCount];
	//  shader
	stage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
	// Set pipeline stage for this shader
	stage.stage = VK_SHADER_STAGE_COMPUTE_BIT;
	// Load binary SPIR-V shader
	stage.module = mComputeShader = createSPIRVShader(code);
	// Main entry point for the shader
	stage.pName = name;
	assert(mStages[mStageCount].module != VK_NULL_HANDLE);
	mIdCS = mStageCount++;

}

void VkFxUtil::VkFxBase::applyShaders(VkGraphicsPipelineCreateInfo* pd)
{
	assert(mStageCount <= VKR_MAX_STAGES);
	//EQVisual::StartCPUCounter();
	pd->stageCount=mStageCount;
	pd->pStages = mStages;


	//DP(("cpu %d", EQVisual::GetCPUCounter()));
	//DP(("cpu %f", EQVisual::GetCPUPastTime()));
}
void VkFxUtil::VkFxBase::applyPipelineLayout(VkGraphicsPipelineCreateInfo* pd)
{

	pd->layout = this->mPplLayout;

}

void VkFxUtil::VkFxBase::applyComputeShader(VkComputePipelineCreateInfo* pd)
{
	assert(mStageCount == 1);

	pd->stage = mStages[0];

}


VkPipelineShaderStageCreateInfo VkFxUtil::loadVkShader(VkDevice device, std::string fileName, VkShaderStageFlagBits stage)
{

	VkPipelineShaderStageCreateInfo shaderStage = {};
	shaderStage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
	shaderStage.stage = stage;
#if defined(VK_USE_PLATFORM_ANDROID_KHR)
	DP(("g_assetManager %p", g_assetManager));
	shaderStage.module = vks::tools::loadShader(g_assetManager, fileName.c_str(), device);
#else
	shaderStage.module = vks::tools::loadShader(fileName.c_str(), device);
#endif
	shaderStage.pName = "main"; // todo : make param
	assert(shaderStage.module != VK_NULL_HANDLE);
	return shaderStage;
}

VkPipeline VkFxUtil::VkFxBase::createComputePipeline(VkPipelineCache cache)
{
	if (mPipeline != VK_NULL_HANDLE)
		throw "hadPipeline";
	VkComputePipelineCreateInfo computePipelineCreateInfo =
			vks::initializers::computePipelineCreateInfo(getPipelineLayout(), 0);

	computePipelineCreateInfo.stage = mStages[0];
	VK_CHECK_RESULT(vkCreateComputePipelines(Device, cache, 1, &computePipelineCreateInfo, nullptr, &mPipeline));
	return mPipeline;

}

void VkFxUtil::VkFxBase::destoryComputePipeLine()
{
	vkDestroyPipeline(Device, mPipeline, nullptr);
	mPipeline = VK_NULL_HANDLE;
}
