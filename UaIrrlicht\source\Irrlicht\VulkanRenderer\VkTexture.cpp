// Copyright (C) 2002-2009 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h

#include "IrrCompileConfig.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#define NO_SSE
#define _IRR_DONT_DO_MEMORY_DEBUGGING_HERE
#include "VkTexture.h"
#include "VkDriver.h"
#include "vulkanRenderPass.h"
//#include "VkVertexDeclaration.h"
#include "Helpers/UpUtils.h"

#include "SIrrCreationParameters.h"
#if USE_DIRECTXTEX
#include "DirectXTexSVN/DirectXTex/DirectXTex.h"
#endif
#include <stdio.h>
#include "os.h"

#include "CImage.h"
#include "CColorConverter.h"

void SwapRBinBuffer(char* pb, uint32_t w, uint32_t h, uint32_t pitch);
namespace irr
{
	using namespace core;
	namespace video
	{
		VkFormat VK_MAKE_SRGB(VkFormat format)
		{
			switch (format)
			{
			//case TEX_FORMAT_RGBA8_TYPELESS:
			case VK_FORMAT_R8G8B8A8_UNORM:
			case VK_FORMAT_R8G8B8A8_UINT:
			case VK_FORMAT_R8G8B8A8_SNORM:
			case VK_FORMAT_R8G8B8A8_SINT:
				return VK_FORMAT_R8G8B8A8_UNORM;
			//case TEX_FORMAT_BGRA8_TYPELESS:
			case VK_FORMAT_B8G8R8A8_UNORM:
				return VK_FORMAT_B8G8R8A8_SRGB;


			//case TEX_FORMAT_BC1_TYPELESS:
			case VK_FORMAT_BC1_RGBA_UNORM_BLOCK:
				return VK_FORMAT_BC1_RGBA_SRGB_BLOCK;
			//case TEX_FORMAT_BC2_TYPELESS:
			case VK_FORMAT_BC2_UNORM_BLOCK:
				return VK_FORMAT_BC2_SRGB_BLOCK;
			//case TEX_FORMAT_BC3_TYPELESS:
			case VK_FORMAT_BC3_UNORM_BLOCK:
				return VK_FORMAT_BC3_SRGB_BLOCK;

			};

			return format;
		}

		void VkTexture::InitVars(VkDriver* driver)
		{
			//DP(("TEX+ %p",this));
			Device = driver->Device;
			ColorFormat = ECF_UNKNOWN;
			_HasMipMaps = false;
			_HardwareMipMaps = false;
			//_IsShared = false;

		}

		//! constructor
		VkTexture::VkTexture(IImage* image, VkDriver* driver,
			u32 flags, const io::path& name, u32 arraySlices, void* mipmapData)
			: ITexture(name,driver)

			, Driver(driver)
			, DepthSurface(0)
			, TextureSize(0, 0)
			, ImageSize(0, 0)
			, Pitch(0)
			, SrcPitch(0)
			, MipLevelLocked(0)
			, ArraySliceLocked(0)
			, NumberOfArraySlices(arraySlices)
			, SampleCount(1)
			, SampleQuality(0)
			, _IsRenderTarget(false)

		{
			assert(stagingMemory == VK_NULL_HANDLE);
			InitVars(driver);

#ifdef _DEBUG 
			_ForceTransferSrc = name.find("<TsfSrcDbg>") >= 0;
			setDebugName("VkTexture");

#endif
#if HAS_ARCORE_SHARETEX
			if (name.find("<EHB>") >=0)
			{
				_IsSystemShared =true;
				//useExternalFormat=true;
				sscanf(name.c_strA(),"<EHB> %llu",&aHardBuf);
				//aHardBuf = (AHardwareBuffer*)addr;
			}
#endif
			_HasMipMaps = Driver->getTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS) | (name.find("<GenMip>") >= 0);
			if (Driver->getTextureCreationFlag(video::ETCF_VULKAN_FORCE_SRC))
				_ForceTransferSrc = true;
			if (image)
			{
				
				if (createTexture(flags, image))
				{
					//if (copyTexture(image) && driver->getTextureCreationFlag(ETCF_CREATE_MIP_MAPS))
					//	regenerateMipMapLevels(mipmapData);
					//if (name.find("<NoImg>") < 0)
					if (copyFromImage(image,0))
						regenerateMipMapLevels(mipmapData);
					else {
						IRRLOG(("Create IrrTexture Failed!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!XXXXXXX", ELL_WARNING));
					}
				}
				//else			IRRLOG(("Could not create DIRECT3D11 Texture.", ELL_WARNING));
			}
		}

		VkTexture::VkTexture(const std::vector<IImage*>& images, VkDriver* driver,
			u32 flags, const io::path& name)
			: ITexture(name,driver)

			, Driver(driver)
			, DepthSurface(0)
			, TextureSize(0, 0)
			, ImageSize(0, 0)
			, Pitch(0)
			, SrcPitch(0)
			, MipLevelLocked(0)
			, ArraySliceLocked(0)
			, NumberOfArraySlices(images.size())
			, SampleCount(1)
			, SampleQuality(0)
			, _IsRenderTarget(false)

		{
			assert(stagingMemory == VK_NULL_HANDLE);
			InitVars(driver);


#ifdef _DEBUG
			setDebugName("VkTexture");
#endif
			_HasMipMaps = Driver->getTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS) | (name.find("<GenMip>") >= 0);
			isCube = name.find("<CubeMap>") >= 0;
			assert(NumberOfArraySlices<=Driver->deviceProperties.limits.maxImageArrayLayers);
			if (NumberOfArraySlices>Driver->deviceProperties.limits.maxImageArrayLayers) NumberOfArraySlices=Driver->deviceProperties.limits.maxImageArrayLayers;

			if (images[0])
			{
				if (createTexture(flags, images[0]))
				{
					
					uint8_t* ptr = (uint8_t*)lock();
					assert(ptr);
					if (ptr)
					{
						u32 layerPitch = Pitch*TextureSize.Height;
						for (int i = 0; i < NumberOfArraySlices; i++)
						{
							//image->copyToScaling(ptr, TextureSize.Width, TextureSize.Height, ColorFormat, MappedPitch);
							memcpy(ptr + layerPitch*i, images[i]->lock(), images[i]->getImageDataSizeInBytes());
							images[i]->unlock();
						}
						unlock();

					}
					
				}

			}
		}


		//! rendertarget constructor
		VkTexture::VkTexture(VkDriver* driver, const core::dimension2d<u32>& size,
			const io::path& name, const ECOLOR_FORMAT format, u32 arraySlices,
			u32 sampleCount, u32 sampleQuality)
			: ITexture(name,driver)

			, Driver(driver)
			, DepthSurface(0)
			, TextureSize(size)
			, ImageSize(size)
			, Pitch(0)
			, SrcPitch(0)
			, MipLevelLocked(0)
			, ArraySliceLocked(0)
			, NumberOfArraySlices(arraySlices)
			, SampleCount(sampleCount)
			, SampleQuality(sampleQuality)
			, _IsRenderTarget(true)
			
		{
			InitVars(driver);
			ColorFormat = format;

#ifdef _DEBUG
			setDebugName("VkTexture");
#endif
			//_IsShared = false;// g_IrrlichtCreateinParameters.bUseSharedTex && texName.contains("|guiRT");
			_HasMipMaps = /*!_IsShared &&*/ Driver->getTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS);


			_UsageStoreImage = name.find("<STO>") >= 0;
			_ForceRGBA = name.find("<RGBA>") >= 0;
			_isDepthRT =  name.find("<DepthRT>") >= 0;
			//_IsSystemShared = name.find("<Shared>") >=0;
#if HAS_ARCORE_SHARETEX
			if (name.find("<EHB>") >=0)
			{
				_IsSystemShared =true;
				//useExternalFormat=true;
				sscanf(name.c_strA(),"<EHB> %llu",&aHardBuf);
				//aHardBuf = (AHardwareBuffer*)addr;
			}
#endif
			//_ForceR8 = name.find("<R8>") >= 0;
			if (_UsageStoreImage || name.find("<NoSwapRB>") >= 0) ExtFlags |= EF_NO_SWAP_RB;
			createTexture(0,nullptr);

		}

		//! 3D texture constructor
		VkTexture::VkTexture(VkDriver* driver, const core::vector3d<u32>& size3d,
			const io::path& name, const ECOLOR_FORMAT format, u32 sampleCount, u32 sampleQuality)
			: ITexture(name, driver)
			, Driver(driver)
			, DepthSurface(0)
			, TextureSize(size3d.x, size3d.y)  // 2D compatibility
			, ImageSize(size3d.x, size3d.y)
			, TextureSize3D(size3d)
			, Pitch(0)
			, SrcPitch(0)
			, MipLevelLocked(0)
			, ArraySliceLocked(0)
			, NumberOfArraySlices(1)  // 3D textures don't use array layers
			, SampleCount(sampleCount)
			, SampleQuality(sampleQuality)
			, _IsRenderTarget(true)
			, _Is3DTexture(true)
		{
			InitVars(driver);
			ColorFormat = format;
			TextureDimension = VK_IMAGE_TYPE_3D;

#ifdef _DEBUG
			setDebugName("VkTexture3D");
#endif
			
			_HasMipMaps = Driver->getTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS);
			_UsageStoreImage = name.find("<STO>") >= 0;
			_ForceRGBA = name.find("<RGBA>") >= 0;
			_isDepthRT = name.find("<DepthRT>") >= 0;
			
			if (_UsageStoreImage || name.find("<NoSwapRB>") >= 0) ExtFlags |= EF_NO_SWAP_RB;
			
			DP(("Creating 3D texture: %ux%ux%u, format: %d\n", 
				TextureSize3D.x, TextureSize3D.y, TextureSize3D.z, ColorFormat));
			
			createTexture(0, nullptr);
		}

		//! 3D texture constructor (non-render target)
		VkTexture::VkTexture(VkDriver* driver, const core::vector3d<u32>& size3d,
			const io::path& name, const ECOLOR_FORMAT format, bool isRenderTarget, u32 sampleCount, u32 sampleQuality)
			: ITexture(name, driver)
			, Driver(driver)
			, DepthSurface(0)
			, TextureSize(size3d.x, size3d.y)  // 2D compatibility
			, ImageSize(size3d.x, size3d.y)
			, TextureSize3D(size3d)
			, Pitch(0)
			, SrcPitch(0)
			, MipLevelLocked(0)
			, ArraySliceLocked(0)
			, NumberOfArraySlices(1)  // 3D textures don't use array layers
			, SampleCount(sampleCount)
			, SampleQuality(sampleQuality)
			, _IsRenderTarget(isRenderTarget)
			, _Is3DTexture(true)
		{
			InitVars(driver);
			ColorFormat = format;
			TextureDimension = VK_IMAGE_TYPE_3D;

#ifdef _DEBUG
			setDebugName("VkTexture3D");
#endif
			
			_HasMipMaps = Driver->getTextureCreationFlag(video::ETCF_CREATE_MIP_MAPS);
			_UsageStoreImage = name.find("<STO>") >= 0;
			_ForceRGBA = name.find("<RGBA>") >= 0;
			_isDepthRT = name.find("<DepthRT>") >= 0;
			
			if (_UsageStoreImage || name.find("<NoSwapRB>") >= 0) ExtFlags |= EF_NO_SWAP_RB;
			
			DP(("Creating 3D texture (non-RT): %ux%ux%u, format: %d\n", 
				TextureSize3D.x, TextureSize3D.y, TextureSize3D.z, ColorFormat));
			
			createTexture(0, nullptr);
		}

		//! destructor
		VkTexture::~VkTexture()
		{
			//DP(("TEX- %p | %d,%d", this, TextureSize.Width, TextureSize.Height));
			ReleaseTextureObjects();
		}

		ITexture* VkTexture::Clone(const io::path& name)
		{
			ITexture* tex = Driver->addTexture(getSize(), name);

			copyTo(tex);
			return tex;

		}

		bool VkTexture::copyTo(ITexture* pTex, bool convertFormatIfNeed)
		{
			assert(getSize() == pTex->getSize());
			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);
			VkImage srcImage = Texture;
			VkImage dstImage = VKTEX(pTex)->getTextureResource();

			// Transition destination image to transfer destination layout
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				dstImage,
				0,
				VK_ACCESS_TRANSFER_WRITE_BIT,
				VK_IMAGE_LAYOUT_UNDEFINED,
				VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			// Transition swapchain image from present to transfer source layout
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				srcImage,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_ACCESS_TRANSFER_READ_BIT,
				ImageLayout,
				VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			if (convertFormatIfNeed && ColorFormat!=pTex->getColorFormat())
			{
				VkImageSubresourceRange mipSubRange = {};
				mipSubRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				mipSubRange.baseMipLevel = 0;
				mipSubRange.levelCount = 1;
				mipSubRange.layerCount = 1;
				//for (int32_t i = 1; i < NumberOfMipLevels; i++)
				{
					VkImageBlit imageBlit{};

					// Source
					imageBlit.srcSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					imageBlit.srcSubresource.layerCount = 1;
					imageBlit.srcSubresource.mipLevel = 0;
					imageBlit.srcOffsets[1].x = int32_t(TextureSize.Width );
					imageBlit.srcOffsets[1].y = int32_t(TextureSize.Height );
					imageBlit.srcOffsets[1].z = 1;

					// Destination
					imageBlit.dstSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					imageBlit.dstSubresource.layerCount = 1;
					imageBlit.dstSubresource.mipLevel = 0;
					imageBlit.dstOffsets[1].x = int32_t(TextureSize.Width );
					imageBlit.dstOffsets[1].y = int32_t(TextureSize.Height );
					imageBlit.dstOffsets[1].z = 1;

					VkImageSubresourceRange mipSubRange = {};
					mipSubRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					mipSubRange.baseMipLevel = 0;
					mipSubRange.levelCount = 1;
					mipSubRange.layerCount = 1;

					// Blit from previous level
					vkCmdBlitImage(
						copyCmd,
						srcImage,	VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
						dstImage,	VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
						1,
						&imageBlit,
						VK_FILTER_NEAREST);  //size == , or VK_FILTER_LINEAR
				}
			}
			else
			{			
				// Otherwise use image copy (requires us to manually flip components)
				VkImageCopy imageCopyRegion{};
				imageCopyRegion.srcSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				imageCopyRegion.srcSubresource.layerCount = 1;
				imageCopyRegion.dstSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				imageCopyRegion.dstSubresource.layerCount = 1;
				imageCopyRegion.extent.width = TextureSize.Width;
				imageCopyRegion.extent.height = TextureSize.Height;
				imageCopyRegion.extent.depth = 1;

				// Issue the copy command
				vkCmdCopyImage(
					copyCmd,
					srcImage, VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					dstImage, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					1,
					&imageCopyRegion);
			}

			// Transition destination image to general layout, which is the required layout for mapping the image memory later on
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				dstImage,
				VK_ACCESS_TRANSFER_WRITE_BIT,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
				VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			//ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
			// Transition back the swap chain image after the blit is done
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				srcImage,
				VK_ACCESS_TRANSFER_READ_BIT,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
				ImageLayout,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			return true;
		}

		bool VkTexture::copyRectTo(ITexture* pTex, core::position2di posTgt, int idx, core::recti* rcSrc )
		{ 
			core::recti rc = rcSrc ? *rcSrc : core::recti(0, 0, TextureSize.Width, TextureSize.Height);
			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);
			VkImage srcImage = Texture;
			VkImage dstImage = VKTEX(pTex)->getTextureResource();

			// Transition destination image to transfer destination layout
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				dstImage,
				0,
				VK_ACCESS_TRANSFER_WRITE_BIT,
				VK_IMAGE_LAYOUT_UNDEFINED,
				VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			// Transition swapchain image from present to transfer source layout
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				srcImage,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_ACCESS_TRANSFER_READ_BIT,
				ImageLayout,
				VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			{
				// Otherwise use image copy (requires us to manually flip components)
				VkImageCopy imageCopyRegion{};
				imageCopyRegion.srcSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				imageCopyRegion.srcSubresource.layerCount = 1; 
				imageCopyRegion.srcOffset = {rc.UpperLeftCorner.X, rc.UpperLeftCorner.Y, 0};
				imageCopyRegion.dstSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				imageCopyRegion.dstSubresource.layerCount = 1;
				imageCopyRegion.dstSubresource.baseArrayLayer = idx;
				imageCopyRegion.dstOffset = { posTgt.X, posTgt.Y, 0 };
				imageCopyRegion.extent.width = rc.getWidth();
				imageCopyRegion.extent.height = rc.getHeight();
				imageCopyRegion.extent.depth = 1;

				// Issue the copy command
				vkCmdCopyImage(
					copyCmd,
					srcImage, VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					dstImage, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					1,
					&imageCopyRegion);
			}

			// Transition destination image to general layout, which is the required layout for mapping the image memory later on
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				dstImage,
				VK_ACCESS_TRANSFER_WRITE_BIT,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
				VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
			// Transition back the swap chain image after the blit is done
			vks::tools::insertImageMemoryBarrier(
				copyCmd,
				srcImage,
				VK_ACCESS_TRANSFER_READ_BIT,
				VK_ACCESS_MEMORY_READ_BIT,
				VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
				ImageLayout,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VK_PIPELINE_STAGE_TRANSFER_BIT,
				VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			return true;
		}



		//! Returns original size of the texture.
		const core::dimension2d<u32>& VkTexture::getOriginalSize() const
		{
			assert(ImageSize.Width > 0);
			return ImageSize;
		}

		//! Regenerates the mip map levels of the texture. Useful after locking and
		//! modifying the texture
		void VkTexture::regenerateMipMapLevels(void* mipmapData)
		{
			
			//todo throw;
			if (!(_HardwareMipMaps && _HasMipMaps)) return;

			// Change texture image layout to shader read after all mip levels have been copied
			
			{
				VkCommandPool pool;
				VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);


				VkImageSubresourceRange mipSubRange = {};
				mipSubRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				mipSubRange.baseMipLevel = 0;
				mipSubRange.levelCount = NumberOfMipLevels;
				mipSubRange.layerCount = 1;

				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					ImageLayout,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					mipSubRange,
					VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
					VK_PIPELINE_STAGE_TRANSFER_BIT);


				for (int32_t i = 1; i < NumberOfMipLevels; i++)
				{
					VkImageBlit imageBlit{};

					// Source
					imageBlit.srcSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					imageBlit.srcSubresource.layerCount = 1;
					imageBlit.srcSubresource.mipLevel = i - 1;
					imageBlit.srcOffsets[1].x = int32_t(TextureSize.Width >> (i - 1));
					imageBlit.srcOffsets[1].y = int32_t(TextureSize.Height >> (i - 1));
					imageBlit.srcOffsets[1].z = 1;

					// Destination
					imageBlit.dstSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					imageBlit.dstSubresource.layerCount = 1;
					imageBlit.dstSubresource.mipLevel = i;
					imageBlit.dstOffsets[1].x = int32_t(TextureSize.Width >> i);
					imageBlit.dstOffsets[1].y = int32_t(TextureSize.Height >> i);
					imageBlit.dstOffsets[1].z = 1;

					VkImageSubresourceRange mipSubRange = {};
					mipSubRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
					mipSubRange.baseMipLevel = i;
					mipSubRange.levelCount = 1;
					mipSubRange.layerCount = 1;

					// Transiton current mip level to transfer dest
					vks::tools::setImageLayout(
						copyCmd,
						Texture,
						VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
						VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
						mipSubRange,
						VK_PIPELINE_STAGE_TRANSFER_BIT,
						VK_PIPELINE_STAGE_TRANSFER_BIT);

					// Blit from previous level
					vkCmdBlitImage(
						copyCmd,
						Texture,
						VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
						Texture,
						VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
						1,
						&imageBlit,
						VK_FILTER_LINEAR);

					// Transiton current mip level to transfer source for read in next iteration
					vks::tools::setImageLayout(
						copyCmd,
						Texture,
						VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
						VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
						mipSubRange,
						VK_PIPELINE_STAGE_TRANSFER_BIT,
						VK_PIPELINE_STAGE_TRANSFER_BIT);
				}

				ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
				VkImageSubresourceRange subresourceRange = {};
				subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
				subresourceRange.baseMipLevel = 0;
				subresourceRange.levelCount = NumberOfMipLevels;
				subresourceRange.layerCount = 1;
				// After the loop, all mip layers are in TRANSFER_SRC layout, so transition all to SHADER_READ

				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					ImageLayout,
					subresourceRange);

				Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			}

		}

		//! returns if it is a render target
		bool VkTexture::isRenderTarget() const
		{
			return _IsRenderTarget;
		}

		void VkTexture::ClearBackBuffer(SColor c) { 			
			
			if (ImageLayout == VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL)
			{
				setVkImageLayout(VK_IMAGE_LAYOUT_GENERAL);
			}

			VkClearColorValue vc; 
			vc.uint32[0] = c.getRed();
			vc.uint32[1] = c.getGreen();
			vc.uint32[2] = c.getBlue();
			vc.uint32[3] = c.getAlpha();
			VkClearAttachment ClearAttachment[1] = {};
			ClearAttachment[0].aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			ClearAttachment[0].colorAttachment = 0;
			ClearAttachment[0].clearValue.color = vc;


			VkImageSubresourceRange subresourceRange = {};
			subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			subresourceRange.baseMipLevel = 0;
			subresourceRange.levelCount = NumberOfMipLevels;
			subresourceRange.layerCount = 1;


			//if (yes)
			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);

			vkCmdClearColorImage(copyCmd, Texture,ImageLayout,&vc,1,&subresourceRange);

			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
		}

		const VkCommandBuffer VkTexture::rtCmdBuf() { return vrp->CmdBuf.cmdBuf; }


		//! creates the hardware texture
		bool VkTexture::createTexture(u32 flags, IImage* image)
		{

			if (!_IsRenderTarget && image)
			{
				ImageSize = image->getDimension();
				SetVkFormat(flags, image);
			}
			else
			{
				if (ColorFormat == ECF_A8R8G8B8 || ColorFormat== ECF_UNKNOWN)
				{
					mFormat =  IRR_SC_BUFFER_DXGI_FORMAT;
						//Driver->SwapChain.colorFormat;//IRR_SC_BUFFER_DXGI_FORMAT;// VK_FORMAT_R8G8B8A8_SNORM;

					if (_ForceRGBA) {
						mFormat = VK_FORMAT_R8G8B8A8_UNORM;
					}
					//if (_ForceR8) mFormat = VK_FORMAT_R8_UNORM;
				}
				else
					mFormat = Driver->getDeviceFormatFromColorFormat(ColorFormat);
			}
			ColorFormat = Driver->getColorFormatFromDeviceFormat(mFormat);

			bSRGB = true;
			if (Driver->getTextureCreationFlag(ETCF_NO_GAMMA_CORRECT))
				bSRGB = false;
			if (bSRGB)
				mFormat = VK_MAKE_SRGB(mFormat);

			// Check hardware support for automatic mipmap support
			if ( 
				Driver->queryFeature(EVDF_MIP_MAP_AUTO_UPDATE))
			{
				u32 support = 0;
				//Driver->Device->CheckFormatSupport(dxformat, &support);
				//if (support && D3D11_FORMAT_SUPPORT_MIP_AUTOGEN)
				//	_HardwareMipMaps = true;
				_HardwareMipMaps = true;
			}
			else
				_HasMipMaps = false;
			
		 	// If array size == 6, force cube texture
			if (NumberOfArraySlices > 1)
			{
				//vks::Texture2DArray *texture = new vks::Texture2DArray();
				//mTexture = texture;
				//texture->loadFromFile();
				_isTextureArray = true;
			}
			else
			{

			}


					TextureSize = ImageSize;
		setPitch(mFormat);
		if (_HasMipMaps) {
			if (_Is3DTexture) {
				NumberOfMipLevels = floor(log2(std::max({TextureSize3D.x, TextureSize3D.y, TextureSize3D.z}))) + 1;
			} else {
				NumberOfMipLevels = floor(log2(std::max(TextureSize.Width, TextureSize.Height))) + 1;
			}
		}
		ImageLayout =  _IsRenderTarget && !_IsSystemShared ? VK_IMAGE_LAYOUT_PREINITIALIZED:	VK_IMAGE_LAYOUT_UNDEFINED;// orig = VK_IMAGE_LAYOUT_UNDEFINED but WIN10NB does not render to rt
		
		// Create optimal tiled target image
		VkImageCreateInfo imageCreateInfo = vks::initializers::imageCreateInfo();
		
		// Determine image type and extent based on texture type
		if (_Is3DTexture) {
			imageCreateInfo.imageType = VK_IMAGE_TYPE_3D;
			imageCreateInfo.extent = { TextureSize3D.x, TextureSize3D.y, TextureSize3D.z };
			imageCreateInfo.arrayLayers = 1;  // 3D textures don't use array layers
		} else {
			imageCreateInfo.imageType = VK_IMAGE_TYPE_2D;
			imageCreateInfo.extent = { TextureSize.Width, TextureSize.Height, 1 };
			imageCreateInfo.arrayLayers = NumberOfArraySlices;
		}
		
		imageCreateInfo.format = useExternalFormat ? VK_FORMAT_UNDEFINED : mFormat;
		imageCreateInfo.mipLevels = NumberOfMipLevels;
		imageCreateInfo.samples = VK_SAMPLE_COUNT_1_BIT;
		imageCreateInfo.tiling = VK_IMAGE_TILING_OPTIMAL;
		imageCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
		imageCreateInfo.initialLayout = ImageLayout; 
		imageCreateInfo.usage =  ImageUsageFlags | VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
 			if (isCube)  				
			imageCreateInfo.flags|=VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT;
			// Ensure that the TRANSFER_DST bit is set for staging
			if (!(imageCreateInfo.usage & VK_IMAGE_USAGE_TRANSFER_DST_BIT))
			{
				imageCreateInfo.usage |= VK_IMAGE_USAGE_TRANSFER_DST_BIT;
			}
			if (_IsRenderTarget && !_Is3DTexture)
				imageCreateInfo.usage |= VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT | VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
			if (_HasMipMaps || _ForceTransferSrc)
				imageCreateInfo.usage |= VK_IMAGE_USAGE_TRANSFER_SRC_BIT;
			if (_UsageStoreImage)
				imageCreateInfo.usage |= VK_IMAGE_USAGE_STORAGE_BIT;


#if HAS_ARCORE_SHARETEX

            VkAndroidHardwareBufferFormatPropertiesANDROID formatInfo = {
                    .sType =
                    VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_FORMAT_PROPERTIES_ANDROID,
                    .pNext = nullptr,
            };
            VkAndroidHardwareBufferPropertiesANDROID properties = {
                    .sType = VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_PROPERTIES_ANDROID,
                    .pNext = &formatInfo,
            };
			if (aHardBuf)
            vkGetAndroidHardwareBufferPropertiesANDROID(Device, aHardBuf, &properties);
            VkExternalFormatANDROID externalFormat{
                    .sType = VK_STRUCTURE_TYPE_EXTERNAL_FORMAT_ANDROID,
                    .pNext = nullptr,
                    .externalFormat = formatInfo.externalFormat,
            };
            VkExternalMemoryImageCreateInfo externalCreateInfo{
                    .sType = VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO,
                    .pNext = useExternalFormat ? &externalFormat : nullptr,
                    .handleTypes =
                    VK_EXTERNAL_MEMORY_HANDLE_TYPE_ANDROID_HARDWARE_BUFFER_BIT_ANDROID,
            };
            if (_IsSystemShared && aHardBuf) {
                imageCreateInfo.pNext=&externalCreateInfo;
				if (useExternalFormat) imageCreateInfo.usage = VK_IMAGE_USAGE_SAMPLED_BIT;
			}



#endif

			VK_CHECK_RESULT(vkCreateImage(Device, &imageCreateInfo, nullptr, &Texture));
			vks::debugmarker::setImageName(Device, Texture, "irrIMAGE");
#if DP_TEX_CREATE
			DP(("CrTex %S  addr=%p", NamedPath.getPath().c_str(),Texture ));
#endif

            VkMemoryRequirements memReqs;
            VkMemoryAllocateInfo memAllocInfo = vks::initializers::memoryAllocateInfo();


#if HAS_ARCORE_SHARETEX
            VkImportAndroidHardwareBufferInfoANDROID androidHardwareBufferInfo{
                    .sType = VK_STRUCTURE_TYPE_IMPORT_ANDROID_HARDWARE_BUFFER_INFO_ANDROID,
                    .pNext = nullptr,
                    .buffer = aHardBuf,
            };
            VkMemoryDedicatedAllocateInfo memoryDAllocateInfo{
                    .sType = VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO,
                    .pNext = &androidHardwareBufferInfo,
                    .image = Texture,
                    .buffer = VK_NULL_HANDLE,
            };

            if (_IsSystemShared && aHardBuf)
            {
                memAllocInfo.pNext=&memoryDAllocateInfo;
                memAllocInfo.allocationSize=properties.allocationSize;
                memAllocInfo.memoryTypeIndex = Driver->mDevice->getMemoryType(properties.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);
                VK_CHECK_RESULT(vkAllocateMemory(Device, &memAllocInfo, nullptr, &TexMemory));
/*                VkBindImageMemoryInfo bindImageInfo;
                bindImageInfo.sType = VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_INFO;
                bindImageInfo.pNext = nullptr;
                bindImageInfo.image = Texture;
                bindImageInfo.memory = TexMemory;
                bindImageInfo.memoryOffset = 0;*/
                //VK_CHECK_RESULT(vkBindImageMemory2KHR(Device, 1, &bindImageInfo));
                VK_CHECK_RESULT(vkBindImageMemory(Device, Texture, TexMemory, 0));
            }
            else
#endif
            {
                vkGetImageMemoryRequirements(Device, Texture, &memReqs);

                memAllocInfo.allocationSize = memReqs.size;

                // Calculate expected size based on texture type
                uint64_t expectedSize;
                if (_Is3DTexture) {
                    expectedSize = (uint64_t)Pitch * TextureSize3D.y * TextureSize3D.z;
                } else {
                    expectedSize = (uint64_t)Pitch * TextureSize.Height * NumberOfArraySlices;
                }
                assert(memReqs.size >= expectedSize);
                memAllocInfo.memoryTypeIndex = Driver->mDevice->getMemoryType(
                        memReqs.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);

                VK_CHECK_RESULT(vkAllocateMemory(Device, &memAllocInfo, nullptr, &TexMemory));
                VK_CHECK_RESULT(vkBindImageMemory(Device, Texture, TexMemory, 0));
            }


#if  DBG_VK_NAMES
			vks::debugmarker::setDeviceMemoryName(Device, TexMemory, "vk:Texture Memory");
#endif

					// Set appropriate image layout based on usage
		if (_UsageStoreImage && _Is3DTexture) {
			// 3D storage images need GENERAL layout for compute shader access
			setVkImageLayout(VK_IMAGE_LAYOUT_GENERAL);
		} else if (_IsRenderTarget) {
			// Traditional 2D render targets
			setVkImageLayout(VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
		}
		//DPWCS((L"Cr.Tex %s %dx%d", NamedPath.getInternalName().c_str(),TextureSize.Width,TextureSize.Height ));
		// create views to bound texture to pipeline
		return createViews();
		}

        bool VkTexture::createViews(bool bCreateRTV)
        {
            if (!Texture)
                return false;

            // Create image view
            VkImageViewCreateInfo viewCreateInfo = {};
            viewCreateInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
            viewCreateInfo.pNext = NULL;
            
            // Determine view type based on texture type
            if (_Is3DTexture) {
                viewCreateInfo.viewType = VK_IMAGE_VIEW_TYPE_3D;
                viewCreateInfo.subresourceRange.layerCount = 1;  // 3D textures use depth, not layers
            } else {
                viewCreateInfo.viewType = isCube ? VK_IMAGE_VIEW_TYPE_CUBE : 
                                          (NumberOfArraySlices > 1 ? VK_IMAGE_VIEW_TYPE_2D_ARRAY : VK_IMAGE_VIEW_TYPE_2D);
                viewCreateInfo.subresourceRange.layerCount = NumberOfArraySlices; //VK_REMAINING_ARRAY_LAYERS
            }
            
            viewCreateInfo.format = useExternalFormat ? VK_FORMAT_UNDEFINED : mFormat;
            viewCreateInfo.components = { VK_COMPONENT_SWIZZLE_R, VK_COMPONENT_SWIZZLE_G, VK_COMPONENT_SWIZZLE_B, VK_COMPONENT_SWIZZLE_A };
            viewCreateInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
            viewCreateInfo.subresourceRange.baseMipLevel = 0;
            viewCreateInfo.subresourceRange.levelCount = NumberOfMipLevels;//VK_REMAINING_MIP_LEVELS
            viewCreateInfo.subresourceRange.baseArrayLayer = 0;
            viewCreateInfo.image = Texture;

            VK_CHECK_RESULT(vkCreateImageView(Device, &viewCreateInfo, nullptr, &SRView));
            //DP(("TexView %p", SRView));

            return true;
        }

#ifndef NO_SSE
#include  <tmmintrin.h> 

		typedef __declspec(align(16)) unsigned char uvec8[16];
		static const uvec8 kShuffleMaskABGRToARGB = {
			2u, 1u, 0u, 3u, 6u, 5u, 4u, 7u, 10u, 9u, 8u, 11u, 14u, 13u, 12u, 15u
		};
#endif

		void* VkTexture::lock(E_TEXTURE_LOCK_MODE mode, u32 mipmapLevel)
		{
			//DP(("TEX Lock+"));
			lastLockMode = mode;
			//if (!_IsRenderTarget && mode != E_TEXTURE_LOCK_MODE::ETLM_WRITE_ONLY)
			//{
			//	//throw;//only support write
			//	DP(("??????????? Check if not only write supported ????????????"));
			//}

			if (!Texture)
				return 0;
			bool needRead = mode == E_TEXTURE_LOCK_MODE::ETLM_READ_ONLY || mode == E_TEXTURE_LOCK_MODE::ETLM_READ_WRITE;


			VkMemoryAllocateInfo memAllocInfo = vks::initializers::memoryAllocateInfo();
			VkMemoryRequirements memReqs;

			// Create a host-visible staging buffer that contains the raw image data

			VkBufferCreateInfo bufferCreateInfo = vks::initializers::bufferCreateInfo();
			if (_Is3DTexture) {
				mBufferSize = Pitch * TextureSize3D.y * TextureSize3D.z;
			} else {
				mBufferSize = Pitch * TextureSize.Height * NumberOfArraySlices;
			}
			bufferCreateInfo.size = mBufferSize;
			// This buffer is used as a transfer source for the buffer copy
			bufferCreateInfo.usage = VK_BUFFER_USAGE_TRANSFER_SRC_BIT | (needRead? VK_BUFFER_USAGE_TRANSFER_DST_BIT :0)	;
			bufferCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

			VK_CHECK_RESULT(vkCreateBuffer(Device, &bufferCreateInfo, nullptr, &stagingBuffer));
#if DBG_VK_NAMES
			vks::debugmarker::setBufferName(Device, stagingBuffer, "vk:Buffer vktexture.Lock");
#endif
			if (((uint64_t)stagingBuffer & 0xFFF) == 0x161)
			{
				//assert(0);
			}
			//DP(("tex stg buf %p=", stagingBuffer));
			// Get memory requirements for the staging buffer (alignment, memory type bits)
			vkGetBufferMemoryRequirements(Device, stagingBuffer, &memReqs);

			memAllocInfo.allocationSize = memReqs.size;
			// Get memory type index for a host visible buffer
			memAllocInfo.memoryTypeIndex = Driver->mDevice->getMemoryType(memReqs.memoryTypeBits, VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT | 
				(needRead? VK_MEMORY_PROPERTY_HOST_CACHED_BIT:0)
			);

			VK_CHECK_RESULT(vkAllocateMemory(Device, &memAllocInfo, nullptr, &stagingMemory));
			VK_CHECK_RESULT(vkBindBufferMemory(Device, stagingBuffer, stagingMemory, 0));

			
			if (needRead)
			{			// Copy texture data into staging buffer
				runVkCopyBufferCmd(false);
			}
			if (mode == ETLM_NONE)
			{
				runTestCmd(false);
			}

			

			uint8_t *data;
			VK_CHECK_RESULT(vkMapMemory(Device, stagingMemory, 0, memReqs.size, 0, (void **)&data));

#if 0
			if (needRead && mFormat != IRR_SC_BUFFER_DXGI_FORMAT && (mFormat == VK_FORMAT_B8G8R8A8_UNORM || mFormat == VK_FORMAT_R8G8B8A8_UNORM))
			{  
				SwapRBinRGBA(data, data, memAllocInfo.allocationSize);//RW this data may be slow on Android, should process after copy
			}
#endif
			//DP(("TEX Lock- %p",data));
			return data;
		}



		//! unlock function
		void VkTexture::unlock()
		{
			//DP(("TEX Unlock+"));
			if (!Texture || !stagingMemory)
			{
				assert(0);
				return;
			}
			vkUnmapMemory(Driver->mDevice->logicalDevice, stagingMemory);

			if (lastLockMode== ETLM_WRITE_ONLY || lastLockMode == ETLM_READ_WRITE)			runVkCopyBufferCmd(true);

			// Clean up staging resources

			vkDestroyBuffer(Device, stagingBuffer, nullptr);
			vkFreeMemory(Device, stagingMemory, nullptr);
			stagingMemory = VK_NULL_HANDLE;
			stagingBuffer = VK_NULL_HANDLE;
			//DP(("TEX Unlock-"));
		}

		void VkTexture::runVkCopyBufferCmd(bool buf2img)
		{
			VkBufferImageCopy bufferCopyRegion = {};
			bufferCopyRegion.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			bufferCopyRegion.imageSubresource.mipLevel = 0;
			bufferCopyRegion.imageSubresource.baseArrayLayer = 0;
			
			// Configure copy region based on texture type
			if (_Is3DTexture) {
				bufferCopyRegion.imageSubresource.layerCount = 1;  // 3D textures use depth, not layers
				bufferCopyRegion.imageExtent.width = TextureSize3D.x;
				bufferCopyRegion.imageExtent.height = TextureSize3D.y;
				bufferCopyRegion.imageExtent.depth = TextureSize3D.z;
			} else {
				bufferCopyRegion.imageSubresource.layerCount = NumberOfArraySlices;
				bufferCopyRegion.imageExtent.width = TextureSize.Width;
				bufferCopyRegion.imageExtent.height = TextureSize.Height;
				bufferCopyRegion.imageExtent.depth = 1;
			}
			bufferCopyRegion.bufferOffset = 0;

			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);



			VkImageSubresourceRange subresourceRange = {};
			subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			subresourceRange.baseMipLevel = 0;
			subresourceRange.levelCount = NumberOfMipLevels; //VK_REMAINING_MIP_LEVELS
			subresourceRange.baseArrayLayer = 0;
			subresourceRange.layerCount = _Is3DTexture ? 1 : NumberOfArraySlices;

			if (buf2img)
			{
				//DP(("B2I 0"));
				// Image barrier for optimal image (target)
				// Optimal image will be used as destination for the copy
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					ImageLayout,
					VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					subresourceRange);
				//ImageLayout = VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL;


				// Copy mip levels from staging buffer
				vkCmdCopyBufferToImage(
					copyCmd,										   //	commandBuffer,
					stagingBuffer,									   //	srcBuffer,
					Texture,										   //	dstImage,
					VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,			   //	dstImageLayout,
					1,												   //	regionCount,
					&bufferCopyRegion								   //	pRegions);
				);
				ImageLayout =  VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
				

				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					ImageLayout,
					subresourceRange);



			}
			else //read from img to buf
			{
				//DP(("I2B 0"));
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					ImageLayout,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					subresourceRange);

				// Copy mip levels from staging buffer
				vkCmdCopyImageToBuffer(
					copyCmd,	
					Texture,//	commandBuffer,
								   //	srcBuffer,
															   //	dstImage,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,			   //	dstImageLayout,
					stagingBuffer,
					1,												   //	regionCount,
					&bufferCopyRegion								   //	pRegions);
				);

				ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
				// Change texture image layout to shader read after all mip levels have been copied
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					ImageLayout,
					subresourceRange);
				
			}


			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			//DP(("IBC -"));
			
		}


		void VkTexture::runTestCmd(bool buf2img)
		{
			VkBufferImageCopy bufferCopyRegion = {};
			bufferCopyRegion.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			bufferCopyRegion.imageSubresource.mipLevel = 0;
			bufferCopyRegion.imageSubresource.baseArrayLayer = 0;
			
			// Configure copy region based on texture type
			if (_Is3DTexture) {
				bufferCopyRegion.imageSubresource.layerCount = 1;  // 3D textures use depth, not layers
				bufferCopyRegion.imageExtent.width = TextureSize3D.x;
				bufferCopyRegion.imageExtent.height = TextureSize3D.y;
				bufferCopyRegion.imageExtent.depth = TextureSize3D.z;
			} else {
				bufferCopyRegion.imageSubresource.layerCount = NumberOfArraySlices;
				bufferCopyRegion.imageExtent.width = TextureSize.Width;
				bufferCopyRegion.imageExtent.height = TextureSize.Height;
				bufferCopyRegion.imageExtent.depth = 1;
			}
			bufferCopyRegion.bufferOffset = 0;

			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);



			VkImageSubresourceRange subresourceRange = {};
			subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			subresourceRange.baseMipLevel = 0;
			subresourceRange.levelCount = NumberOfMipLevels; //VK_REMAINING_MIP_LEVELS
			subresourceRange.baseArrayLayer = 0;
			subresourceRange.layerCount = _Is3DTexture ? 1 : NumberOfArraySlices;

			if (buf2img)
			{
				//DP(("B2I 0"));
				// Image barrier for optimal image (target)
				// Optimal image will be used as destination for the copy
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					ImageLayout,
					VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					subresourceRange);
				//ImageLayout = VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL;




				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
					ImageLayout,
					subresourceRange);



			}
			else //read from img to buf
			{
				//DP(("I2B 0"));
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					ImageLayout,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					subresourceRange);



				ImageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
				// Change texture image layout to shader read after all mip levels have been copied
				vks::tools::setImageLayout(
					copyCmd,
					Texture,
					VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
					ImageLayout,
					subresourceRange);

			}


			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			//DP(("IBC -"));

		}


		//! copies the image to the texture
		bool VkTexture::copyFromImage(IImage* image, u32 layerId)
		{

			SrcPitch = image->getPitch(); //
			void* ptr = lock();
			assert(ptr);
			if (ptr)
			{
				//image->copyToScaling(ptr, TextureSize.Width, TextureSize.Height, ColorFormat, MappedPitch);

				if (Pitch < SrcPitch)
				{
					if (image->isRGBA == Driver->isRGBA() || ColorFormat != ECF_A8R8G8B8) 
					{
						char* pbT = (char*)ptr, *pbS = (char*)image->lock();
						for (int i = 0; i < TextureSize.Height; i++)
						{
							memcpy(pbT, pbS, Pitch);
							pbT += Pitch; pbS += SrcPitch;
						}
					}
					else
					{
						assert(0);//to check
						uu::swapRBinRGBA(image->lock(), ptr, image->getImageDataSizeInBytes());
					}

				}
				else
				{
					if (image->isRGBA == Driver->isRGBA() || ColorFormat != ECF_A8R8G8B8)
						memcpy(ptr, image->lock(), image->getImageDataSizeInBytes());
					else
						uu::swapRBinRGBA(image->lock(), ptr, image->getImageDataSizeInBytes());

						
				}
				image->unlock();
				//if (IRR_SC_BUFFER_DXGI_FORMAT == VK_FORMAT_R8G8B8A8_UNORM)		SwapRBinRGBA(ptr, ptr, TextureSize.Height*MappedPitch);

				unlock();

			}
			else return false;
	

			return true;
		}


		
		void VkTexture::setPitch(VkFormat d3dformat)
		{
			if (_Is3DTexture) {
				Pitch = getVkFormatBitsPerPixel(d3dformat) / 8 * TextureSize3D.x;
			} else {
				Pitch = getVkFormatBitsPerPixel(d3dformat) / 8 * TextureSize.Width;
			}
		}





	

		void VkTexture::SetVkFormat(u32 flags, IImage* image)
		{
			mFormat = IRR_SC_BUFFER_DXGI_FORMAT;

			// Color format for DX 10 driver shall be different that for DX 9
			// - B5G5R5A1 family is deprecated in DXGI, and doesn't exists in DX 10
			// - Irrlicht color format follows DX 9 (alpha first), and DX 10 is alpha last
			switch (getTextureFormatFromFlags(flags))
			{
			case ETCF_ALWAYS_16_BIT:
			case ETCF_ALWAYS_32_BIT:
				mFormat = IRR_SC_BUFFER_DXGI_FORMAT;
				break;
			case ETCF_OPTIMIZED_FOR_QUALITY:
			{
				

				switch (image->getColorFormat())
				{
				case ECF_R16F:
					mFormat = VK_FORMAT_R16_SFLOAT;
					break;

				case ECF_R32F:
					mFormat = VK_FORMAT_R32_SFLOAT;
					break;

				case ECF_G16R16F:
					mFormat = VK_FORMAT_R16G16_SFLOAT;
					break;

				case ECF_G32R32F:
					mFormat = VK_FORMAT_R32G32_SFLOAT;
					break;

				case ECF_A16B16G16R16F:
					mFormat = VK_FORMAT_R16G16B16A16_SFLOAT;
					break;
				case ECF_A32B32G32R32F:
					mFormat = VK_FORMAT_R32G32B32A32_SFLOAT;
					break;

				case ECF_A1R5G5B5:
				case ECF_R5G6B5:
					throw "!sy";//not supported yet
					break;
				case ECF_R8G8B8:
					mFormat = VK_FORMAT_R8G8B8_UNORM;
					break;
				case ECF_R8:					mFormat = VK_FORMAT_R8_UNORM; break;
				case ECF_R8G8:					mFormat = VK_FORMAT_R8G8_UNORM; break;
				case ECF_A8R8G8B8:
				default:
					
					mFormat = IRR_SC_BUFFER_DXGI_FORMAT;
					break;
					break;
				}
			}
			break;
			case ETCF_OPTIMIZED_FOR_SPEED:
				mFormat = IRR_SC_BUFFER_DXGI_FORMAT;
				break;
			default:
				break;
			}
		}

		VkImageLayout VkTexture::setVkImageLayout(VkImageLayout layout)
		{
			VkImageLayout oldLo = ImageLayout;
			VkCommandPool pool;
			VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);

			VkImageSubresourceRange mipSubRange{};
			mipSubRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
			mipSubRange.baseMipLevel = 0;
			mipSubRange.levelCount = 1;
			mipSubRange.layerCount = 1;

			vks::tools::setImageLayout(copyCmd,Texture,oldLo,layout, mipSubRange	);
			ImageLayout = layout;
			Driver->mDevice->flushCommandBuffer(copyCmd,pool, Driver->queueCopy());
			return oldLo;
		}

		void VkTexture::transitionLayoutForStorageAccess()
		{
			if (_Is3DTexture && _UsageStoreImage && ImageLayout != VK_IMAGE_LAYOUT_GENERAL) {
				setVkImageLayout(VK_IMAGE_LAYOUT_GENERAL);
			}
		}

		void VkTexture::transitionLayoutForShaderRead()
		{
			if (_Is3DTexture && ImageLayout != VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL) {
				setVkImageLayout(VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
			}
		}

		VulkanPass* VkTexture::getVRP() {

			if (!vrp) {
				vrp = new VulkanPass();
				vrp->createRenderPass(Driver, this, _isDepthRT);

				//Vrp->cmdBegin(false,true);
				//Vrp->cmdEnd();
			}
			return vrp;
		}


		void VkTexture::ReleaseTextureObjects()
		{
			if (vrp) delete vrp; vrp = nullptr;
			
			if (DepthSurface)
			{
				if (DepthSurface->drop())
					Driver->removeDepthSurface(DepthSurface);
			}

			//vkDestroyImageView(Device,RTView,nullptr);
			vkDestroyImageView(Device,SRView,nullptr);

			vkDestroyImage(Device,Texture,nullptr);
			vkFreeMemory(Device, TexMemory,nullptr);
			if (stagingBuffer)		vkDestroyBuffer(Device, stagingBuffer, nullptr);
			if (stagingMemory)		vkFreeMemory(Device,stagingMemory,nullptr);

			//SAFE_RELEASE(_keyedMutex);
#if HAS_ARCORE_SHARETEX
			if (aHardBuf)
			AHardwareBuffer_release(aHardBuf);
#endif

		}
#if HAS_ARCORE_SHARETEX

		AHardwareBuffer *VkTexture::getSysHardwareBuffer()
		{
			if (aHardBuf) return aHardBuf;

			//VkDevice device, const VkMemoryGetAndroidHardwareBufferInfoANDROID* pInfo, struct AHardwareBuffer** pBuffer
			VkMemoryGetAndroidHardwareBufferInfoANDROID bi{};
			bi.sType = VK_STRUCTURE_TYPE_MEMORY_GET_ANDROID_HARDWARE_BUFFER_INFO_ANDROID;
			bi.memory = TexMemory;
			VkResult vr=vkGetMemoryAndroidHardwareBufferANDROID(Device,&bi,&aHardBuf);
			return aHardBuf;
		}
#endif

	}
}
#endif;