﻿  RenderPrimitive.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibEvtRcv.h(41,45): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\CommonStaticLib\MidiMan.h(91,31): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
D:\AProj\AppMainLib\src\UaLibContext.h(400,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'saba/src/Saba/Model/MMD/joltUtils/RenderPrimitive.cpp')
  
  CommonStaticLib.vcxproj -> D:\AProj\VkUpApp\x64\Debug\CommonStaticLib.lib
