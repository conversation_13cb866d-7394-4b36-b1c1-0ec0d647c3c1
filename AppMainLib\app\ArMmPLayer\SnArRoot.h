#pragma once
#include <ualib.h>

#include "FL_ArEditor.h"
#include "SnArItem.h"
#include "VideoHelpers.h"
#if HAS_ARCORE
#include "d:/aproj/ardatmovie/android/app/src/main/cpp/ARCoreMan.h"
#endif
#include "irrmmd/irrmmd.h"
namespace AppNameSpace {
	class AppMainAMP;
}
namespace vks { class UIOverlay; }

namespace  irr::scene{
 
	struct ArRootParam {
		ualib::VideoProcessor* vp;
		AppNameSpace::AppMainAMP* stage;
		ArItemParam aip; 
	};



	class SnArRoot : public irr::scene::SnArItem
	{
		struct PickNodeInfo {
			SnArItem* sn;
 
			~PickNodeInfo() {
				if (sbNode)
					sbNode->model->saba->drop();
			}

			// Copy assignment operator
			PickNodeInfo& operator=(const PickNodeInfo& other) {
				if (this != &other) {
					if (sbNode)
						sbNode->model->saba->drop();
					sn = other.sn;
					startXY = other.startXY;
					altDownXY = other.altDownXY;
					lastMovePe = other.lastMovePe;
					isMoveStarted = other.isMoveStarted;
					altDown = other.altDown;
					view = other.view;
					sbNode = nullptr;
					if (other.sbNode) {
						sbNode = other.sbNode;
						sbNode->model->saba->grab();
					}
				}
				return *this;
			}

			// Move assignment operator
			PickNodeInfo& operator=(PickNodeInfo&& other) noexcept {
				if (this != &other) {
					if (sbNode)
						sbNode->model->saba->drop();
					sn = other.sn;
					startXY = other.startXY;
					altDownXY = other.altDownXY;
					lastMovePe = other.lastMovePe;
					isMoveStarted = other.isMoveStarted;
					altDown = other.altDown;
					view = other.view;
					sbNode = other.sbNode;
					other.sbNode = nullptr;
				}
				return *this;
			}

			irr::core::vector2df startXY, altDownXY;
			SEvent::SPointInput lastMovePe;
			bool isMoveStarted;
			bool altDown = false;
			int view = 0;

			saba::MMDNode* getSbNode() const { return sbNode; }
			void setSbNode(saba::MMDNode* node) {

				if (sbNode) {
					if (irr::scene::g_mmd->camSbTgtNd == sbNode) 
						irr::scene::g_mmd->camSbTgtNd = nullptr;
					sbNode->model->saba->drop();
				}
				if (node) node->model->saba->grab();
				sbNode = node;				 
			}
		private:
			saba::MMDNode* sbNode{};
		};
	public:
		SnArRoot(irr::scene::ISceneNode* parent, irr::scene::ISceneManager* mgr, int id, ArRootParam pm);
		~SnArRoot();

		void loadSettings();
		void saveSettings();

		virtual void OnRegisterSceneNode() _IRR_OVERRIDE_;
		virtual void render() _IRR_OVERRIDE_  {};
		virtual void OnAnimate(irr::u32 timeMs) _IRR_OVERRIDE_;

		virtual const core::aabbox3d<f32>& getBoundingBox() const
		{
			return Box;
		}
		virtual u32 getMaterialCount() const
		{
			return 1;
		}
		virtual video::SMaterial& getMaterial(u32 i)
		{
			return mtr;
		}
		virtual void updateAbsolutePosition()
		{
 			AbsoluteTransformation =
				Parent->getAbsoluteTransformation() * getRelativeTransformation();
			AbsoluteTransformation.getInverse(invAbs);
 		}
		
		void loadMMD(const saba::PMXFileCreateParam& fcp, uint32_t aiFlag);

		const irr::core::matrix4& getInvAbsMat() { return invAbs; }

		void setFwIdx(int id) { fwIdx = id; };

		void updateMediaFrame(float timeS, float dtime); // BEFORE OnAnimate
		void onArDatLoaded(bool hasData);
		int64_t handleEditorCmd(const irr::SEvent::SCmdInput& ce);
		void onGeatureScaleUpdate(const irr::SEvent::SCmdInput& ce);

		void onPickSetPsTrs(const irr::SEvent::SCmdInput& ce);
		void onPickSetPsRtt(const irr::SEvent::SCmdInput& ce);
		void onPickSetPsScl(const irr::SEvent::SCmdInput& ce);
		void onPickSetPnTrs(const irr::SEvent::SCmdInput& ce, int space);
		void onPickSetPnRtt(const irr::SEvent::SCmdInput& ce, int space);
		void onPickSetPnScl(const irr::SEvent::SCmdInput& ce, int space);

		//MIGUI gizmo
		void OnUpdateUIOverlay(vks::UIOverlay*);
		void switchGizmoMode();
		void switchGizmoGL();
		bool showGizmo = true, gizmoGlobalMode = true;
		int gizmoType = 0;


		void onCceGyroScope();
		void camRttFocus();
		void getFocusPos(irr::core::matrix4& tgt);
		void buildNodeButtonList();
		void onSelectNodeButtonList(int nodeId,int act);

		void onItemUpdated(CsEditorItem* p, irr::scene::ISceneNode* sn, uint64_t flag);
		SnArItem* newArSn(CsEditorItem* p);
		std::string saveItems();

		void loadPmxZipFromDataDir(const std::string fn, const std::string vmd, SnArItem* sn);
		void loadStaticModelFromDataDir(const std::string fn,  SnArItem* sn);
		void loadItems(const char* str, int fromId);
		void clearLoadEIs();
		bool onModelUploaded(IrrMMD* mmd, io::IFileArchive* fa, irr::io::path dir,SnArItem *parentArSn,bool onlyGetInfo, 
			bool allowAllNodePhysics=true
		);
		void addPickNode(int id, PickNodeInfo& pni);
		std::function <void(MMDNode* node)> onSbNodePicked;
		bool onPointerEvent(const irr::SEvent::SPointInput& pe);
		bool pickRelease(const irr::SEvent::SPointInput& pe);
		void pickMapErase(std::map<int, irr::scene::SnArRoot::PickNodeInfo>::iterator& it);
		uint32_t pickPoint(int x, int y,int & view,int range=0,uint32_t *pd=nullptr, glm::vec3 *pos=nullptr);
		void pickGetResult(u32 data, u32& id, u32& vtxId);
		int getSabaNum();
		void setAddRttId();
		void resetPickingNodeAnimationTR(u32 flag);
		bool allowRttCamControl() { return snPickMap.size() == 0; };
		void selectItemOfs(int ofs, uint32_t aiFlag=0x10);
		void selectClosetItemOfs(int ofs, uint32_t aiFlag);
		//1 or -1
		//physics

		
		
		//SnArItem* getPickSn(int pick) { SnArItem* snPick{}; forEachArChild([=, &snPick](irr::scene::SnArItem* sn) {if (sn->getMaterial(0).PickColor == pick) { snPick = sn; }}); return snPick; }
		CsEditorState Cs{};

		irr::scene::SnArItem* curArSn{}, * lastArSn{}, *vtxFwSn{};
		void setCurSn(irr::scene::SnArItem* sn,bool changeCamera=false);
		irr::scene::SnArItem* curChar();
		irr::scene::IrrSaba* curAiSb() { return curAiChar->sb; }
		bool renderring() {
			return pVp->recording;
		};
		IrrMMD* mmd{};

		ISceneNode* cmRoot{}, * cmRootSc{};
		IMeshSceneNode* snPointCloud{};

		irr::core::vector3df CopyPos, CopyRtt, CopyScale;


		std::map<int, PickNodeInfo> snPickMap;
		std::vector<CsNodeButton> nodeBtns, nodeBtnsP, nodeBtnsC;

		void addFakePtrMove();
		void rotateSubPickNodes(float a);

		void connectSbs(bool useLastPickRbOfs,glm::vec3 ofs={});

		void connectConnToPick(int lid,glm::vec3 ofs);

		void connectConns(int lid, int lid0, glm::vec3 ofs);
		
		void updateTmpIk();
		PickNodeInfo curPickNode{}, lastPickNode{};


		SnArItem* it0{};
		bool onKeyEvent(const irr::SEvent::SKeyInput& ke);
		irr::scene::ISceneNode* snMmdSpaceRoot{};  // in MMD space (scale)
		bool framePicked = false;
	private:
		core::aabbox3d<f32> Box;
		video::SMaterial mtr;
		ualib::UaJsonSetting jssARE{ "ArMovieEditor_Settings" };

		ArRootParam Pm;
		irr::core::matrix4 invAbs;
		
		irr::scene::ISceneNode* snIPT{};
		AppNameSpace::AppMainAMP* Amp{};
 
		irr::scene::SnArItem* curAiChar{};
		bool ptScaling = false;
		bool ptDown = false;
		core::vector2df ptStart;
		int fwIdx = 0;
		float hAdd = 0.f;

		ualib::VideoProcessor* pVp{};
		int uiLoadItemIdx = -1, uiLoadModelNum=0;
		float gstScale,gstZoomPow;

		std::vector<LoadItemStruct> loadEIs;

		
		int pickingVtxId=-1;

	
		bool tempIkMode=false;

		bool lastShift = false;
		void ArSnToCs(SnArItem* sn, CsEditorItem* p,int redraw=1);

		const SnArItem* curTgtSn{};



		SnArItem* snSticking{};
        void stickMoving(SnArItem *sn);

		void onGestureRtt(const CsEditorItem *cei) const;
		void setLastPickNode(const PickNodeInfo& pni);



		bool onPtrMove(const SEvent::SPointInput &pe);

		void mouseCursorPos(const irr::SEvent::SPointInput& pe, irr::core::vector2df& xy);


#if IS_WIN
		bool shiftDragging = false, shiftChanging = false; 
		IrrSaba* shiftPickSaba{};
		POINT shiftCurPos{}, shiftStartPos{},shiftLastPos{};
		irr::core::vector2df shiftDragStart,shiftDragOfs,mouseDeltaOfs;
#endif
		bool shiftCurPosGet = false;
		bool setPickReleased = false;
	};

}