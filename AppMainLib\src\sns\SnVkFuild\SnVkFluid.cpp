#include "appGlobal.h"
#include "SnVkFluid.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/include/ISceneManager.h"
#include "../../../UaIrrlicht/include/IVideoDriver.h"
#include "../../../UaIrrlicht/include/IMaterialRendererServices.h"
#include "../../../UaIrrlicht/include/ICameraSceneNode.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkHardwareBuffer.h"

#include "PxPhysicsAPI.h"
#include "extensions/PxParticleExt.h"
#include "extensions/PxCudaHelpersExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"
// External PhysX references (from SnPhyFluid.cpp)

using namespace physx;
using namespace physx::ExtGpu;
using namespace glm;
extern PxPBDParticleSystem* gFluidParticleSystem;
extern PxParticleAndDiffuseBuffer* gFluidBuffer;
extern irr::video::ITexture* gDomeTex;
namespace irr
{
namespace scene
{
	using namespace video;
// Static member initialization
video::MrVkFluid* SnVkFluid::s_fluidMaterialRenderer = nullptr;
video::E_MATERIAL_TYPE SnVkFluid::s_fluidMaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
bool SnVkFluid::s_fluidInitialized = false;

// =================================================================
// Material Index Packing/Unpacking Functions (CPU side)
// =================================================================

// Pack material index into mass field using sign encoding (compatible with float16)
// Encoding: +materialIndex+0.5 for normal particles, -(materialIndex+0.5) for diffuse particles
// Examples: +0.5 = material 0 normal, +1.5 = material 1 normal, -0.5 = material 0 diffuse, -1.5 = material 1 diffuse
inline float packMaterialMass(uint32_t materialIndex, bool isDiffuse) {
    float encoded = static_cast<float>(materialIndex) + 0.5f;
    return isDiffuse ? -encoded : encoded;
}

// Unpack material index and particle type from mass field
void unpackMaterialMass(float packedMass, uint32_t& materialIndex, bool& isDiffuse) {
    if (packedMass >= 0.0f) {
        // Positive = normal particle
        isDiffuse = false;
        materialIndex = static_cast<uint32_t>(packedMass);  // floor(packedMass)
    } else {
        // Negative = diffuse particle
        isDiffuse = true;
        materialIndex = static_cast<uint32_t>(-packedMass);  // floor(-packedMass)
    }
}

// Get actual particle mass based on type (normal vs diffuse)
float getParticleMass(bool isDiffuse) {
    return isDiffuse ? 0.5f : 1.0f;  // Diffuse particles have half mass
}

// Note: phaseToMaterialIndex is now a member function of SnVkFluid

// Convert PhysX phase to material index (using the phase-to-material mapping from SnPhyFluid)
#ifdef _DEBUGX
uint32_t SnVkFluid::phaseToMaterialIndex(uint32_t physxPhase) {
    // Search through fluidPhases to find matching material index

    for (size_t i = 0; i < fluidPhases.size(); ++i) {
        if (fluidPhases[i] == physxPhase) {
			assert(i == (physxPhase & 0xFF)); // Ensure index fits in 8 bits
            return static_cast<uint32_t>(i);
        }
    }

    return 0; // Default to material index 0 if not found
}
#else
inline uint32_t phaseToMaterialIndex(uint32_t physxPhase) {
        // Since debug shows phases are direct indices: 0,1,2,3,4...
        return physxPhase & 0xFF;  // Just extract lower 8 bits
    }
#endif


SnVkFluid::SnVkFluid(f32 size, ISceneNode* parent, ISceneManager* mgr, s32 id,
                     const core::vector3df& position, const core::vector3df& rotation,
                     const core::vector3df& scale, const 
                     const video::VkFluidVolumeUniforms& volumeParams)
    : SnPhyFluid(size, parent, mgr, id, position, rotation, scale)
    , Pm(volumeParams)
    , m_particleBuffer(nullptr)
    , m_densityTexture3D(nullptr)
#if COVARIANCE_COMPUTATION
    , m_covarianceTexture3D(nullptr)
#endif
    , m_gridTexture3D(nullptr)
    , m_renderMode(FRM_VOLUME)
    , m_enableVolumetric(true)
    , m_enableDiffuseRendering(false)
    , m_volumeTexturesNeedUpdate(true)
    , m_fluidColor(video::SColorf( 0,0.22,0.77, 0.01).toSColor()) 
	

    , m_refractionIndex(1.333f)
    , m_absorptionColor(0.57, 0.83, 0.953)
    , maxParticles(0)
    , ptcBuf(nullptr)
    , velBuf(nullptr)
    , phaseBuf(nullptr)
    // Grid initialization (BufferB pattern)
    , m_gridResolution(128, 128, 128)  // 64^3 grid cells
    , m_gridCellSize(0.5f)          // Each cell is 0.5 units
{
    #ifdef _DEBUG
    setDebugName("SnVkFluid");
    #endif
    int mul = 1;
    int res = 128*mul;    // Increase resolution for smoother rendering at scaled coordinates


    int yMulX2 = 4; 
    float sizeR = MUL_FLUID_SCALE(32.f) ;

    int grid= 128*mul;     // Increase grid resolution too
    m_gridResolution.set(grid, grid*yMulX2/2, grid);
    
    // Initialize all VkFluidVolumeUniforms fields
    m_localVolumeMin.set(-sizeR, 0, -sizeR);
    m_localVolumeMax.set(sizeR, sizeR* yMulX2 / 2, sizeR);

    Pm.volumeResX = res; 
    Pm.volumeResY = res * yMulX2 / 2; 
    Pm.volumeResZ = res;
    Pm.volumeMin = core::vector3df(-sizeR, 0, -sizeR);
    Pm.volumeMax = core::vector3df(sizeR, sizeR * yMulX2/2, sizeR);
    Pm.volumeSize = Pm.volumeMax - Pm.volumeMin;
    Pm.rayStepSize = MUL_FLUID_SCALE(0.2f);
    Pm.kernelRadius = MUL_FLUID_SCALE(0.75f);
    Pm.maxRaymarchSteps = 128;
    Pm.densityThreshold = 0.1f;
    Pm.renderDiffuse = 1;
    Pm.numParticles = 0;
    Pm.gridCellSize = 0.5f;
    Pm.lightDir = core::vector3df(0.741f, 1.000f, 0.580f);
    Pm.traceMaxDensity = 5.0f;
    Pm.time = 0.0f;
    Pm.baseColor = { 0,0.22,0.77, 0.03};
    Pm.absorb = m_absorptionColor;
    Pm.renderDiffuse = 0;
    Pm.pm1.x = 0.2f;
    DP(("DEBUG: Scaled volume parameters - min:(%.1f,%.1f,%.1f) max:(%.1f,%.1f,%.1f) stepSize:%.3f\n",
        Pm.volumeMin.X, Pm.volumeMin.Y, Pm.volumeMin.Z,
        Pm.volumeMax.X, Pm.volumeMax.Y, Pm.volumeMax.Z,
        Pm.rayStepSize));

    for (int i = 0; i < 32; i++) {
        SColorHSL hsl(i * 60+150, 100, 50);
        SColorf c = hsl.toSColor();
        Pm.mtrColor[i] = { c.r,c.g,c.b,0.1f };
    }
	Pm.mtrColor[0] = { 0,0.33,1, 0.15 }; // Default fluid color
	Pm.mtrColor[1] = { 0.8f, 0.6f, 0.2f, 0.6f }; // Gold 
	Pm.mtrColor[2] = { 0.2f, 0.6f, 0.8f, 0.6f }; // Blueish
	Pm.mtrColor[3] = { 0.8f, 0.2f, 0.6f, 0.1f }; // Pinkish
	Pm.mtrColor[4] = { 1, 1,1,0.7f}; 
    Pm.mtrColor[5] = { 1, 0.9f, 0.8f, .9f };  
    
    setPassType(IrrPassType_Normal, false);    setPassType(IrrPassType_PostLayer, true);
   // setPassType(IrrPassType_Mirror, false);
    // Initialize Vulkan fluid system with volume rendering

    initFluid(60, 50, 60, { 0, 33.f, 0});

    initializeVulkanFluid();
    initializeVolumeTextures();
    initializeParticleGrid();


}

SnVkFluid::~SnVkFluid()
{
    // Clean up Vulkan buffers using drop() method (Irrlicht reference counting)
    if (m_particleBuffer) {
        m_particleBuffer->drop();
        m_particleBuffer = nullptr;
    }

    //Driver->add* textures managed by driver , no need to free, 
 
    
    // Clean up CUDA staging buffers
    if (ptcBuf) {
        delete[] ptcBuf;
        ptcBuf = nullptr;
    }
    if (velBuf) {
        delete[] velBuf;
        velBuf = nullptr;
    }
    if (phaseBuf) {
        delete[] phaseBuf;
        phaseBuf = nullptr;
    }
}



void SnVkFluid::OnAnimate(u32 timeMs)
{


    if (drawDebugScene) {
        SnPhyFluid::OnAnimate(timeMs); // Call base class for debug scene updates
        return;
    }
    else {
        ISceneNode::OnAnimate(timeMs);    // We'll call our own updateMesh override
        core::matrix4 worldMatrix = getAbsoluteTransformation();
        core::aabbox3d<f32> worldBox(m_localVolumeMin, m_localVolumeMax); 
        worldMatrix.transformBoxEx( worldBox);
        worldBox.MinEdge /= MMD_SABA_SCALE;
		worldBox.MaxEdge /= MMD_SABA_SCALE; // Scale down to original scene coordinates
        Pm.volumeMin = worldBox.MinEdge;
        Pm.volumeMax = worldBox.MaxEdge;
        Pm.volumeSize = Pm.volumeMax - Pm.volumeMin;

        extractPhysXData();

        if (m_particleData.size() > 1) {
            // Update 3D volume textures if needed
            if (m_volumeTexturesNeedUpdate) {

                // Update volume textures in material renderer when they change (BufferD pattern) 
                s_fluidMaterialRenderer->setDensityFieldTexture(m_densityTexture3D);
#if COVARIANCE_COMPUTATION
                s_fluidMaterialRenderer->setCovarianceFieldTexture(m_covarianceTexture3D);
#endif


                // Note: Velocity and distance textures removed as they're not part of BufferD pattern

                m_volumeTexturesNeedUpdate = false; // Reset flag after all updates
            }
            // Step 1: Particle-to-Grid assignment (NEW - creates grid from raw particles)
            uploadParticleData();
            dispatchParticleToGrid();
#if COVARIANCE_COMPUTATION
             computeCovariance3D();  // Main BufferD SVD computation
#endif


        }
        // Update ShaderToy timing (following VkMr2D pattern)
        f32 deltaTime = gFrameTime;
        f32 currentTime = gSceneTime;
        s_fluidMaterialRenderer->setTime(currentTime);
        s_fluidMaterialRenderer->setDeltaTime(deltaTime);
        s_fluidMaterialRenderer->setFrame(gSceneTime * 60); // 60fps frame counter
    }

    // Then, handle volume rendering updates (ShaderToy pattern)
    
}

void SnVkFluid::render()
{
    if (drawDebugScene)
    {
        SnPhyFluid::render();
        return;
    }


    if (!IsVisible) {
        DP(("DEBUG: Fluid node not visible!\n"));
        return;
    }

    if (!s_fluidMaterialRenderer) {
        DP(("DEBUG: Fluid material renderer is NULL!\n"));
        return;
    }


    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) {
        return;
    }
    if (s_fluidMaterialRenderer) {
        CPU_COUNT_B(AAA);
        s_fluidMaterialRenderer->waitForComputeCompletion();
        CPU_COUNT_E(AAA);

        video::IVideoDriver* driver = SceneManager->getVideoDriver();
        resDiv = 2;
        if (!rtTex)
            rtTex = driver->addRenderTargetTexture(driver->getCurrentRenderTargetSize()/resDiv);
		auto oldrt = driver->getRenderTarget();
        driver->setRenderTarget(rtTex, true, true, video::SColor(0, 0, 0, 0)); // Clear render target
        

        
        // Update screen resolution for ShaderToy

        screenSize = driver->getCurrentRenderTargetSize() / resDiv;
        s_fluidMaterialRenderer->setResolution(float3(screenSize.Width, screenSize.Height, 0.0f));

        // Get current transformation matrices - use scene camera directly for consistency
        core::matrix4 worldMatrix = getAbsoluteTransformation();

        // Use scene camera directly instead of driver transforms for better consistency
        scene::ICameraSceneNode* camera = SceneManager->getActiveCamera();
        if (camera) {
            core::matrix4 viewMatrix = camera->getViewMatrix();
            core::matrix4 projectionMatrix = camera->getProjectionMatrix();
            core::vector3df cameraPosition = camera->getAbsolutePosition();

            // Pass matrices to material renderer for proper camera integration
            s_fluidMaterialRenderer->setWorldMatrix(worldMatrix);
            viewMatrix.setTranslation(viewMatrix.getTranslation() * MUL_FLUID_SCALE(1.f) / MMD_SABA_SCALE); // Scale view matrix
            // No scaling needed since fluid simulation is now in original scene coordinates
            s_fluidMaterialRenderer->setViewProjMatrix(viewMatrix, projectionMatrix);

            // Set camera position directly without scaling
            s_fluidMaterialRenderer->setCameraPosition(cameraPosition * MUL_FLUID_SCALE(1.f) / MMD_SABA_SCALE);
        }
        else {
            DP(("DEBUG: No active camera found!\n"));
        }

        core::vector3df pos = worldMatrix.getTranslation();
        //DP(("DEBUG: Fluid world position: %.2f, %.2f, %.2f\n", pos.X, pos.Y, pos.Z));
        driver->setTransform(video::ETS_WORLD, worldMatrix);

        updateVulkanBuffers();

        // Set Vulkan material for volume raymarching
        video::SMaterial volumeMaterial;
        volumeMaterial.MaterialType = s_fluidMaterialType;
        volumeMaterial.Lighting = false;
        volumeMaterial.BackfaceCulling = false;
        volumeMaterial.setFlag(video::EMF_BLEND_OPERATION, true);
        volumeMaterial.MaterialTypeParam = static_cast<f32>(m_renderMode);

        driver->setMaterial(volumeMaterial);

        // Render using ShaderToy pattern (single full-screen quad)
        renderFullScreenQuad();

        driver->setRenderTarget(oldrt, 0, 0, 0);
    }
 
    auto rtsize = driver->getCurrentRenderTargetSize();
    irr::core::recti rc = rtTex->getRectI(), trc = { {0,0,},rtsize };
    rc.fitAspectRatioInside(trc);
    driver->draw2DImageRect(rtTex, trc, rc, 0, 0, Pm.flag);

  
}


void SnVkFluid::dispatchParticleToGrid()
{
    CPU_COUNT_B(particleGrid);
    if (s_fluidMaterialRenderer && m_particleData.size() > 0) {
        DP(("DEBUG: Dispatching particle-to-grid assignment for %u particles\n", (u32)m_particleData.size()));
        s_fluidMaterialRenderer->dispatchParticleToGridComputation3D(m_particleData,
            Pm.volumeResX, Pm.volumeResY, Pm.volumeResZ);
    } else {
        DP(("DEBUG: Cannot dispatch particle-to-grid - renderer:%p, particles:%u\n", 
            s_fluidMaterialRenderer, (u32)m_particleData.size()));
	}
	CPU_COUNT_E(particleGrid);
}
 


void SnVkFluid::computeCovariance3D()
{
#if COVARIANCE_COMPUTATION
    if (s_fluidMaterialRenderer && m_covarianceTexture3D && m_particleData.size() > 0) {
        DP(("DEBUG: Dispatching 3D covariance computation for %u particles (BufferD equivalent)\n", (u32)m_particleData.size()));
        s_fluidMaterialRenderer->dispatchCovarianceComputation3D(
            Pm.volumeResX, Pm.volumeResY, Pm.volumeResZ);
    }
#endif
}

// Volume configuration methods
void SnVkFluid::setVolumeResolution(u32 x, u32 y, u32 z)
{
    Pm.volumeResX = x;
    Pm.volumeResY = y;
    Pm.volumeResZ = z;
    m_volumeTexturesNeedUpdate = true;
    
    // Recreate volume textures with new resolution
    createVolumeTextures();
    
    DP(("DEBUG: Volume resolution set to %ux%ux%u\n", x, y, z));
}

void SnVkFluid::setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds)
{
    Pm.volumeMin = minBounds;
    Pm.volumeMax = maxBounds;
    m_volumeTexturesNeedUpdate = true;
    
    DP(("DEBUG: Volume extents set to (%.2f,%.2f,%.2f) -> (%.2f,%.2f,%.2f)\n",
        minBounds.X, minBounds.Y, minBounds.Z, maxBounds.X, maxBounds.Y, maxBounds.Z));
}



void SnVkFluid::setDepthTexture(irr::video::ITexture* texture) 
{
	s_fluidMaterialRenderer->setDepthTexture(texture);

}

 
 

void SnVkFluid::setAbsorptionColor(const core::vector3df& absorption)
{
    m_absorptionColor = absorption;
    if (s_fluidMaterialRenderer) {
        s_fluidMaterialRenderer->setAbsorptionColor(absorption);
    }
}

// Get statistics
u32 SnVkFluid::getActiveParticleCount() const
{
    return (u32)m_particleData.size();
}

void SnVkFluid::RefreshMr()
{
	s_fluidMaterialRenderer->ReloadShaders();
}

void SnVkFluid::changeMaterialPreset(int presetType)
{
	SnPhyFluid::changeMaterialPreset(presetType);
    Pm.mtrColor[defaultMaterialIndex] = materialParameters[defaultMaterialIndex].defaultColor;
}

// Protected methods
void SnVkFluid::initializeVulkanFluid()
{
    if (!s_fluidInitialized) {
        video::IVideoDriver* driver = SceneManager->getVideoDriver();
        if (driver) {
            // Create material renderer for volume raymarching
            s_fluidMaterialRenderer = new video::MrVkFluid(driver, SceneManager->getFileSystem());
            s_fluidMaterialType = (video::E_MATERIAL_TYPE)driver->addMaterialRenderer(s_fluidMaterialRenderer);
            s_fluidInitialized = true;
            
            DP(("DEBUG: VkFluid material renderer initialized with type %d\n", s_fluidMaterialType));
        }
    }
    s_fluidMaterialRenderer->setAbsorptionColor({}); // Set default fluid color
    // Create GPU buffers for particle data
    createVulkanBuffers();
}

void SnVkFluid::initializeVolumeTextures()
{
    createVolumeTextures();
    m_volumeTexturesNeedUpdate = true;
}

void SnVkFluid::createVolumeTextures()
{
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;
    
    const u32 resX = Pm.volumeResX;
    const u32 resY = Pm.volumeResY;
    const u32 resZ = Pm.volumeResZ;
    
    DP(("DEBUG: Creating 3D volume textures %ux%ux%u\n", resX, resY, resZ));
    
    // Release existing textures
    if (m_densityTexture3D) m_densityTexture3D->drop();

    // Create 3D volume textures with proper 3D dimensions and storage usage for compute shaders (BufferD pattern)
    const core::vector3d<u32> volumeSize(resX, resY, resZ);
    
    // Essential textures for BufferD covariance computation
    m_densityTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidDensity3D<STO>", video::ECF_A16B16G16R16F);  // RGBA16F format for color(RGB) + density/alpha(A)

#if COVARIANCE_COMPUTATION
    if (m_covarianceTexture3D) m_covarianceTexture3D->drop();
    m_covarianceTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidCovariance3D<STO>", video::ECF_A16B16G16R16F);
#endif   
    // Note: Velocity, distance, normal, and temperature textures removed 
    // as they're not part of BufferD covariance computation pattern
	s_fluidMaterialRenderer->setEnvironmentMap(gDomeTex); // Set environment map for reflections
}
void SnVkFluid::extractPhysXData()
{
    // Extract particle data from PhysX following SnPhyFluid::updateMesh() pattern
    PxParticleAndDiffuseBuffer* userBuffer = gFluidBuffer;
    if (!userBuffer) {
        DP(("DEBUG: No PhysX fluid buffer available\n"));
        return;
    }

    // Extract regular particles
    PxVec4* positions = userBuffer->getPositionInvMasses();
    PxVec4* velocities = userBuffer->getVelocities();
    PxU32* phases = userBuffer->getPhases();
    PxU32 numParticles = userBuffer->getNbActiveParticles();

    // Extract diffuse particles
    PxVec4* diffusePositions = userBuffer->getDiffusePositionLifeTime();
    PxVec4* diffuseVelocities = userBuffer->getDiffuseVelocities();
    PxU32 numDiffuseParticles = userBuffer->getNbActiveDiffuseParticles();

    // Calculate total particle count (regular + diffuse)
    PxU32 totalParticles = numParticles + numDiffuseParticles;
	u32 totalRenderParticles = 0;
    if ((!positions || numParticles == 0) && (!diffusePositions || numDiffuseParticles == 0)) {
        DP(("DEBUG: No PhysX particle data - regular:%p count:%u, diffuse:%p count:%u\n", 
            positions, numParticles, diffusePositions, numDiffuseParticles));
        return;
    }

    // Get CUDA context for memory copying (following SnPhyFluid pattern)
    PxScene* scene;
    PxGetPhysics().getScenes(&scene, 1);
    PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();
    if (!cudaContextManager) {
        DP(("DEBUG: No CUDA context manager available\n"));
        return;
    }

    cudaContextManager->acquireContext();
    PxCudaContext* cudaContext = cudaContextManager->getCudaContext();
    if (!cudaContext) {
        DP(("DEBUG: No CUDA context available\n"));
        return;
    }

    // Allocate temporary buffers for CUDA memory copy (include space for diffuse particles)
    if (!ptcBuf || maxParticles < totalParticles) {
        // Calculate full dynamic buffer capacity (matching SnPhyFluid approach)
        const PxU32 initialParticles = numPointsX * numPointsY * numPointsZ;
        const PxU32 dynamicMaxParticles = static_cast<PxU32>(initialParticles * dynamicParticleCapacityMultiplier);
        
        // Allocate for the full dynamic capacity, not just current particles
        const PxU32 allocateSize = core::max_(totalParticles, dynamicMaxParticles);
        
        if (ptcBuf) delete[] ptcBuf, delete[] ptcBufT;
		if (velBuf) delete[] velBuf, delete[] velBufT;
        if (phaseBuf) delete[] phaseBuf;
        
		ptcBuf = new float4[allocateSize]; ptcBufT = new float4[allocateSize];
		velBuf = new float4[allocateSize]; velBufT = new float4[allocateSize];
        phaseBuf = new uint32_t[allocateSize];
         
        
        maxParticles = allocateSize;
        
        DP(("DEBUG: Allocated and ZEROED staging buffers for %u total particles (%u regular + %u diffuse, capacity: %u)\n", 
            totalParticles, numParticles, numDiffuseParticles, allocateSize));
    }
    
    // Copy regular particle data from GPU to CPU (same as SnPhyFluid::updateMesh)
    if (positions && numParticles > 0) {
        cudaContext->memcpyDtoH(ptcBuf, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);
        
        // Copy velocity data from GPU to CPU if available
        if (velocities) {
            cudaContext->memcpyDtoH(velBuf, CUdeviceptr(velocities), sizeof(PxVec4) * numParticles);
        }
        
        // Copy phase data from GPU to CPU for material index extraction
        if (phases) {
            cudaContext->memcpyDtoH(phaseBuf, CUdeviceptr(phases), sizeof(PxU32) * numParticles);
        }
    }
    
    // Copy diffuse particle data from GPU to CPU (offset by regular particle count)
    if (diffusePositions && numDiffuseParticles > 0) {
        cudaContext->memcpyDtoH(&ptcBuf[numParticles], CUdeviceptr(diffusePositions), sizeof(PxVec4) * numDiffuseParticles);
        
        // Copy diffuse velocity data from GPU to CPU if available
        if (diffuseVelocities) {
            cudaContext->memcpyDtoH(&velBuf[numParticles], CUdeviceptr(diffuseVelocities), sizeof(PxVec4) * numDiffuseParticles);
        }
        
        // For diffuse particles, assign default material phase (since they don't have individual phases)
        for (PxU32 i = 0; i < numDiffuseParticles; ++i) {
            phaseBuf[numParticles + i] = fluidParticlePhase; // Use default phase
        }
    }

    // Resize particle data array to accommodate maximum capacity
    if (m_particleData.size() < totalParticles) {
        m_particleData.set_used(totalParticles);
    }

    // Copy PhysX data to our format - separate regular and diffuse particles
    // Fixed: Use standard for loop instead of OpenMP to avoid scheduling differences
#define CAN_DEL_PARTICLES  1
 
#if CAN_DEL_PARTICLES
    u32 c = 0; 
    u32 regularParticleCount = 0;
    u32 diffuseParticleCount = 0;
    
    // Process regular particles first (full mass = 1.0)
    for (PxU32 i = 0; i < numParticles; ++i) {
        auto& p = ptcBuf[i];
        if (p.y < 1 
            //|| p.y>60.f
            ) {
            //float dis = glm::length2(vec2(p.x, p.z));
            //if (dis > 360) {
            //    //glm::vec3 p1 = glm::normalize(glm::vec3(p.x, 0, p.z)) * 5.f;
            //    //ptcBuf[i] = float4(p1.x, 60, p1.z, p.w);             velBuf[i] = float4(0, 0, 0, 0);     
            //    continue;
            //}
            //else if (dis > 225) {
            //    auto dir = (vec3(0, velBuf[i].y+1, 0) - vec3(velBuf[i])) * 1.f;
            //    velBuf[i] = vec4(dir, 1);
            //}
            continue;
        }
        // Convert PhysX phase to material index
        uint32_t materialIndex = phaseToMaterialIndex(phaseBuf[i]);
 
        // Pack material index and particle type into mass field
        float packedMass = packMaterialMass(materialIndex, false);
        
        // Position and packed mass (from CUDA copied data) - convert from PhysX coordinate system (invert Z)
		ptcBufT[c] = ptcBuf[i];
		velBufT[c] = velBuf[i];
        m_particleData[c] = float4(MUL_FLUID_SCALE(p.x), MUL_FLUID_SCALE(p.y), -MUL_FLUID_SCALE(p.z), packedMass);
        c++;
        regularParticleCount++;
    }
    
    // Update numParticles to actual count after filtering
    if (c < numParticles) {
        numParticles = c;
        changePtcState = 1;
    }
    
    // Process diffuse particles (mass < 1.0) - stored in secondary grid texture
    if (Pm.renderDiffuse) {
        for (PxU32 i = numParticles; i < totalParticles; ++i) {
            auto& p = ptcBuf[i];
            
            // Convert PhysX phase to material index
            uint32_t materialIndex = phaseToMaterialIndex(phaseBuf[i]);
 
            // Pack material index and particle type into mass field
            float packedMass = packMaterialMass(materialIndex, true);
            
            // Position and packed mass (stored in secondary grid texture)
            m_particleData[c] = float4(MUL_FLUID_SCALE(p.x), MUL_FLUID_SCALE(p.y), -MUL_FLUID_SCALE(p.z), packedMass);
            c++;
            diffuseParticleCount++;
        }
    }
    totalRenderParticles = c;
    
    DP(("DEBUG: Separated particles - Regular: %u (primary grid), Diffuse: %u (secondary grid), Total render: %u\n", 
        regularParticleCount, diffuseParticleCount, totalRenderParticles));

#else
    // Alternative: Process all particles with OpenMP (preserve particle type for mass assignment)
#pragma omp parallel for schedule(static) num_threads(8) 
    for (int i = 0; i < totalParticles; ++i) {
        auto& p = ptcBuf[i];
        
        // Convert PhysX phase to material index
        uint32_t materialIndex = phaseToMaterialIndex(phaseBuf[i]);
        bool isDiffuse = (i >= numParticles); // Diffuse particles come after regular ones
        
        // Pack material index and particle type into mass field
        float packedMass = packMaterialMass(materialIndex, isDiffuse);
        
        m_particleData[i] = float4(p.x * FluidSC, p.y * FluidSC, -p.z * FluidSC, packedMass);
	}
#endif
    

    if (changePtcState) {
        if (changePtcState & 0x10) {
            for (PxU32 i = 0; i < numParticles; ++i) {
                // Convert PhysX positions to float4 format (invert Z for Vulkan)
                ptcBufT[i] = float4(ptcBufT[i].x, ptcBufT[i].y + MUL_FLUID_SCALE(1.f), ptcBufT[i].z, ptcBufT[i].w);
                velBufT[i] = float4(0, 100, 0, 0);
			}
        }
        if (changePtcState & 0x20) {
            for (PxU32 i = 0; i < numParticles; ++i) {
                // Convert PhysX positions to float4 format (invert Z for Vulkan)
#if CAN_DEL_PARTICLES
				auto& p = ptcBufT[i];
				auto& v = velBufT[i];
#else
                auto& p = ptcBuf[i];
                auto& v = velBuf[i];
#endif
				glm::vec2 pos(p.x, -p.z);
                if (v.y < 10 && glm::length2(pos - glm::vec2(0, 0)) < 3.f) {
                    p = float4(p.x, p.y + gFrameTime * 2, p.z, p.w);
                    v = float4(0, 200, 0, 0);
                }
            }
        }

        // Create params struct and delegate to the new struct-based version
        EnhancedFluidParticleParams params;
        params.numParticles = numParticles;
        params.positions = ptcBufT;
        params.velocities = velBufT;
        params.isAppend = false;
        params.copyFlag = 1 | 2;
       
        changePtcState = 0;
        setParticles(params);
    }
    m_particleData.set_used(totalRenderParticles);	assert(m_particleData.size() == totalRenderParticles);

    //for display
    activePtCount = numParticles;
	maxPtCount = maxParticles;
    activeDiffusePtCount = numDiffuseParticles;
	maxDiffusePtCount = maxDiffuseParticles;
    
    // Update particle count in material renderer
    if (s_fluidMaterialRenderer) {
        // Update volume uniforms with new particle count
        updateVulkanBuffers();
    }
}

void SnVkFluid::uploadParticleData()
{
    if (m_particleData.size() == 0 || !m_particleBuffer) {
        DP(("DEBUG: No particle data to upload - count:%u buffer:%p\n", (u32)m_particleData.size(), m_particleBuffer));
        return;
    }

    // Upload particle data to GPU buffer (m_particleData is array<float4>)
    video::VkHardwareBuffer* vkBuffer = static_cast<video::VkHardwareBuffer*>(m_particleBuffer);
    if (vkBuffer) {
        void* data = vkBuffer->lock(video::ETLM_READ_WRITE, 0, m_particleData.size() * sizeof(float4));
        if (data) {
            memcpy(data, m_particleData.pointer(), m_particleData.size() * sizeof(float4));
            vkBuffer->unlock();
            DP(("DEBUG: Uploaded %u particles (float4) to GPU buffer\n", (u32)m_particleData.size()));
        }
    }
	s_fluidMaterialRenderer->setParticleBuffer((video::VkHardwareBuffer * )m_particleBuffer);
}
 
void SnVkFluid::createVulkanBuffers()
{
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;

    // Calculate dynamic buffer capacity based on SnPhyFluid parameters (matching SnPhyFluid approach)
    const u32 initialParticles = numPointsX * numPointsY * numPointsZ;
    const u32 maxParticleCount = static_cast<u32>(initialParticles * dynamicParticleCapacityMultiplier)+maxDiffuseParticles;

    // Create particle buffer for PhysX data (float4 particles)
    if (!m_particleBuffer) {
        m_particleBuffer = driver->createHardwareBuffer(
            video::EHBT_STORAGE, video::EHBA_DEFAULT_RW, maxParticleCount * sizeof(float4) );
        DP(("DEBUG: Created particle buffer for %u particles (float4) with capacity for %u total\n", 
            initialParticles, maxParticleCount));
    }


}

void SnVkFluid::updateVulkanBuffers()
{
        // Time for animation
    Pm.time = gFrameTime;
        
    // Particle count
    Pm.numParticles = static_cast<u32>(m_particleData.size());
        
    // Grid cell size for coordinate conversion
    Pm.gridCellSize = m_gridCellSize;
    auto driver = SceneManager->getVideoDriver();
        
        auto pos = driver->dsd.lightPos[0];

        Pm.lightDir.x = -pos.X;
        Pm.lightDir.y = -pos.Y;
        Pm.lightDir.z = -pos.Z;
        Pm.lightDir = glm::fastNormalize(Pm.lightDir);

    s_fluidMaterialRenderer->updateVolumeUniforms(Pm);    
}

// ShaderToy rendering method using VkDriver::draw2DImageMr pattern
void SnVkFluid::renderFullScreenQuad()
{

    // Set raymarching pipeline mode
    s_fluidMaterialRenderer->setRenderPipeline(video::MrVkFluid::FRP_RAYMARCHING);
    
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;
    
	
    
    // Create material for fluid ShaderToy rendering (following VkMr2D pattern)
    video::SMaterial fluidMaterial;
    fluidMaterial.MaterialType = s_fluidMaterialType;
    fluidMaterial.Lighting = false;
    fluidMaterial.BackfaceCulling = false;
    fluidMaterial.setFlag(video::EMF_BLEND_OPERATION, true);
    
    // Set 3D textures for fluid data (BufferD pattern - essential textures only)
    fluidMaterial.setTexture(0, m_gridTexture3D);       // iChannel0 - binding 1 (grid particles)
    fluidMaterial.setTexture(1, m_densityTexture3D);    // iChannel1 - binding 2 (density field)
#if COVARIANCE_COMPUTATION
    fluidMaterial.setTexture(2, m_covarianceTexture3D); // iChannel2 - binding 3 (covariance field - BufferD output)
#endif
    
	s_fluidMaterialRenderer->transitDepthImageLayout(); // Ensure depth image is in correct layout for rendering
    // Use VkDriver::draw2DImageMr for material-aware rendering (like VkMr2D)
    driver->draw2DImageMr(
        fluidMaterial,
        core::rect<s32>(0, 0, screenSize.Width , screenSize.Height ),  // Full screen dest
        core::rect<s32>(0, 0, screenSize.Width, screenSize.Height),  // Full screen source
        nullptr,  // No clip
        nullptr,  // Default colors
        false,     // No Y inversion
        false // NOT VkMr2D matrial
    );

    
}

// =================================================================
// Grid-based Particle Storage Implementation (BufferB Pattern)
// =================================================================

void SnVkFluid::initializeParticleGrid()
{
    // Calculate grid world bounds based on volume parameters
    m_gridWorldMin = Pm.volumeMin;
    m_gridWorldMax = Pm.volumeMax;
    
    // Calculate grid cell size based on volume size and resolution
    core::vector3df volumeSize = m_gridWorldMax - m_gridWorldMin;
    m_gridCellSize = core::max_(volumeSize.X / m_gridResolution.X, 
                               core::max_(volumeSize.Y / m_gridResolution.Y, 
                                         volumeSize.Z / m_gridResolution.Z));
    
	auto colorFormat = 1 ? video::ECF_A32B32G32R32F : video::ECF_A16B16G16R16F; //ALSO modify rgba32f in shader
    // Create 3D texture for grid data
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (driver) {
        m_gridTexture3D = driver->addRenderTargetTexture3D(
            m_gridResolution, "FluidParticleGrid3D<STO>", colorFormat);
    }
    
    DP(("DEBUG: Initialized particle grid %ux%ux%u, cell size: %.3f\n", 
        m_gridResolution.X, m_gridResolution.Y, m_gridResolution.Z, m_gridCellSize));
    
    // Immediately set the grid texture in the material renderer for GPU-based processing
    if (s_fluidMaterialRenderer && m_gridTexture3D) {
        s_fluidMaterialRenderer->setParticleGridTexture(m_gridTexture3D);
        DP(("DEBUG: Set grid texture in material renderer during initialization\n"));
    }
     
    if (driver) {
        m_gridTexture3D2 = driver->addRenderTargetTexture3D(
            m_gridResolution, "FluidParticleGrid3D2<STO>", colorFormat);
    }
    
    DP(("DEBUG: Initialized particle grid %ux%ux%u, cell size: %.3f\n", 
        m_gridResolution.X, m_gridResolution.Y, m_gridResolution.Z, m_gridCellSize));
    
    // Immediately set the grid texture in the material renderer for GPU-based processing
    if (s_fluidMaterialRenderer && m_gridTexture3D2) {
        s_fluidMaterialRenderer->setParticleGridTextureSecondary(m_gridTexture3D2);
        DP(("DEBUG: Set grid texture 2 in material renderer during initialization\n"));
    }


}

 

 


} // namespace scene
} // namespace irr 