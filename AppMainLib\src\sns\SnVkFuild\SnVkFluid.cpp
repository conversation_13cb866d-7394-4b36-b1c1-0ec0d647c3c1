#include "appGlobal.h"
#include "SnVkFluid.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/include/ISceneManager.h"
#include "../../../UaIrrlicht/include/IVideoDriver.h"
#include "../../../UaIrrlicht/include/IMaterialRendererServices.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkHardwareBuffer.h"

#include "PxPhysicsAPI.h"
#include "extensions/PxParticleExt.h"
#include "extensions/PxCudaHelpersExt.h"
#include "cudamanager/PxCudaContext.h"
#include "cudamanager/PxCudaContextManager.h"
// External PhysX references (from SnPhyFluid.cpp)

using namespace physx;
using namespace physx::ExtGpu;

extern PxPBDParticleSystem* gFluidParticleSystem;
extern PxParticleAndDiffuseBuffer* gFluidBuffer;

namespace irr
{
namespace scene
{

// Static member initialization
video::MrVkFluid* SnVkFluid::s_fluidMaterialRenderer = nullptr;
video::E_MATERIAL_TYPE SnVkFluid::s_fluidMaterialType = video::EMT_TRANSPARENT_ALPHA_CHANNEL;
bool SnVkFluid::s_fluidInitialized = false;

SnVkFluid::SnVkFluid(f32 size, ISceneNode* parent, ISceneManager* mgr, s32 id,
                     const core::vector3df& position, const core::vector3df& rotation,
                     const core::vector3df& scale, const FluidSimParams& params,
                     const FluidVolumeParams& volumeParams)
    : SnPhyFluid(size, parent, mgr, id, position, rotation, scale)
    , m_params(params)
    , m_volumeParams(volumeParams)
    , m_particleBuffer(nullptr)
    , m_diffuseBuffer(nullptr)
    , m_covarianceBuffer(nullptr)
    , m_densityTexture3D(nullptr)
    , m_velocityTexture3D(nullptr)
    , m_distanceTexture3D(nullptr)
    , m_covarianceTexture3D(nullptr)
    , m_normalTexture3D(nullptr)
    , m_temperatureTexture3D(nullptr)
    , m_renderMode(FRM_VOLUME)
    , m_enableVolumetric(true)
    , m_enableDiffuseRendering(false)
    , m_volumeTexturesNeedUpdate(true)
    , m_fluidColor(video::SColor(255, 100, 150, 255))
    , m_refractionIndex(1.333f)
    , m_absorptionColor(0.584f, 0.843f, 0.953f)
    , maxParticles(0)
    , ptcBuf(nullptr)
    , velBuf(nullptr)
{
    #ifdef _DEBUG
    setDebugName("SnVkFluid");
    #endif

    // Initialize Vulkan fluid system with volume rendering
    initializeVulkanFluid();
    initializeVolumeTextures();
}

SnVkFluid::~SnVkFluid()
{
    // Clean up Vulkan buffers using drop() method (Irrlicht reference counting)
    if (m_particleBuffer) {
        m_particleBuffer->drop();
        m_particleBuffer = nullptr;
    }
    if (m_diffuseBuffer) {
        m_diffuseBuffer->drop();
        m_diffuseBuffer = nullptr;
    }
    if (m_covarianceBuffer) {
        m_covarianceBuffer->drop();
        m_covarianceBuffer = nullptr;
    }
    
    // Clean up 3D volume textures
    if (m_densityTexture3D) {
        m_densityTexture3D->drop();
        m_densityTexture3D = nullptr;
    }
    if (m_velocityTexture3D) {
        m_velocityTexture3D->drop();
        m_velocityTexture3D = nullptr;
    }
    if (m_distanceTexture3D) {
        m_distanceTexture3D->drop();
        m_distanceTexture3D = nullptr;
    }
    if (m_covarianceTexture3D) {
        m_covarianceTexture3D->drop();
        m_covarianceTexture3D = nullptr;
    }
    if (m_normalTexture3D) {
        m_normalTexture3D->drop();
        m_normalTexture3D = nullptr;
    }
    if (m_temperatureTexture3D) {
        m_temperatureTexture3D->drop();
        m_temperatureTexture3D = nullptr;
    }
    
    // Clean up CUDA staging buffers
    if (ptcBuf) {
        delete[] ptcBuf;
        ptcBuf = nullptr;
    }
    if (velBuf) {
        delete[] velBuf;
        velBuf = nullptr;
    }
}

void SnVkFluid::updateMesh()
{
    // Override SnPhyFluid::updateMesh() to prevent quad mesh generation
    // For volume rendering, we don't need traditional mesh geometry
    // The PhysX particle data will be used for volume textures instead
    
    // Extract PhysX data directly without creating mesh geometry
    extractPhysXData();
    uploadParticleData();
}

void SnVkFluid::OnAnimate(u32 timeMs)
{
    // First, let SnPhyFluid handle PhysX simulation (without calling updateMesh)
    // We'll call our own updateMesh override
    ISceneNode::OnAnimate(timeMs);
    
    // Update PhysX simulation if this is the active fluid
    // (Note: This may need to be moved to a proper PhysX update call)
    updateMesh();

    // Then, handle volume rendering updates (ShaderToy pattern)
    if (s_fluidMaterialRenderer) {
        // Extract PhysX data and convert to volume textures
        extractPhysXData();
        
        // Update 3D volume textures if needed
        if (m_volumeTexturesNeedUpdate) {
            updateVolumeTextures();
            
            // Update volume textures in material renderer when they change
            s_fluidMaterialRenderer->setDensityFieldTexture(m_densityTexture3D);
            s_fluidMaterialRenderer->setVelocityFieldTexture(m_velocityTexture3D);
            s_fluidMaterialRenderer->setDistanceFieldTexture(m_distanceTexture3D);
            s_fluidMaterialRenderer->setCovarianceFieldTexture(m_covarianceTexture3D);
            
            m_volumeTexturesNeedUpdate = false; // Reset flag after all updates
        }
        
        // Dispatch volume generation compute shaders
        dispatchVolumeComputeShaders();
        
        // Update ShaderToy timing (following VkMr2D pattern)
        f32 deltaTime = gFrameTime;
        f32 currentTime = timeMs / 1000.0f;
        s_fluidMaterialRenderer->setTime(currentTime);
        s_fluidMaterialRenderer->setDeltaTime(deltaTime);
        s_fluidMaterialRenderer->setFrame(timeMs / 16); // 60fps frame counter
        
        // Update camera and matrices for volume rendering
        video::IVideoDriver* driver = SceneManager->getVideoDriver();
        if (driver) {
            // Update screen resolution for ShaderToy
            core::dimension2d<u32> screenSize = driver->getCurrentRenderTargetSize();
            s_fluidMaterialRenderer->setResolution(float3(screenSize.Width, screenSize.Height, 0.0f));
            
            core::matrix4 worldMatrix = getAbsoluteTransformation();
            core::matrix4 viewMatrix = driver->getTransform(video::ETS_VIEW);
            core::matrix4 projectionMatrix = driver->getTransform(video::ETS_PROJECTION);
            
            s_fluidMaterialRenderer->setWorldMatrix(worldMatrix);
            s_fluidMaterialRenderer->setViewMatrix(viewMatrix);
            s_fluidMaterialRenderer->setProjectionMatrix(projectionMatrix);
            
            // Update camera position for raymarching
            core::matrix4 viewInverse;
            viewMatrix.getInverse(viewInverse);
            core::vector3df cameraPos = viewInverse.getTranslation();
            s_fluidMaterialRenderer->setCameraPosition(cameraPos);
             
        }
    }
}

void SnVkFluid::render()
{
    if (!IsVisible) {
        DP(("DEBUG: Fluid node not visible!\n"));
        return;
    }
    
    if (!s_fluidMaterialRenderer) {
        DP(("DEBUG: Fluid material renderer is NULL!\n"));
        return;
    }
    
    DP(("DEBUG: Rendering fluid volume with %u particles\n", (u32)m_particleData.size()));

    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) {
        return;
    }

    // Set transformation matrix
    core::matrix4 worldMatrix = getAbsoluteTransformation();
    core::vector3df pos = worldMatrix.getTranslation();
    DP(("DEBUG: Fluid world position: %.2f, %.2f, %.2f\n", pos.X, pos.Y, pos.Z));
    driver->setTransform(video::ETS_WORLD, worldMatrix);
 

    // Update volume uniforms
    updateVulkanBuffers();

    // Set Vulkan material for volume raymarching
    video::SMaterial volumeMaterial;
    volumeMaterial.MaterialType = s_fluidMaterialType;
    volumeMaterial.Lighting = false;
    volumeMaterial.BackfaceCulling = false;
    volumeMaterial.setFlag(video::EMF_BLEND_OPERATION, true);
    volumeMaterial.MaterialTypeParam = static_cast<f32>(m_renderMode);
    
    driver->setMaterial(volumeMaterial);

    // Render using ShaderToy pattern (single full-screen quad)
    renderFullScreenQuad();
}

// Volume rendering methods (ShaderToy pattern)
void SnVkFluid::updateVolumeTextures()
{
    DP(("DEBUG: Updating volume textures %ux%ux%u\n", 
        m_volumeParams.volumeResX, m_volumeParams.volumeResY, m_volumeParams.volumeResZ));
        
    // Upload particle data to GPU buffers
    uploadParticleData();
    
    // Generate 3D volume textures from particle data
    generateDensityField3D();
    generateVelocityField3D();
    generateDistanceField3D();
    computeCovariance3D();
}

void SnVkFluid::generateDensityField3D()
{
    if (s_fluidMaterialRenderer && m_enableVolumetric) {
        DP(("DEBUG: Dispatching 3D density computation %ux%ux%u\n", 
            m_volumeParams.volumeResX, m_volumeParams.volumeResY, m_volumeParams.volumeResZ));
        s_fluidMaterialRenderer->dispatchDensityComputation3D(
            m_volumeParams.volumeResX, m_volumeParams.volumeResY, m_volumeParams.volumeResZ);
    } else {
        DP(("DEBUG: Cannot generate 3D density field - renderer:%p, volumetric:%d\n", 
            s_fluidMaterialRenderer, m_enableVolumetric));
    }
}

void SnVkFluid::generateVelocityField3D()
{
    if (s_fluidMaterialRenderer && m_particleData.size() > 0) {
        DP(("DEBUG: Dispatching 3D velocity field computation for %u particles\n", (u32)m_particleData.size()));
        s_fluidMaterialRenderer->dispatchVelocityComputation3D(
            m_volumeParams.volumeResX, m_volumeParams.volumeResY, m_volumeParams.volumeResZ);
    }
}

void SnVkFluid::generateDistanceField3D()
{
    if (s_fluidMaterialRenderer && m_particleData.size() > 0) {
        DP(("DEBUG: Dispatching 3D distance field computation for %u particles\n", (u32)m_particleData.size()));
        s_fluidMaterialRenderer->dispatchDistanceComputation3D(
            m_volumeParams.volumeResX, m_volumeParams.volumeResY, m_volumeParams.volumeResZ);
    }
}

void SnVkFluid::computeCovariance3D()
{
    if (s_fluidMaterialRenderer && m_particleData.size() > 0) {
        DP(("DEBUG: Dispatching 3D covariance computation for %u particles (BufferD equivalent)\n", (u32)m_particleData.size()));
        s_fluidMaterialRenderer->dispatchCovarianceComputation3D((u32)m_particleData.size());
    }
}

// Volume configuration methods
void SnVkFluid::setVolumeResolution(u32 x, u32 y, u32 z)
{
    m_volumeParams.volumeResX = x;
    m_volumeParams.volumeResY = y;
    m_volumeParams.volumeResZ = z;
    m_volumeTexturesNeedUpdate = true;
    
    // Recreate volume textures with new resolution
    createVolumeTextures();
    
    DP(("DEBUG: Volume resolution set to %ux%ux%u\n", x, y, z));
}

void SnVkFluid::setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds)
{
    m_volumeParams.volumeMin = minBounds;
    m_volumeParams.volumeMax = maxBounds;
    m_volumeTexturesNeedUpdate = true;
    
    DP(("DEBUG: Volume extents set to (%.2f,%.2f,%.2f) -> (%.2f,%.2f,%.2f)\n",
        minBounds.X, minBounds.Y, minBounds.Z, maxBounds.X, maxBounds.Y, maxBounds.Z));
}

void SnVkFluid::setRaymarchSteps(u32 steps)
{
    m_volumeParams.maxRaymarchSteps = steps;
    DP(("DEBUG: Raymarching steps set to %u\n", steps));
}

void SnVkFluid::setRaymarchStepSize(f32 stepSize)
{
    m_volumeParams.rayStepSize = stepSize;
    DP(("DEBUG: Raymarching step size set to %.4f\n", stepSize));
}

void SnVkFluid::setDensityThreshold(f32 threshold)
{
    m_volumeParams.densityThreshold = threshold;
    DP(("DEBUG: Density threshold set to %.4f\n", threshold));
}

void SnVkFluid::setVolumeNoiseScale(f32 scale)
{
    m_volumeParams.volumeNoiseScale = scale;
    DP(("DEBUG: Volume noise scale set to %.4f\n", scale));
}

// Material properties
void SnVkFluid::setFluidColor(const video::SColor& color)
{
    m_fluidColor = color;
    if (s_fluidMaterialRenderer) {
        s_fluidMaterialRenderer->setFluidColor(color);
    }
}

void SnVkFluid::setRefractionIndex(f32 index)
{
    m_refractionIndex = index;
    if (s_fluidMaterialRenderer) {
        s_fluidMaterialRenderer->setRefractionIndex(index);
    }
}

void SnVkFluid::setAbsorptionColor(const core::vector3df& absorption)
{
    m_absorptionColor = absorption;
    if (s_fluidMaterialRenderer) {
        s_fluidMaterialRenderer->setAbsorptionColor(absorption);
    }
}

void SnVkFluid::setAbsorptionScale(f32 scale)
{
    m_volumeParams.absorptionScale = scale;
}

void SnVkFluid::setScatteringScale(f32 scale)
{
    m_volumeParams.scatteringScale = scale;
}

// Get statistics
u32 SnVkFluid::getActiveParticleCount() const
{
    return (u32)m_particleData.size();
}

// Protected methods
void SnVkFluid::initializeVulkanFluid()
{
    if (!s_fluidInitialized) {
        video::IVideoDriver* driver = SceneManager->getVideoDriver();
        if (driver) {
            // Create material renderer for volume raymarching
            s_fluidMaterialRenderer = new video::MrVkFluid(driver, SceneManager->getFileSystem());
            s_fluidMaterialType = (video::E_MATERIAL_TYPE)driver->addMaterialRenderer(s_fluidMaterialRenderer);
            s_fluidInitialized = true;
            
            DP(("DEBUG: VkFluid material renderer initialized with type %d\n", s_fluidMaterialType));
        }
    }
    
    // Create GPU buffers for particle data
    createVulkanBuffers();
}

void SnVkFluid::initializeVolumeTextures()
{
    createVolumeTextures();
    m_volumeTexturesNeedUpdate = true;
}

void SnVkFluid::createVolumeTextures()
{
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;
    
    const u32 resX = m_volumeParams.volumeResX;
    const u32 resY = m_volumeParams.volumeResY;
    const u32 resZ = m_volumeParams.volumeResZ;
    
    DP(("DEBUG: Creating 3D volume textures %ux%ux%u\n", resX, resY, resZ));
    
    // Release existing textures
    if (m_densityTexture3D) m_densityTexture3D->drop();
    if (m_velocityTexture3D) m_velocityTexture3D->drop();
    if (m_distanceTexture3D) m_distanceTexture3D->drop();
    if (m_covarianceTexture3D) m_covarianceTexture3D->drop();
    if (m_normalTexture3D) m_normalTexture3D->drop();
    if (m_temperatureTexture3D) m_temperatureTexture3D->drop();
    
    // Create 3D volume textures with proper 3D dimensions and storage usage for compute shaders
    const core::vector3d<u32> volumeSize(resX, resY, resZ);
    
    m_densityTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidDensity3D<STO>", video::ECF_R16F);
    m_velocityTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidVelocity3D<STO>", video::ECF_G16R16F);
    m_distanceTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidDistance3D<STO>", video::ECF_R16F);
    m_covarianceTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidCovariance3D<STO>", video::ECF_A16B16G16R16F);
    m_normalTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidNormal3D<STO>", video::ECF_G16R16F);
    m_temperatureTexture3D = driver->addRenderTargetTexture3D(
        volumeSize, "FluidTemperature3D<STO>", video::ECF_R16F);
}

void SnVkFluid::extractPhysXData()
{
    // Extract particle data from PhysX following SnPhyFluid::updateMesh() pattern
    PxParticleAndDiffuseBuffer* userBuffer = gFluidBuffer;
    if (!userBuffer) {
        DP(("DEBUG: No PhysX fluid buffer available\n"));
        return;
    }

    PxVec4* positions = userBuffer->getPositionInvMasses();
    PxVec4* velocities = userBuffer->getVelocities();
    const PxU32 numParticles = userBuffer->getNbActiveParticles();

    if (!positions || numParticles == 0) {
        DP(("DEBUG: No PhysX particle data - pos:%p count:%u\n", positions, numParticles));
        return;
    }

    // Get CUDA context for memory copying (following SnPhyFluid pattern)
    PxScene* scene;
    PxGetPhysics().getScenes(&scene, 1);
    PxCudaContextManager* cudaContextManager = scene->getCudaContextManager();
    if (!cudaContextManager) {
        DP(("DEBUG: No CUDA context manager available\n"));
        return;
    }

    cudaContextManager->acquireContext();
    PxCudaContext* cudaContext = cudaContextManager->getCudaContext();
    if (!cudaContext) {
        DP(("DEBUG: No CUDA context available\n"));
        return;
    }

    // Allocate temporary buffers for CUDA memory copy
    if (!ptcBuf || maxParticles < numParticles) {
        if (ptcBuf) delete[] ptcBuf;
        ptcBuf = new float4[numParticles];
        maxParticles = numParticles;
    }
    
    if (!velBuf) {
        velBuf = new float4[numParticles];
    }

    // Copy position data from GPU to CPU (same as SnPhyFluid::updateMesh)
    cudaContext->memcpyDtoH(ptcBuf, CUdeviceptr(positions), sizeof(PxVec4) * numParticles);
    
    // Copy velocity data from GPU to CPU if available
    if (velocities) {
        cudaContext->memcpyDtoH(velBuf, CUdeviceptr(velocities), sizeof(PxVec4) * numParticles);
    }

    // Resize particle data array
    m_particleData.set_used(numParticles);

    // Copy PhysX data to our format
    for (PxU32 i = 0; i < numParticles; ++i) {
        FluidParticle& particle = m_particleData[i];
        
        // Position and mass (from CUDA copied data)
        particle.positionMass.x = ptcBuf[i].x;
        particle.positionMass.y = ptcBuf[i].y;
        particle.positionMass.z = ptcBuf[i].z;
        particle.positionMass.w = ptcBuf[i].w; // 1/mass
        
        // Velocity and density
        if (velocities && velBuf) {
            particle.velocity.x = velBuf[i].x;
            particle.velocity.y = velBuf[i].y;
            particle.velocity.z = velBuf[i].z;
            particle.velocity.w = velBuf[i].w; // Density or other data
        } else {
            particle.velocity = float4(0, 0, 0, 1.0f); // Default values
        }
        
        // Force and pressure (computed later by compute shaders)
        particle.force = float4(0, 0, 0, 0);
        particle.extra = float4(0, 0, 0, 0);
    }

    DP(("DEBUG: Extracted %u particles from PhysX for volume rendering\n", numParticles));
    
    // Update particle count in material renderer
    if (s_fluidMaterialRenderer) {
        // Update volume uniforms with new particle count
        updateVulkanBuffers();
    }
}

void SnVkFluid::uploadParticleData()
{
    if (m_particleData.size() == 0 || !m_particleBuffer) {
        DP(("DEBUG: No particle data to upload - count:%u buffer:%p\n", (u32)m_particleData.size(), m_particleBuffer));
        return;
    }

    // Upload particle data to GPU buffer
    video::VkHardwareBuffer* vkBuffer = static_cast<video::VkHardwareBuffer*>(m_particleBuffer);
    if (vkBuffer) {
        void* data = vkBuffer->lock(video::ETLM_READ_WRITE, 0, m_particleData.size() * sizeof(FluidParticle));
        if (data) {
            memcpy(data, m_particleData.pointer(), m_particleData.size() * sizeof(FluidParticle));
            vkBuffer->unlock();
            DP(("DEBUG: Uploaded %u particles to GPU buffer\n", (u32)m_particleData.size()));
        }
    }
}

void SnVkFluid::dispatchVolumeComputeShaders()
{
    if (s_fluidMaterialRenderer) {
        // Generate 3D volume textures from particle data
        generateDensityField3D();
        generateVelocityField3D();
        generateDistanceField3D();
        computeCovariance3D();
    }
}

void SnVkFluid::createVulkanBuffers()
{
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;

    const u32 maxParticleCount = 100000; // Maximum expected particles

    // Create particle buffer for PhysX data
    if (!m_particleBuffer) {
        m_particleBuffer = driver->createHardwareBuffer(
            
            video::EHBT_STORAGE, video::EHBA_DEFAULT, maxParticleCount * sizeof(FluidParticle) );
        DP(("DEBUG: Created particle buffer for %u particles\n", maxParticleCount));
    }

    // Create covariance buffer for anisotropic shapes
    if (!m_covarianceBuffer) {
        m_covarianceBuffer = driver->createHardwareBuffer(
            video::EHBT_STORAGE, video::EHBA_DEFAULT, maxParticleCount * sizeof(ParticleCovariance));
        DP(("DEBUG: Created covariance buffer for %u particles\n", maxParticleCount));
    }

    // Create diffuse particle buffer for foam/bubbles
    if (!m_diffuseBuffer) {
        m_diffuseBuffer = driver->createHardwareBuffer(
            video::EHBT_STORAGE, video::EHBA_DEFAULT, maxParticleCount * sizeof(FluidParticle));
        DP(("DEBUG: Created diffuse particle buffer for %u particles\n", maxParticleCount));
    }
}

void SnVkFluid::updateVulkanBuffers()
{
    if (s_fluidMaterialRenderer) {
        // Update volume rendering parameters
        video::VkFluidVolumeUniforms volumeUniforms;
        
        // Volume bounds
        volumeUniforms.volumeMin = m_volumeParams.volumeMin;
        volumeUniforms.volumeMax = m_volumeParams.volumeMax;
        volumeUniforms.volumeSize = m_volumeParams.volumeMax - m_volumeParams.volumeMin;
        
        // Raymarching parameters
        volumeUniforms.rayStepSize = m_volumeParams.rayStepSize;
        volumeUniforms.maxRaymarchSteps = m_volumeParams.maxRaymarchSteps;
        volumeUniforms.densityThreshold = m_volumeParams.densityThreshold;
        volumeUniforms.noiseScale = m_volumeParams.volumeNoiseScale;
        
        // Time for animation
        volumeUniforms.time = gFrameTime;
        
        // Particle count
        volumeUniforms.numParticles = static_cast<u32>(m_particleData.size());
        
        // Camera matrices
        video::IVideoDriver* driver = SceneManager->getVideoDriver();
        if (driver) {
            core::matrix4 view = driver->getTransform(video::ETS_VIEW);
            core::matrix4 proj = driver->getTransform(video::ETS_PROJECTION);
            core::matrix4 viewProj = proj * view;
            viewProj.getInverse(volumeUniforms.mViewProjectionInverse);
            
            core::matrix4 viewInverse;
            view.getInverse(viewInverse);
            volumeUniforms.cameraPosition = viewInverse.getTranslation();
        }
        
        s_fluidMaterialRenderer->updateVolumeUniforms(volumeUniforms);
    }
}

// ShaderToy rendering method using VkDriver::draw2DImageMr pattern
void SnVkFluid::renderFullScreenQuad()
{
    if (!s_fluidMaterialRenderer) {
        DP(("DEBUG: Cannot render full-screen quad - no material renderer\n"));
        return;
    }
    
    DP(("DEBUG: Rendering full-screen quad for volume raymarching using draw2DImageMr\n"));
    
    // Set raymarching pipeline mode
    s_fluidMaterialRenderer->setRenderPipeline(video::MrVkFluid::FRP_RAYMARCHING);
    
    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) return;
    
    // Get screen dimensions for full-screen rendering
    const core::dimension2d<u32>& screenSize = driver->getCurrentRenderTargetSize();
    
    // Create material for fluid ShaderToy rendering (following VkMr2D pattern)
    video::SMaterial fluidMaterial;
    fluidMaterial.MaterialType = s_fluidMaterialType;
    fluidMaterial.Lighting = false;
    fluidMaterial.BackfaceCulling = false;
    fluidMaterial.setFlag(video::EMF_BLEND_OPERATION, true);
    
    // Set 3D textures for fluid data (matching descriptor set bindings)
    fluidMaterial.setTexture(0, m_densityTexture3D);     
    fluidMaterial.setTexture(1, m_velocityTexture3D);    
    fluidMaterial.setTexture(2, m_distanceTexture3D);    
    fluidMaterial.setTexture(3, m_covarianceTexture3D);  
    
    // Use VkDriver::draw2DImageMr for material-aware rendering (like VkMr2D)
    driver->draw2DImageMr(
        fluidMaterial,
        core::rect<s32>(0, 0, screenSize.Width, screenSize.Height),  // Full screen dest
        core::rect<s32>(0, 0, screenSize.Width, screenSize.Height),  // Full screen source
        nullptr,  // No clip
        nullptr,  // Default colors
        false     // No Y inversion
    );
}

} // namespace scene
} // namespace irr 